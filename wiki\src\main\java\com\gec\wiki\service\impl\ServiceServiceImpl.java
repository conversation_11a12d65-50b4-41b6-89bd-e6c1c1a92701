package com.gec.wiki.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gec.wiki.mapper.ServiceMapper;
import com.gec.wiki.pojo.Category;
import com.gec.wiki.pojo.Service;
import com.gec.wiki.pojo.req.ServiceQueryReq;
import com.gec.wiki.pojo.resp.PageResp;
import com.gec.wiki.pojo.resp.ServiceQueryResp;
import com.gec.wiki.service.CategoryService;
import com.gec.wiki.service.ServiceService;
import com.gec.wiki.utils.CopyUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 服务Service实现类
 */
@org.springframework.stereotype.Service
public class ServiceServiceImpl extends ServiceImpl<ServiceMapper, Service> implements ServiceService {

    private static final Logger LOG = LoggerFactory.getLogger(ServiceServiceImpl.class);

    @Autowired
    private CategoryService categoryService;

    @Override
    public PageResp<ServiceQueryResp> getServiceListByPage(ServiceQueryReq req) {
        LOG.info("🔍 构建服务查询条件：{}", req);
        
        // 构建查询条件
        QueryWrapper<Service> wrapper = new QueryWrapper<>();
        
        // 按服务名称模糊查询
        if (StringUtils.hasText(req.getName())) {
            LOG.info("📝 添加服务名称模糊查询条件：{}", req.getName());
            wrapper.like("name", req.getName());
        }
        
        // 按分类查询
        if (!ObjectUtils.isEmpty(req.getCategory1Id())) {
            LOG.info("🏷️ 添加一级分类查询条件：{}", req.getCategory1Id());
            wrapper.eq("category1_id", req.getCategory1Id());
        }
        if (!ObjectUtils.isEmpty(req.getCategory2Id())) {
            LOG.info("🏷️ 添加二级分类查询条件：{}", req.getCategory2Id());
            wrapper.eq("category2_id", req.getCategory2Id());
        }
        
        // 按状态查询
        if (!ObjectUtils.isEmpty(req.getStatus())) {
            LOG.info("⚡ 添加状态查询条件：{}", req.getStatus());
            wrapper.eq("status", req.getStatus());
        }
        
        // 按推荐状态查询
        if (!ObjectUtils.isEmpty(req.getIsRecommend())) {
            LOG.info("⭐ 添加推荐状态查询条件：{}", req.getIsRecommend());
            wrapper.eq("is_recommend", req.getIsRecommend());
        }
        
        // 按创建时间降序排列
        wrapper.orderByDesc("create_time");
        
        // 分页查询
        Page<Service> page = new Page<>(req.getPage(), req.getSize());
        LOG.info("🔢 执行分页查询：页码={}, 页大小={}", req.getPage(), req.getSize());
        page = this.page(page, wrapper);
        
        List<Service> serviceList = page.getRecords();
        LOG.info("📋 数据库查询结果：共 {} 条记录，当前页 {} 条", page.getTotal(), serviceList.size());
        
        List<ServiceQueryResp> serviceQueryRespList = CopyUtil.copyList(serviceList, ServiceQueryResp.class);
        
        // 设置分类名称
        for (ServiceQueryResp resp : serviceQueryRespList) {
            if (!ObjectUtils.isEmpty(resp.getCategory2Id())) {
                String categoryName = getCategoryName(resp.getCategory2Id());
                resp.setCategoryName(categoryName);
            }
        }
        
        PageResp<ServiceQueryResp> pageResp = new PageResp<>();
        pageResp.setTotal(page.getTotal());
        pageResp.setList(serviceQueryRespList);
        
        LOG.info("✅ 服务查询完成，返回 {} 条记录", serviceQueryRespList.size());
        return pageResp;
    }

    @Override
    public List<ServiceQueryResp> getAllServiceList(ServiceQueryReq req) {
        QueryWrapper<Service> wrapper = new QueryWrapper<>();
        
        if (StringUtils.hasText(req.getName())) {
            wrapper.like("name", req.getName());
        }
        
        if (!ObjectUtils.isEmpty(req.getCategory1Id())) {
            wrapper.eq("category1_id", req.getCategory1Id());
        }
        if (!ObjectUtils.isEmpty(req.getCategory2Id())) {
            wrapper.eq("category2_id", req.getCategory2Id());
        }
        
        if (!ObjectUtils.isEmpty(req.getStatus())) {
            wrapper.eq("status", req.getStatus());
        }
        
        wrapper.orderByDesc("create_time");
        
        List<Service> serviceList = this.list(wrapper);
        List<ServiceQueryResp> serviceQueryRespList = CopyUtil.copyList(serviceList, ServiceQueryResp.class);
        
        // 设置分类名称
        for (ServiceQueryResp resp : serviceQueryRespList) {
            if (!ObjectUtils.isEmpty(resp.getCategory2Id())) {
                String categoryName = getCategoryName(resp.getCategory2Id());
                resp.setCategoryName(categoryName);
            }
        }
        
        return serviceQueryRespList;
    }

    @Override
    public ServiceQueryResp getServiceById(Long id) {
        Service service = this.getById(id);
        if (service == null) {
            return null;
        }
        
        ServiceQueryResp resp = CopyUtil.copy(service, ServiceQueryResp.class);
        
        // 设置分类名称
        if (!ObjectUtils.isEmpty(resp.getCategory2Id())) {
            String categoryName = getCategoryName(resp.getCategory2Id());
            resp.setCategoryName(categoryName);
        }
        
        return resp;
    }

    @Override
    public boolean saveOrUpdateService(Service service) {
        try {
            LOG.info("💾 开始保存服务：{}", service);
            
            if (ObjectUtils.isEmpty(service.getId())) {
                // 新增服务
                LOG.info("➕ 新增服务，设置默认值");
                service.setCreateTime(LocalDateTime.now());
                service.setUpdateTime(LocalDateTime.now());
                service.setBookingCount(0);
                service.setCompleteCount(0);
                service.setRatingCount(0);
                service.setRatingScore(new java.math.BigDecimal("5.0"));
                if (ObjectUtils.isEmpty(service.getStatus())) {
                    service.setStatus(1); // 默认上架
                }
                if (ObjectUtils.isEmpty(service.getIsRecommend())) {
                    service.setIsRecommend(0); // 默认不推荐
                }
                
                LOG.info("🔧 准备保存的服务数据：{}", service);
                boolean result = this.save(service);
                LOG.info("💾 服务保存结果：{}, 生成的ID：{}", result, service.getId());
                return result;
            } else {
                // 更新服务 - 保留原有的统计数据
                LOG.info("✏️ 更新服务，ID：{}", service.getId());
                Service existingService = this.getById(service.getId());
                if (existingService != null) {
                    LOG.info("📋 找到现有服务：{}", existingService);
                    service.setBookingCount(existingService.getBookingCount());
                    service.setCompleteCount(existingService.getCompleteCount());
                    service.setRatingCount(existingService.getRatingCount());
                    service.setRatingScore(existingService.getRatingScore());
                    service.setCreateTime(existingService.getCreateTime());
                } else {
                    LOG.warn("⚠️ 未找到ID为 {} 的现有服务", service.getId());
                }
                service.setUpdateTime(LocalDateTime.now());
                
                LOG.info("🔧 准备更新的服务数据：{}", service);
                boolean result = this.updateById(service);
                LOG.info("✏️ 服务更新结果：{}", result);
                return result;
            }
        } catch (Exception e) {
            LOG.error("💥 保存服务失败，服务数据：{}", service, e);
            return false;
        }
    }

    @Override
    public boolean batchUpdateStatus(List<Long> ids, Integer status) {
        try {
            QueryWrapper<Service> wrapper = new QueryWrapper<>();
            wrapper.in("id", ids);
            
            Service service = new Service();
            service.setStatus(status);
            service.setUpdateTime(LocalDateTime.now());
            
            return this.update(service, wrapper);
        } catch (Exception e) {
            LOG.error("批量更新服务状态失败", e);
            return false;
        }
    }

    @Override
    public boolean batchDeleteServices(List<Long> ids) {
        try {
            return this.removeByIds(ids);
        } catch (Exception e) {
            LOG.error("批量删除服务失败", e);
            return false;
        }
    }

    @Override
    public String uploadServiceImage(MultipartFile file) {
        try {
            if (file == null || file.isEmpty()) {
                throw new RuntimeException("文件不能为空");
            }
            
            // 检查文件类型
            String originalFilename = file.getOriginalFilename();
            if (!isValidImageFile(originalFilename)) {
                throw new RuntimeException("不支持的文件类型，只支持 jpg、jpeg、png、gif 格式");
            }
            
            // 检查文件大小（2MB限制）
            if (file.getSize() > 2 * 1024 * 1024) {
                throw new RuntimeException("文件大小不能超过2MB");
            }
            
            // 生成文件名
            String fileName = UUID.randomUUID().toString() + "_" + originalFilename.replaceAll("[^a-zA-Z0-9._-]", "");
            
            // 创建上传目录 - 使用绝对路径
            String projectPath = System.getProperty("user.dir");
            String uploadDir = projectPath + File.separator + "web" + File.separator + "public" + File.separator + "image" + File.separator;
            
            LOG.info("📁 上传目录：{}", uploadDir);
            LOG.info("📄 文件名：{}", fileName);
            
            File uploadDirFile = new File(uploadDir);
            if (!uploadDirFile.exists()) {
                boolean created = uploadDirFile.mkdirs();
                LOG.info("📁 创建上传目录结果：{}", created);
            }
            
            // 保存文件
            File targetFile = new File(uploadDir + fileName);
            LOG.info("💾 目标文件路径：{}", targetFile.getAbsolutePath());
            
            file.transferTo(targetFile);
            LOG.info("✅ 文件保存成功");
            
            // 返回文件访问路径
            String imageUrl = "/image/" + fileName;
            LOG.info("🔗 返回图片URL：{}", imageUrl);
            return imageUrl;
            
        } catch (IOException e) {
            LOG.error("💥 上传服务图片IO异常", e);
            throw new RuntimeException("文件保存失败：" + e.getMessage());
        } catch (Exception e) {
            LOG.error("💥 上传服务图片失败", e);
            throw new RuntimeException("文件上传失败：" + e.getMessage());
        }
    }

    /**
     * 获取分类名称
     */
    private String getCategoryName(Long categoryId) {
        try {
            Category category = categoryService.getById(categoryId);
            return category != null ? category.getName() : "未知分类";
        } catch (Exception e) {
            LOG.warn("获取分类名称失败，分类ID：{}", categoryId);
            return "未知分类";
        }
    }

    /**
     * 检查是否为有效的图片文件
     */
    private boolean isValidImageFile(String filename) {
        if (filename == null) {
            return false;
        }
        String extension = getFileExtension(filename).toLowerCase();
        return extension.equals(".jpg") || extension.equals(".jpeg") || extension.equals(".png") || extension.equals(".gif");
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (filename == null || !filename.contains(".")) {
            return "";
        }
        return filename.substring(filename.lastIndexOf("."));
    }

    @Override
    public PageResp<ServiceQueryResp> getShopServiceListByPage(ServiceQueryReq req, Long shopId) {
        LOG.info("🏪 构建维修店服务查询条件：{}, shopId: {}", req, shopId);
        
        // 构建查询条件
        QueryWrapper<Service> wrapper = new QueryWrapper<>();
        
        // 限制查询维修店的服务
        wrapper.eq("shop_id", shopId);
        
        // 按服务名称模糊查询
        if (StringUtils.hasText(req.getName())) {
            LOG.info("📝 添加服务名称模糊查询条件：{}", req.getName());
            wrapper.like("name", req.getName());
        }
        
        // 按分类查询
        if (!ObjectUtils.isEmpty(req.getCategory1Id())) {
            LOG.info("🏷️ 添加一级分类查询条件：{}", req.getCategory1Id());
            wrapper.eq("category1_id", req.getCategory1Id());
        }
        if (!ObjectUtils.isEmpty(req.getCategory2Id())) {
            LOG.info("🏷️ 添加二级分类查询条件：{}", req.getCategory2Id());
            wrapper.eq("category2_id", req.getCategory2Id());
        }
        
        // 按状态查询
        if (!ObjectUtils.isEmpty(req.getStatus())) {
            LOG.info("⚡ 添加状态查询条件：{}", req.getStatus());
            wrapper.eq("status", req.getStatus());
        }
        
        // 按推荐状态查询
        if (!ObjectUtils.isEmpty(req.getIsRecommend())) {
            LOG.info("⭐ 添加推荐状态查询条件：{}", req.getIsRecommend());
            wrapper.eq("is_recommend", req.getIsRecommend());
        }
        
        // 按创建时间降序排列
        wrapper.orderByDesc("create_time");
        
        // 分页查询
        Page<Service> page = new Page<>(req.getPage(), req.getSize());
        LOG.info("🔢 执行维修店服务分页查询：页码={}, 页大小={}", req.getPage(), req.getSize());
        page = this.page(page, wrapper);
        
        List<Service> serviceList = page.getRecords();
        LOG.info("📋 维修店 {} 查询结果：共 {} 条记录，当前页 {} 条", shopId, page.getTotal(), serviceList.size());
        
        List<ServiceQueryResp> serviceQueryRespList = CopyUtil.copyList(serviceList, ServiceQueryResp.class);
        
        // 设置分类名称
        for (ServiceQueryResp resp : serviceQueryRespList) {
            if (!ObjectUtils.isEmpty(resp.getCategory2Id())) {
                String categoryName = getCategoryName(resp.getCategory2Id());
                resp.setCategoryName(categoryName);
            }
        }
        
        PageResp<ServiceQueryResp> pageResp = new PageResp<>();
        pageResp.setTotal(page.getTotal());
        pageResp.setList(serviceQueryRespList);
        
        LOG.info("✅ 维修店 {} 服务查询完成，返回 {} 条记录", shopId, serviceQueryRespList.size());
        return pageResp;
    }

    @Override
    public List<ServiceQueryResp> getShopAllServiceList(ServiceQueryReq req, Long shopId) {
        LOG.info("🏪 查询维修店所有服务：shopId={}", shopId);
        
        QueryWrapper<Service> wrapper = new QueryWrapper<>();
        
        // 限制查询维修店的服务
        wrapper.eq("shop_id", shopId);
        
        if (StringUtils.hasText(req.getName())) {
            wrapper.like("name", req.getName());
        }
        
        if (!ObjectUtils.isEmpty(req.getCategory1Id())) {
            wrapper.eq("category1_id", req.getCategory1Id());
        }
        if (!ObjectUtils.isEmpty(req.getCategory2Id())) {
            wrapper.eq("category2_id", req.getCategory2Id());
        }
        
        if (!ObjectUtils.isEmpty(req.getStatus())) {
            wrapper.eq("status", req.getStatus());
        }
        
        wrapper.orderByDesc("create_time");
        
        List<Service> serviceList = this.list(wrapper);
        List<ServiceQueryResp> serviceQueryRespList = CopyUtil.copyList(serviceList, ServiceQueryResp.class);
        
        // 设置分类名称
        for (ServiceQueryResp resp : serviceQueryRespList) {
            if (!ObjectUtils.isEmpty(resp.getCategory2Id())) {
                String categoryName = getCategoryName(resp.getCategory2Id());
                resp.setCategoryName(categoryName);
            }
        }
        
        LOG.info("✅ 维修店 {} 查询到 {} 条服务", shopId, serviceQueryRespList.size());
        return serviceQueryRespList;
    }
}
