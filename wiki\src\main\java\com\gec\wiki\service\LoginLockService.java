package com.gec.wiki.service;

import com.gec.wiki.pojo.UserLoginLock;

/**
 * 登录锁定服务接口
 */
public interface LoginLockService {
    
    /**
     * 检查用户是否被锁定
     * @param username 用户名
     * @param ipAddress IP地址
     * @return 锁定信息，如果未锁定返回null
     */
    UserLoginLock checkUserLocked(String username, String ipAddress);
    
    /**
     * 记录登录失败
     * @param username 用户名
     * @param ipAddress IP地址
     * @param userId 用户ID（可为null）
     * @return 失败次数
     */
    int recordLoginFailure(String username, String ipAddress, Long userId);
    
    /**
     * 清除登录失败记录
     * @param username 用户名
     * @param ipAddress IP地址
     */
    void clearLoginFailure(String username, String ipAddress);
    
    /**
     * 获取最大登录失败次数配置
     * @return 最大失败次数
     */
    int getMaxFailCount();
    
    /**
     * 设置最大登录失败次数
     * @param maxFailCount 最大失败次数
     * @return 是否设置成功
     */
    boolean setMaxFailCount(int maxFailCount);
    
    /**
     * 手动解锁用户
     * @param username 用户名
     * @return 是否解锁成功
     */
    boolean unlockUser(String username);
    
}
