package com.gec.wiki.controller;

import com.gec.wiki.pojo.resp.CommonResp;
import com.gec.wiki.service.LoginLockService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 系统设置控制器
 */
@RestController
@RequestMapping("/admin/settings")
public class SystemSettingsController {

    private static final Logger LOG = LoggerFactory.getLogger(SystemSettingsController.class);

    @Autowired
    private LoginLockService loginLockService;

    /**
     * 获取登录锁定设置
     */
    @GetMapping("/login-lock")
    public CommonResp<Object> getLoginLockSettings() {
        CommonResp<Object> resp = new CommonResp<>();
        try {
            LOG.info("🔍 获取登录锁定设置");
            
            int maxFailCount = loginLockService.getMaxFailCount();
            
            Map<String, Object> settings = new HashMap<>();
            settings.put("maxFailCount", maxFailCount);
            settings.put("lockDurationMinutes", 30); // 固定30分钟
            
            resp.setSuccess(true);
            resp.setMessage("获取设置成功");
            resp.setContent(settings);
            
            LOG.info("✅ 获取登录锁定设置成功：maxFailCount={}", maxFailCount);
            
        } catch (Exception e) {
            LOG.error("❌ 获取登录锁定设置失败", e);
            resp.setSuccess(false);
            resp.setMessage("获取设置失败：" + e.getMessage());
        }
        return resp;
    }

    /**
     * 更新登录锁定设置
     */
    @PostMapping("/login-lock")
    public CommonResp<Object> updateLoginLockSettings(@RequestBody UpdateLockSettingsReq req) {
        CommonResp<Object> resp = new CommonResp<>();
        try {
            LOG.info("⚙️ 更新登录锁定设置：{}", req);
            
            // 参数校验
            if (req.getMaxFailCount() == null || req.getMaxFailCount() < 1 || req.getMaxFailCount() > 100) {
                resp.setSuccess(false);
                resp.setMessage("最大失败次数必须在1-100之间");
                return resp;
            }
            
            boolean success = loginLockService.setMaxFailCount(req.getMaxFailCount());
            
            if (success) {
                resp.setSuccess(true);
                resp.setMessage("设置更新成功");
                LOG.info("✅ 登录锁定设置更新成功：maxFailCount={}", req.getMaxFailCount());
            } else {
                resp.setSuccess(false);
                resp.setMessage("设置更新失败");
                LOG.error("❌ 登录锁定设置更新失败");
            }
            
        } catch (Exception e) {
            LOG.error("❌ 更新登录锁定设置异常", e);
            resp.setSuccess(false);
            resp.setMessage("设置更新失败：" + e.getMessage());
        }
        return resp;
    }

    /**
     * 手动解锁用户
     */
    @PostMapping("/unlock-user")
    public CommonResp<Object> unlockUser(@RequestBody UnlockUserReq req) {
        CommonResp<Object> resp = new CommonResp<>();
        try {
            LOG.info("🔓 手动解锁用户：{}", req.getUsername());
            
            if (req.getUsername() == null || req.getUsername().trim().isEmpty()) {
                resp.setSuccess(false);
                resp.setMessage("用户名不能为空");
                return resp;
            }
            
            boolean success = loginLockService.unlockUser(req.getUsername().trim());
            
            if (success) {
                resp.setSuccess(true);
                resp.setMessage("用户解锁成功");
                LOG.info("✅ 用户解锁成功：{}", req.getUsername());
            } else {
                resp.setSuccess(true); // 即使没有找到锁定记录，也认为是成功的
                resp.setMessage("用户未被锁定或解锁成功");
                LOG.info("ℹ️ 用户未被锁定：{}", req.getUsername());
            }
            
        } catch (Exception e) {
            LOG.error("❌ 解锁用户异常：username={}", req.getUsername(), e);
            resp.setSuccess(false);
            resp.setMessage("解锁失败：" + e.getMessage());
        }
        return resp;
    }

    /**
     * 更新锁定设置请求类
     */
    public static class UpdateLockSettingsReq {
        private Integer maxFailCount;

        public Integer getMaxFailCount() {
            return maxFailCount;
        }

        public void setMaxFailCount(Integer maxFailCount) {
            this.maxFailCount = maxFailCount;
        }

        @Override
        public String toString() {
            return "UpdateLockSettingsReq{" +
                    "maxFailCount=" + maxFailCount +
                    '}';
        }
    }

    /**
     * 解锁用户请求类
     */
    public static class UnlockUserReq {
        private String username;

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        @Override
        public String toString() {
            return "UnlockUserReq{" +
                    "username='" + username + '\'' +
                    '}';
        }
    }
}
