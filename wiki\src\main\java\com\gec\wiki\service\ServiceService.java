package com.gec.wiki.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.gec.wiki.pojo.Service;
import com.gec.wiki.pojo.req.ServiceQueryReq;
import com.gec.wiki.pojo.resp.PageResp;
import com.gec.wiki.pojo.resp.ServiceQueryResp;

import java.util.List;

/**
 * 服务Service接口
 */
public interface ServiceService extends IService<Service> {

    /**
     * 分页查询服务列表
     * @param req 查询请求
     * @return 分页响应
     */
    PageResp<ServiceQueryResp> getServiceListByPage(ServiceQueryReq req);

    /**
     * 获取所有服务列表
     * @param req 查询请求
     * @return 服务列表
     */
    List<ServiceQueryResp> getAllServiceList(ServiceQueryReq req);

    /**
     * 根据ID获取服务详情
     * @param id 服务ID
     * @return 服务详情
     */
    ServiceQueryResp getServiceById(Long id);

    /**
     * 保存或更新服务
     * @param service 服务信息
     * @return 是否成功
     */
    boolean saveOrUpdateService(Service service);

    /**
     * 批量更新服务状态
     * @param ids 服务ID列表
     * @param status 状态
     * @return 是否成功
     */
    boolean batchUpdateStatus(List<Long> ids, Integer status);

    /**
     * 批量删除服务
     * @param ids 服务ID列表
     * @return 是否成功
     */
    boolean batchDeleteServices(List<Long> ids);

    /**
     * 上传服务图片
     * @param file 图片文件
     * @return 图片URL
     */
    String uploadServiceImage(org.springframework.web.multipart.MultipartFile file);

    /**
     * 分页查询维修店服务列表
     * @param req 查询请求
     * @param shopId 维修店ID
     * @return 分页响应
     */
    PageResp<ServiceQueryResp> getShopServiceListByPage(ServiceQueryReq req, Long shopId);

    /**
     * 获取维修店所有服务列表
     * @param req 查询请求
     * @param shopId 维修店ID
     * @return 服务列表
     */
    List<ServiceQueryResp> getShopAllServiceList(ServiceQueryReq req, Long shopId);
}
