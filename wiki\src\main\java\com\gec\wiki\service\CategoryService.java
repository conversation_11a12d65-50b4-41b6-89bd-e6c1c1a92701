package com.gec.wiki.service;

import com.gec.wiki.pojo.Category;
import com.baomidou.mybatisplus.extension.service.IService;
import com.gec.wiki.pojo.req.CategoryQueryReq;
import com.gec.wiki.pojo.resp.CategoryQueryResp;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【category】的数据库操作Service
* @createDate 2025-06-18 14:35:21
*/
@Service
public interface CategoryService extends IService<Category> {

    List<Category> getByIds(List<Long> ids);
    
    /**
     * 获取所有分类列表（平铺）
     */
    List<CategoryQueryResp> allList(CategoryQueryReq req);
    
    /**
     * 获取树形结构的分类列表
     */
    List<CategoryQueryResp> getTreeList();
    
    /**
     * 根据父级ID获取子分类列表
     */
    List<CategoryQueryResp> getChildrenByParentId(Long parentId);
    
    /**
     * 删除分类（级联删除子分类）
     */
    boolean deleteCategoryById(Long id);
    
    /**
     * 检查分类是否有子分类
     */
    boolean hasChildren(Long id);
    
    /**
     * 检查分类名称是否已存在（在同一父级下）
     */
    boolean isNameExists(String name, Long parentId, Long excludeId);


}
