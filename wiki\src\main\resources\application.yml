spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************************************************
    username: root
    password: 123456
    schema: classpath:sql/schema.sql  # 自动执行数据库初始化脚本
    initialization-mode: always      # 每次启动都检查并执行脚本
  
  # 邮件配置
  mail:
    host: smtp.qq.com  # QQ邮箱SMTP服务器
    port: 587
    username: <EMAIL>  # 发送方邮箱
    password: xnmnpjxqwqevibgi  # QQ邮箱授权码（16位字符串）
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
  
  # Redis配置
  redis:
    host: localhost
    port: 6379
    database: 0
    timeout: 10000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
server:
  port: 8880
mybatis-plus:
  global-config:
    db-config:
      id-type: auto
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl