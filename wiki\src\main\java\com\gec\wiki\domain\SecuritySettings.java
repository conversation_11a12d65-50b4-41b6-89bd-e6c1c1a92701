package com.gec.wiki.domain;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.time.LocalDateTime;

/**
 * 安全设置实体类
 */
public class SecuritySettings {
    
    private Long id;
    
    /**
     * 最大登录失败次数
     */
    private Integer maxLoginFailCount;
    
    /**
     * 锁定持续时间（分钟）
     */
    private Integer lockDurationMinutes;
    
    /**
     * 密码最小长度
     */
    private Integer minPasswordLength;
    
    /**
     * 是否要求复杂密码
     */
    private Boolean requireComplexPassword;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;

    public SecuritySettings() {
    }

    public SecuritySettings(Integer maxLoginFailCount, Integer lockDurationMinutes, 
                          Integer minPasswordLength, Boolean requireComplexPassword) {
        this.maxLoginFailCount = maxLoginFailCount;
        this.lockDurationMinutes = lockDurationMinutes;
        this.minPasswordLength = minPasswordLength;
        this.requireComplexPassword = requireComplexPassword;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getMaxLoginFailCount() {
        return maxLoginFailCount;
    }

    public void setMaxLoginFailCount(Integer maxLoginFailCount) {
        this.maxLoginFailCount = maxLoginFailCount;
    }

    public Integer getLockDurationMinutes() {
        return lockDurationMinutes;
    }

    public void setLockDurationMinutes(Integer lockDurationMinutes) {
        this.lockDurationMinutes = lockDurationMinutes;
    }

    public Integer getMinPasswordLength() {
        return minPasswordLength;
    }

    public void setMinPasswordLength(Integer minPasswordLength) {
        this.minPasswordLength = minPasswordLength;
    }

    public Boolean getRequireComplexPassword() {
        return requireComplexPassword;
    }

    public void setRequireComplexPassword(Boolean requireComplexPassword) {
        this.requireComplexPassword = requireComplexPassword;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "SecuritySettings{" +
                "id=" + id +
                ", maxLoginFailCount=" + maxLoginFailCount +
                ", lockDurationMinutes=" + lockDurationMinutes +
                ", minPasswordLength=" + minPasswordLength +
                ", requireComplexPassword=" + requireComplexPassword +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
