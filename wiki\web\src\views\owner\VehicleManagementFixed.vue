<template>
  <div class="vehicle-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <CarOutlined />
          我的车辆
        </h1>
        <p class="page-description">管理您的车辆信息，让维修更便捷</p>
      </div>
      <div class="header-actions">
        <a-button 
          type="primary" 
          size="large" 
          @click="handleAdd"
          data-test="add-vehicle-btn"
          :loading="saving"
        >
          <PlusOutlined />
          添加车辆
        </a-button>
        
      </div>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section">
      <a-row :gutter="16">
        <a-col :xs="24" :sm="8">
          <a-input
            v-model:value="searchParams.licensePlate"
            placeholder="搜索车牌号"
            allow-clear
            @press-enter="handleSearch"
          >
            <template #prefix>
              <SearchOutlined />
            </template>
          </a-input>
        </a-col>
        <a-col :xs="24" :sm="8">
          <a-input
            v-model:value="searchParams.brand"
            placeholder="搜索品牌"
            allow-clear
            @press-enter="handleSearch"
          >
            <template #prefix>
              <SearchOutlined />
            </template>
          </a-input>
        </a-col>
        <a-col :xs="24" :sm="8">
          <a-button type="primary" @click="handleSearch">
            <SearchOutlined />
            搜索
          </a-button>
          <a-button @click="handleReset" style="margin-left: 8px">
            重置
          </a-button>
        </a-col>
      </a-row>
    </div>

    <!-- 车辆列表 -->
    <div class="vehicle-list-section">
      <!-- 移动端卡片视图 -->
      <div class="mobile-view" v-if="isMobile">
        <div v-if="loading" class="loading-container">
          <a-spin size="large" tip="加载中..." />
        </div>
        <div v-else-if="vehicles.length === 0" class="empty-state">
          <a-empty description="暂无车辆信息">
            <a-button type="primary" @click="handleAdd">添加第一辆车</a-button>
          </a-empty>
        </div>
        <div v-else class="vehicle-cards">
          <div
            v-for="vehicle in vehicles"
            :key="vehicle.id"
            class="vehicle-card"
            :class="{ 'default-vehicle': vehicle.isDefault }"
          >
            <div class="card-header">
              <div class="license-plate">
                <span class="plate-text">{{ vehicle.licensePlate }}</span>
                <a-tag v-if="vehicle.isDefault" color="gold" class="default-tag">
                  <StarFilled />
                  默认
                </a-tag>
              </div>
              <div class="card-actions">
                <a-dropdown>
                  <a-button type="text" size="small">
                    <MoreOutlined />
                  </a-button>
                  <template #overlay>
                    <a-menu>
                      <a-menu-item key="edit" @click="handleEdit(vehicle)">
                        <EditOutlined />
                        编辑
                      </a-menu-item>
                      <a-menu-item key="default" @click="setAsDefault(vehicle)" v-if="!vehicle.isDefault">
                        <StarOutlined />
                        设为默认
                      </a-menu-item>
                      <a-menu-divider />
                      <a-menu-item key="delete" @click="handleDelete(vehicle)" class="danger-item">
                        <DeleteOutlined />
                        删除
                      </a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>
              </div>
            </div>
            
            <div class="card-content">
              <div class="vehicle-info">
                <div class="info-row">
                  <span class="label">品牌型号：</span>
                  <span class="value">{{ vehicle.brand }} {{ vehicle.model }}</span>
                </div>
                <div class="info-row" v-if="vehicle.color">
                  <span class="label">车辆颜色：</span>
                  <span class="value">{{ vehicle.color }}</span>
                </div>
                <div class="info-row" v-if="vehicle.year">
                  <span class="label">购买年份：</span>
                  <span class="value">{{ vehicle.year }}年</span>
                </div>
                <div class="info-row">
                  <span class="label">行驶里程：</span>
                  <span class="value">{{ formatMileage(vehicle.mileage) }}</span>
                </div>
              </div>
            </div>

            <div class="card-footer">
              <div class="vehicle-status">
                <a-tag :color="vehicle.status === 1 ? 'green' : 'red'">
                  {{ vehicle.status === 1 ? '正常使用' : '停用' }}
                </a-tag>
              </div>
              <div class="create-time">
                添加于: {{ formatDate(vehicle.createTime) }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 桌面端表格视图 -->
      <div class="desktop-view" v-else>
        <a-table
          :columns="columns"
          :data-source="vehicles"
          :loading="loading"
          :pagination="paginationConfig"
          @change="handleTableChange"
          row-key="id"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'licensePlate'">
              <div class="license-plate-cell">
                <span class="plate-text">{{ record.licensePlate }}</span>
                <a-tag v-if="record.isDefault" color="gold" size="small">
                  <StarFilled />
                  默认
                </a-tag>
              </div>
            </template>

            <template v-if="column.dataIndex === 'vehicleInfo'">
              <div class="vehicle-info-cell">
                <div class="brand-model">{{ record.brand }} {{ record.model }}</div>
                <div class="details" v-if="record.color || record.year">
                  {{ record.color }}{{ record.color && record.year ? ' | ' : '' }}{{ record.year }}年
                </div>
              </div>
            </template>

            <template v-if="column.dataIndex === 'mileage'">
              {{ formatMileage(record.mileage) }}
            </template>

            <template v-if="column.dataIndex === 'status'">
              <a-tag :color="record.status === 1 ? 'green' : 'red'">
                {{ record.status === 1 ? '正常使用' : '停用' }}
              </a-tag>
            </template>

            <template v-if="column.dataIndex === 'createTime'">
              {{ formatDate(record.createTime) }}
            </template>

            <template v-if="column.dataIndex === 'operation'">
              <div class="operation-buttons">
                <a-button type="link" size="small" @click="handleEdit(record)">
                  <EditOutlined />
                  编辑
                </a-button>
                <a-button
                  v-if="!record.isDefault"
                  type="link"
                  size="small"
                  @click="setAsDefault(record)"
                >
                  <StarOutlined />
                  设为默认
                </a-button>
                <a-popconfirm
                  title="确定要删除这辆车吗？"
                  ok-text="确定"
                  cancel-text="取消"
                  @confirm="confirmDelete(record)"
                >
                  <a-button type="link" size="small" danger>
                    <DeleteOutlined />
                    删除
                  </a-button>
                </a-popconfirm>
              </div>
            </template>
          </template>
        </a-table>
      </div>
    </div>

    <!-- 原生HTML模态框 - 替代Ant Design -->
    <div v-if="showModal" class="custom-modal-overlay" @click="handleCancel">
      <div class="custom-modal" @click.stop>
        <div class="modal-header">
          <h3>{{ modalTitle }}</h3>
          <button class="close-btn" @click="handleCancel">×</button>
        </div>
        <div class="modal-body" @click="handleModalBodyClick">
      <a-form
        ref="formRef"
        :model="currentVehicle"
        :rules="formRules"
        layout="vertical"
        class="vehicle-form"
      >
        <a-row :gutter="16">
          <a-col :xs="24" :sm="12">
            <a-form-item label="车牌号" name="licensePlate" required>
              <a-input
                v-model:value="currentVehicle.licensePlate"
                placeholder="请输入车牌号"
                class="license-input"
              />
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="12">
            <a-form-item label="车辆品牌" name="brand" required>
              <div class="custom-input-wrapper">
                <input 
                  v-model="currentVehicle.brand"
                  list="brand-options"
                  class="custom-input-select"
                  placeholder="请输入或选择品牌"
                  autocomplete="off"
                  @focus="showBrandOptions = true"
                  @blur="handleBrandBlur"
                  @input="filterBrandOptions"
                >
                <datalist id="brand-options">
                  <option v-for="brand in filteredBrands" :key="brand" :value="brand">
                    {{ brand }}
                  </option>
                </datalist>
                <div class="input-icon" @click="toggleBrandDropdown">▼</div>
                <div v-if="showBrandOptions && filteredBrands.length > 0" class="options-dropdown">
                  <div 
                    v-for="brand in filteredBrands" 
                    :key="brand" 
                    class="option-item"
                    @click="selectBrand(brand)"
                  >
                    {{ brand }}
                  </div>
                </div>
              </div>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :xs="24" :sm="12">
            <a-form-item label="车辆型号" name="model">
              <a-input
                v-model:value="currentVehicle.model"
                placeholder="请输入型号"
              />
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="12">
            <a-form-item label="车辆颜色" name="color">
              <div class="custom-input-wrapper">
                <input 
                  v-model="currentVehicle.color"
                  list="color-options"
                  class="custom-input-select"
                  placeholder="请输入或选择颜色"
                  autocomplete="off"
                  @focus="showColorOptions = true"
                  @blur="handleColorBlur"
                  @input="filterColorOptions"
                >
                <datalist id="color-options">
                  <option v-for="color in filteredColors" :key="color" :value="color">
                    {{ color }}
                  </option>
                </datalist>
                <div class="input-icon" @click="toggleColorDropdown">▼</div>
                <div v-if="showColorOptions && filteredColors.length > 0" class="options-dropdown">
                  <div 
                    v-for="color in filteredColors" 
                    :key="color" 
                    class="option-item"
                    @click="selectColor(color)"
                  >
                    {{ color }}
                  </div>
                </div>
              </div>
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :xs="24" :sm="12">
            <a-form-item label="购买年份" name="year">
              <div class="year-picker-container">
                <div class="year-display" @click="toggleYearPicker">
                  <span>{{ currentVehicle.year ? currentVehicle.year + '年' : '请选择年份' }}</span>
                  <span class="year-arrow" :class="{ 'open': showYearPicker }">▼</span>
                </div>
                <div v-if="showYearPicker" class="year-grid-container">
                  <div class="year-grid-header">
                    <span class="year-range-text">{{ minYear }} - {{ maxYear }}</span>
                  </div>
                  <div class="year-grid">
                    <div 
                      v-for="year in yearOptions" 
                      :key="year" 
                      class="year-item"
                      :class="{ 
                        'selected': currentVehicle.year === year,
                        'current': year === currentYear
                      }"
                      @click="selectYear(year)"
                    >
                      {{ year }}
                    </div>
                  </div>
                </div>
              </div>
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="12">
            <a-form-item label="行驶里程(公里)" name="mileage">
              <a-input-number
                v-model:value="currentVehicle.mileage"
                :min="0"
                :max="999999"
                placeholder="请输入里程数"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :xs="24" :sm="12">
            <a-form-item label="发动机号" name="engineNumber">
              <a-input
                v-model:value="currentVehicle.engineNumber"
                placeholder="请输入发动机号"
              />
            </a-form-item>
          </a-col>
          <a-col :xs="24" :sm="12">
            <a-form-item label="车架号(VIN)" name="vin">
              <a-input
                v-model:value="currentVehicle.vin"
                placeholder="请输入车架号"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="24">
            <a-form-item>
              <a-checkbox v-model:checked="currentVehicle.isDefaultChecked">
                设为默认车辆
              </a-checkbox>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
        </div>
        <div class="modal-footer">
          <button class="btn btn-cancel" @click="handleCancel" :disabled="saving">
            取消
          </button>
          <button class="btn btn-primary" @click="handleSave" :disabled="saving">
            <span v-if="saving">保存中...</span>
            <span v-else>确定</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, reactive, onMounted, computed } from 'vue';
import { message } from 'ant-design-vue';
import axios from 'axios';
import dayjs from 'dayjs';
import {
  CarOutlined,
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  StarOutlined,
  StarFilled,
  MoreOutlined
} from '@ant-design/icons-vue';

// VehicleManagementFixed.vue - 车辆管理组件

interface Vehicle {
  id?: number;
  licensePlate: string;
  brand: string;
  model?: string;
  color?: string;
  year?: number;
  engineNumber?: string;
  vin?: string;
  mileage?: number;
  isDefault?: number;
  status?: number;
  userId?: number;
  createTime?: string;
  updateTime?: string;
  isDefaultChecked?: boolean;
}

export default defineComponent({
  name: 'VehicleManagementFixed',
  components: {
    CarOutlined,
    PlusOutlined,
    SearchOutlined,
    EditOutlined,
    DeleteOutlined,
    StarOutlined,
    StarFilled,
    MoreOutlined
  },
  setup() {
    const vehicles = ref<Vehicle[]>([]);
    const loading = ref(false);
    const saving = ref(false);
    const showModal = ref(false);
    const formRef = ref();
    const isMobile = ref(window.innerWidth <= 768);
    const showYearPicker = ref(false);
    const showBrandOptions = ref(false);
    const showColorOptions = ref(false);

    // 搜索参数
    const searchParams = reactive({
      licensePlate: '',
      brand: ''
    });

    // 分页配置
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0
    });

    const paginationConfig = computed(() => ({
      current: pagination.current,
      pageSize: pagination.pageSize,
      total: pagination.total,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total: number) => `共 ${total} 条记录`
    }));

    // 当前编辑的车辆
    const currentVehicle = ref<Vehicle>({
      licensePlate: '',
      brand: '',
      model: '',
      color: '',
        year: undefined,
        engineNumber: '',
        vin: '',
        mileage: 0,
        isDefault: 0,
        status: 1,
        isDefaultChecked: false
    });

    const modalTitle = computed(() => {
      return currentVehicle.value.id ? '编辑车辆' : '添加车辆';
    });

    // 车辆品牌选项
    const carBrands = [
      '奥迪', '宝马', '奔驰', '大众', '丰田', '本田', '日产', '马自达',
      '现代', '起亚', '福特', '雪佛兰', '别克', '凯迪拉克', '吉利',
      '比亚迪', '长城', '奇瑞', '荣威', '名爵', '传祺', '领克',
      '沃尔沃', '捷豹', '路虎', '保时捷', '法拉利', '兰博基尼',
      '玛莎拉蒂', '阿斯顿马丁', '宾利', '劳斯莱斯', '其他'
    ];


    // 车辆颜色选项
    const carColors = [
      '白色', '黑色', '银色', '灰色', '红色', '蓝色', '绿色',
      '黄色', '橙色', '紫色', '棕色', '香槟色', '其他'
    ];
    
    // 过滤后的选项
    const filteredBrands = ref([...carBrands]);
    const filteredColors = ref([...carColors]);

    // 年份相关计算属性
    const currentYear = new Date().getFullYear();
    const minYear = 1990;
    const maxYear = currentYear;
    
    // 年份选项 - 从1990年到当前年份
    const yearOptions = computed(() => {
      const years = [];
      for (let year = maxYear; year >= minYear; year--) {
        years.push(year);
      }
      return years;
    });


    // 表格列配置
    const columns = [
      {
        title: '车牌号',
        dataIndex: 'licensePlate',
        key: 'licensePlate',
        width: 150,
        fixed: 'left'
      },
      {
        title: '车辆信息',
        dataIndex: 'vehicleInfo',
        key: 'vehicleInfo',
        width: 200
      },
      {
        title: '行驶里程',
        dataIndex: 'mileage',
        key: 'mileage',
        width: 120,
        align: 'center'
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        width: 100,
        align: 'center'
      },
      {
        title: '添加时间',
        dataIndex: 'createTime',
        key: 'createTime',
        width: 150
      },
      {
        title: '操作',
        dataIndex: 'operation',
        key: 'operation',
        width: 200,
        fixed: 'right'
      }
    ];

    // 表单验证规则
    const formRules = {
      licensePlate: [
        { required: true, message: '请输入车牌号' },
        { min: 2, max: 20, message: '车牌号长度应在2-20个字符之间' }
      ],
      brand: [
        { required: true, message: '请选择车辆品牌' }
      ]
    };

    // 获取当前用户
    const getCurrentUser = () => {
      const userInfoStr = localStorage.getItem('userInfo');
      if (userInfoStr) {
        try {
          return JSON.parse(userInfoStr);
        } catch (error) {
          console.error('Parse user info error:', error);
          return null;
        }
      }
      return null;
    };

    // 加载车辆列表
    const loadVehicles = async () => {
      loading.value = true;
      try {
        const currentUser = getCurrentUser();
        if (!currentUser) {
          message.error('用户未登录');
          return;
        }

        const response = await axios.get('/vehicle/getVehicleListByPage', {
          params: {
            page: pagination.current,
            size: pagination.pageSize,
            licensePlate: searchParams.licensePlate || undefined,
            brand: searchParams.brand || undefined,
            userId: currentUser.id
          }
        });

        const data = response.data;
        if (data.success !== false) {
          vehicles.value = data.content.list || [];
          pagination.total = data.content.total || 0;
        } else {
          message.error(data.message || '获取车辆列表失败');
        }
      } catch (error) {
        console.error('Load vehicles error:', error);
        message.error('加载车辆列表失败，请重试');
      } finally {
        loading.value = false;
      }
    };

    // 处理搜索
    const handleSearch = () => {
      pagination.current = 1;
      loadVehicles();
    };

    // 重置搜索
    const handleReset = () => {
      searchParams.licensePlate = '';
      searchParams.brand = '';
      pagination.current = 1;
      loadVehicles();
    };
    
    // 年份选择器相关方法
    const toggleYearPicker = () => {
      showYearPicker.value = !showYearPicker.value;
    };
    
    const selectYear = (year: number) => {
      currentVehicle.value.year = year;
      showYearPicker.value = false;
    };
    
    // 处理模态框内容点击（用于关闭年份选择器）
    const handleModalBodyClick = (event: Event) => {
      // 如果点击的不是年份选择器相关元素，则关闭年份选择器
      const target = event.target as Element;
      if (!target.closest('.year-picker-container')) {
        showYearPicker.value = false;
      }
      // 关闭品牌和颜色选项下拉框
      if (!target.closest('.custom-input-wrapper')) {
        showBrandOptions.value = false;
        showColorOptions.value = false;
      }
    };
    
    // 品牌相关方法
    const filterBrandOptions = () => {
      const input = (currentVehicle.value.brand && currentVehicle.value.brand.toLowerCase()) || '';
      filteredBrands.value = carBrands.filter(brand => 
        brand.toLowerCase().includes(input)
      );
    };
    
    const selectBrand = (brand: string) => {
      currentVehicle.value.brand = brand;
      showBrandOptions.value = false;
    };
    
    const handleBrandBlur = () => {
      // 延迟关闭，以便点击选项能正常工作
      setTimeout(() => {
        showBrandOptions.value = false;
      }, 150);
    };
    
    const toggleBrandDropdown = () => {
      showBrandOptions.value = !showBrandOptions.value;
      if (showBrandOptions.value) {
        filterBrandOptions();
      }
    };
    
    // 颜色相关方法
    const filterColorOptions = () => {
      const input = (currentVehicle.value.color && currentVehicle.value.color.toLowerCase()) || '';
      filteredColors.value = carColors.filter(color => 
        color.toLowerCase().includes(input)
      );
    };
    
    const selectColor = (color: string) => {
      currentVehicle.value.color = color;
      showColorOptions.value = false;
    };
    
    const handleColorBlur = () => {
      // 延迟关闭，以便点击选项能正常工作
      setTimeout(() => {
        showColorOptions.value = false;
      }, 150);
    };
    
    const toggleColorDropdown = () => {
      showColorOptions.value = !showColorOptions.value;
      if (showColorOptions.value) {
        filterColorOptions();
      }
    };

    // 表格变化处理
    const handleTableChange = (pag: any) => {
      pagination.current = pag.current;
      pagination.pageSize = pag.pageSize;
      loadVehicles();
    };

    // 添加车辆
    const handleAdd = () => {
      console.log('🚗 添加车辆');
      console.log('当前品牌数组:', carBrands);
      console.log('品牌数组长度:', carBrands.length);
      
      currentVehicle.value = {
        id: undefined,
        licensePlate: '',
        brand: '',
        model: '',
        color: '',
        year: undefined,
        engineNumber: '',
        vin: '',
        mileage: 0,
        isDefault: 0,
        status: 1,
        isDefaultChecked: false
      };
      
      showModal.value = true;
      showYearPicker.value = false; // 重置年份选择器状态
      showBrandOptions.value = false; // 重置品牌选项状态
      showColorOptions.value = false; // 重置颜色选项状态
      
      // 重置过滤选项
      filteredBrands.value = [...carBrands];
      filteredColors.value = [...carColors];
      
      // 延迟检查DOM中的选项
      setTimeout(() => {
        const selectOptions = document.querySelectorAll('.ant-select-item-option-content');
        console.log('DOM中找到的选项数量:', selectOptions.length);
        if (selectOptions.length === 0) {
          console.warn('⚠️ 下拉选项未正确渲染到DOM中');
        }
      }, 500);
    };

    // 编辑车辆
    const handleEdit = (vehicle: Vehicle) => {
      currentVehicle.value = {
        ...vehicle,
        isDefaultChecked: vehicle.isDefault === 1
      };
      showModal.value = true;
      showYearPicker.value = false; // 重置年份选择器状态
      showBrandOptions.value = false; // 重置品牌选项状态
      showColorOptions.value = false; // 重置颜色选项状态
      
      // 重置过滤选项
      filteredBrands.value = [...carBrands];
      filteredColors.value = [...carColors];
    };

    // 删除车辆
    const handleDelete = (vehicle: Vehicle) => {
      confirmDelete(vehicle);
    };

    // 确认删除
    const confirmDelete = async (vehicle: Vehicle) => {
      try {
        const response = await axios.get('/vehicle/remove', {
          params: { id: vehicle.id }
        });

        const data = response.data;
        if (data.success !== false) {
          message.success('删除成功');
          loadVehicles();
        } else {
          message.error(data.message || '删除失败');
        }
      } catch (error) {
        console.error('Delete vehicle error:', error);
        message.error('删除失败，请重试');
      }
    };

    // 设为默认车辆
    const setAsDefault = async (vehicle: Vehicle) => {
      try {
        const currentUser = getCurrentUser();
        if (!currentUser) {
          message.error('用户未登录');
          return;
        }

        const response = await axios.post('/vehicle/save', {
          ...vehicle,
          isDefault: 1,
          userId: currentUser.id
        });

        const data = response.data;
        if (data.success !== false) {
          message.success('设置成功');
          loadVehicles();
        } else {
          message.error(data.message || '设置失败');
        }
      } catch (error) {
        console.error('Set default error:', error);
        message.error('设置失败，请重试');
      }
    };


    // 保存车辆
    const handleSave = async () => {
      try {
        // 验证表单
        if (formRef.value) {
          await formRef.value.validateFields();
        }
        saving.value = true;

        const currentUser = getCurrentUser();
        if (!currentUser) {
          message.error('用户未登录');
          saving.value = false;
          return;
        }

        console.log('开始保存车辆，用户ID:', currentUser.id);
        console.log('当前车辆数据:', currentVehicle.value);
        console.log('选择的品牌:', currentVehicle.value.brand);

        // 确保必填字段不为空
        if (!currentVehicle.value.licensePlate || currentVehicle.value.licensePlate.trim() === '') {
          message.error('车牌号不能为空');
          saving.value = false;
          return;
        }
        
        if (!currentVehicle.value.brand || currentVehicle.value.brand.trim() === '' || currentVehicle.value.brand === '') {
          message.error('请选择车辆品牌');
          saving.value = false;
          return;
        }
        
        console.log('✅ 表单验证通过，准备保存数据');

        const vehicleData = {
          id: currentVehicle.value.id,
          licensePlate: currentVehicle.value.licensePlate.trim(),
          brand: currentVehicle.value.brand,
          model: currentVehicle.value.model || null,
          color: currentVehicle.value.color || null,
          year: currentVehicle.value.year || null,
          engineNumber: currentVehicle.value.engineNumber || null,
          vin: currentVehicle.value.vin || null,
          mileage: currentVehicle.value.mileage || 0,
          isDefault: currentVehicle.value.isDefaultChecked ? 1 : 0,
          status: 1,
          userId: currentUser.id
        };

        console.log('即将发送的车辆数据:', vehicleData);

        const response = await axios.post('/vehicle/save', vehicleData);
        console.log('API响应:', response.data);
        
        const data = response.data;

        if (data.success !== false) {
          message.success(data.message || '保存成功');
          showModal.value = false;
          loadVehicles();
        } else {
          message.error(data.message || '保存失败');
        }
      } catch (error: any) {
        console.error('❌ 保存车辆失败:', error);
        let errorMessage = '未知错误';
        
        if (error && error.response) {
          console.error('API响应数据:', error.response.data);
          console.error('HTTP状态码:', error.response.status);
          
          if (error.response.data && error.response.data.message) {
            errorMessage = error.response.data.message;
          } else if (error.response.status === 400) {
            errorMessage = '请求参数错误，请检查输入的数据';
          } else if (error.response.status === 401) {
            errorMessage = '用户未登录或登录已过期';
          } else if (error.response.status === 500) {
            errorMessage = '服务器内部错误';
          } else {
            errorMessage = `HTTP错误: ${error.response.status}`;
          }
        } else if (error && error.message) {
          errorMessage = error.message;
        }
        
        message.error('保存失败: ' + errorMessage);
      } finally {
        saving.value = false;
      }
    };

    // 取消操作
    const handleCancel = () => {
      showModal.value = false;
      showYearPicker.value = false; // 重置年份选择器状态
      showBrandOptions.value = false; // 重置品牌选项状态
      showColorOptions.value = false; // 重置颜色选项状态
      
      // 重置过滤选项
      filteredBrands.value = [...carBrands];
      filteredColors.value = [...carColors];
      
      if (formRef.value) {
        formRef.value.resetFields();
      }
    };

    // 格式化里程数
    const formatMileage = (mileage: number | undefined) => {
      if (!mileage && mileage !== 0) return '-';
      return `${mileage.toLocaleString()}公里`;
    };

    // 格式化日期
    const formatDate = (dateStr: string | undefined) => {
      if (!dateStr) return '-';
      return dayjs(dateStr).format('YYYY-MM-DD');
    };



    // 监听窗口大小变化
    const handleResize = () => {
      isMobile.value = window.innerWidth <= 768;
    };

    onMounted(() => {
      console.log('🚗 车辆管理组件已挂载');
      console.log('品牌数据:', carBrands);
      console.log('品牌数量:', carBrands.length);
      loadVehicles();
      window.addEventListener('resize', handleResize);
    });

    return {
      vehicles,
      loading,
      saving,
      showModal,
      formRef,
      isMobile,
      searchParams,
      pagination,
      paginationConfig,
      currentVehicle,
      modalTitle,
      carBrands,
      carColors,
      yearOptions,
      showYearPicker,
      showBrandOptions,
      showColorOptions,
      filteredBrands,
      filteredColors,
      currentYear,
      minYear,
      maxYear,
      columns,
      formRules,
      loadVehicles,
      handleSearch,
      handleReset,
      handleTableChange,
      handleAdd,
      handleEdit,
      handleDelete,
      confirmDelete,
      setAsDefault,
      toggleYearPicker,
      selectYear,
      handleModalBodyClick,
      filterBrandOptions,
      selectBrand,
      handleBrandBlur,
      toggleBrandDropdown,
      filterColorOptions,
      selectColor,
      handleColorBlur,
      toggleColorDropdown,
      handleSave,
      handleCancel,
      formatMileage,
      formatDate
    };
  }
});
</script>

<style scoped>
.vehicle-management {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0;
  position: relative;
}

.vehicle-management::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.03)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

/* 页面头部 */
.page-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 0 0 24px 24px;
  padding: 32px;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content .page-title {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0 0 8px 0;
  font-size: 32px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-description {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.header-actions .ant-btn {
  height: 44px;
  border-radius: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 搜索区域 */
.search-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 24px;
  margin: 0 32px 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

/* 车辆列表区域 */
.vehicle-list-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  margin: 0 32px 32px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
  overflow: hidden;
}

/* 移动端卡片视图 */
.mobile-view {
  padding: 24px;
}

.loading-container {
  text-align: center;
  padding: 60px 0;
}

.empty-state {
  text-align: center;
  padding: 60px 0;
}

.vehicle-cards {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.vehicle-card {
  background: rgba(248, 250, 252, 0.8);
  border: 1px solid #e8e8e8;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s;
  position: relative;
}

.vehicle-card.default-vehicle {
  border: 2px solid #fadb14;
  box-shadow: 0 4px 12px rgba(250, 219, 20, 0.15);
}

.vehicle-card:hover {
  background: rgba(248, 250, 252, 1);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.license-plate {
  display: flex;
  align-items: center;
  gap: 8px;
}

.plate-text {
  font-size: 18px;
  font-weight: 700;
  color: #1a1a1a;
}

.default-tag {
  font-size: 12px;
}

.card-actions {
  display: flex;
  align-items: center;
}

.vehicle-info {
  margin-bottom: 16px;
}

.info-row {
  display: flex;
  margin-bottom: 8px;
}

.label {
  color: #666;
  width: 80px;
  flex-shrink: 0;
}

.value {
  color: #1a1a1a;
  font-weight: 500;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.create-time {
  font-size: 12px;
  color: #999;
}

/* 桌面端表格视图 */
.desktop-view {
  overflow-x: auto;
}

.desktop-view .ant-table {
  border-radius: 0;
}

.license-plate-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.license-plate-cell .plate-text {
  font-weight: 600;
  color: #1a1a1a;
}

.vehicle-info-cell .brand-model {
  font-weight: 500;
  color: #1a1a1a;
}

.vehicle-info-cell .details {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

.operation-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

/* 表单样式 */
.vehicle-form .ant-form-item {
  margin-bottom: 20px;
}

.license-input {
  text-transform: uppercase;
}

/* 自定义输入选择框样式 */
.custom-input-wrapper {
  position: relative;
  display: inline-block;
  width: 100%;
}

.custom-input-select {
  width: 100%;
  height: 40px;
  padding: 8px 36px 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  color: #262626;
  outline: none;
  transition: all 0.3s;
}

.custom-input-select:hover {
  border-color: #40a9ff;
}

.custom-input-select:focus {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.input-icon {
  position: absolute;
  top: 50%;
  right: 12px;
  transform: translateY(-50%);
  color: #bfbfbf;
  font-size: 12px;
  cursor: pointer;
  transition: transform 0.3s, color 0.3s;
}

.input-icon:hover {
  color: #40a9ff;
}

.custom-input-wrapper:focus-within .input-icon {
  color: #40a9ff;
}

.options-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #d9d9d9;
  border-top: none;
  border-radius: 0 0 6px 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1001;
  max-height: 200px;
  overflow-y: auto;
  animation: slideDown 0.2s ease;
}

.option-item {
  padding: 8px 12px;
  cursor: pointer;
  font-size: 14px;
  color: #262626;
  transition: background-color 0.2s;
}

.option-item:hover {
  background: #e6f7ff;
}

.option-item:last-child {
  border-radius: 0 0 6px 6px;
}

/* 隐藏datalist的默认样式 */
datalist {
  display: none;
}

/* 旧的自定义选择框样式保留（用于年份选择器） */
.custom-select-wrapper {
  position: relative;
  display: inline-block;
  width: 100%;
}

.custom-select {
  width: 100%;
  height: 40px;
  padding: 8px 36px 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  color: #262626;
  cursor: pointer;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  outline: none;
  transition: all 0.3s;
}

.custom-select:hover {
  border-color: #40a9ff;
}

.custom-select:focus {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.custom-select option {
  padding: 8px 12px;
  background: white;
  color: #262626;
  font-size: 14px;
}

.custom-select option:hover {
  background: #f5f5f5;
}

.select-icon {
  position: absolute;
  top: 50%;
  right: 12px;
  transform: translateY(-50%);
  color: #bfbfbf;
  font-size: 12px;
  pointer-events: none;
  transition: transform 0.3s;
}

.custom-select:focus + .select-icon {
  transform: translateY(-50%) rotate(180deg);
  color: #40a9ff;
}

/* 年份表格选择器样式 */
.year-picker-container {
  position: relative;
  width: 100%;
}

.year-display {
  width: 100%;
  height: 40px;
  padding: 8px 36px 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  color: #262626;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: all 0.3s;
}

.year-display:hover {
  border-color: #40a9ff;
}

.year-arrow {
  color: #bfbfbf;
  font-size: 12px;
  transition: transform 0.3s;
}

.year-arrow.open {
  transform: rotate(180deg);
  color: #40a9ff;
}

.year-grid-container {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  margin-top: 4px;
  animation: slideDown 0.2s ease;
}

.year-grid-header {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  background: #fafafa;
  border-radius: 6px 6px 0 0;
}

.year-range-text {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.year-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 8px;
  padding: 16px;
  max-height: 240px;
  overflow-y: auto;
}

.year-item {
  padding: 8px 12px;
  text-align: center;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
  border: 1px solid transparent;
  background: #fafafa;
}

.year-item:hover {
  background: #e6f7ff;
  border-color: #91d5ff;
}

.year-item.selected {
  background: #1890ff;
  color: white;
  border-color: #1890ff;
}

.year-item.current {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-color: #667eea;
  font-weight: 600;
}

.year-item.current.selected {
  background: #1890ff;
  border-color: #1890ff;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 危险操作项 */
.danger-item {
  color: #ff4d4f !important;
}

.danger-item:hover {
  background-color: #fff2f0 !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .vehicle-management {
    padding: 0;
  }
  
  .page-header {
    padding: 24px 20px;
    border-radius: 0;
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .header-content .page-title {
    font-size: 24px;
  }
  
  .search-section,
  .vehicle-list-section {
    margin: 0 16px 24px;
    border-radius: 12px;
  }
  
  .search-section {
    padding: 20px;
  }
  
  .mobile-view {
    padding: 20px;
  }
  
  .vehicle-card {
    padding: 16px;
  }
  
  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .card-actions {
    align-self: flex-end;
  }
}

@media (max-width: 480px) {
  .page-header {
    padding: 20px 16px;
  }
  
  .search-section,
  .vehicle-list-section {
    margin: 0 8px 16px;
  }
  
  .search-section {
    padding: 16px;
  }
  
  .mobile-view {
    padding: 16px;
  }
  
  .vehicle-card {
    padding: 12px;
  }
  
  .info-row {
    flex-direction: column;
    margin-bottom: 12px;
  }
  
  .label {
    width: auto;
    margin-bottom: 4px;
    font-size: 12px;
  }
  
  .card-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  /* 移动端select优化 */
  .custom-select {
    height: 44px;
    font-size: 16px; /* 防止iOS缩放 */
  }
  
  .select-icon {
    font-size: 14px;
  }
  
  /* 移动端输入选择框优化 */
  .custom-input-select {
    height: 44px;
    font-size: 16px; /* 防止iOS缩放 */
  }
  
  .input-icon {
    font-size: 14px;
  }
  
  .options-dropdown {
    max-height: 40vh;
  }
  
  .option-item {
    padding: 12px;
    font-size: 16px;
  }
  
  /* 移动端年份选择器优化 */
  .year-display {
    height: 44px;
    font-size: 16px;
  }
  
  .year-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 12px;
    padding: 12px;
  }
  
  .year-item {
    padding: 12px 8px;
    font-size: 16px;
  }
  
  .year-grid-container {
    max-height: 60vh;
  }
}

/* 原生模态框样式 */
.custom-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.3s ease;
}

.custom-modal {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  animation: slideIn 0.3s ease;
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #f0f0f0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.modal-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

.modal-body {
  padding: 24px;
  overflow-y: auto;
  flex: 1;
}

.modal-footer {
  padding: 16px 24px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  background: #fafafa;
}

.btn {
  padding: 8px 16px;
  border-radius: 8px;
  border: none;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  min-width: 80px;
}

.btn:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.btn-cancel {
  background: #f5f5f5;
  color: #666;
}

.btn-cancel:hover:not(:disabled) {
  background: #e0e0e0;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #5a6fd8, #6a42a0);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { 
    opacity: 0; 
    transform: scale(0.9) translateY(-20px);
  }
  to { 
    opacity: 1; 
    transform: scale(1) translateY(0);
  }
}
</style>
