package com.gec.wiki.service.impl;

import com.gec.wiki.domain.SecuritySettings;
import com.gec.wiki.mapper.SecuritySettingsMapper;
import com.gec.wiki.service.SecuritySettingsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.regex.Pattern;

/**
 * 安全设置服务实现类
 */
@Service
public class SecuritySettingsServiceImpl implements SecuritySettingsService {
    
    private static final Logger LOG = LoggerFactory.getLogger(SecuritySettingsServiceImpl.class);
    
    @Resource
    private SecuritySettingsMapper securitySettingsMapper;
    
    // 默认安全设置
    private static final SecuritySettings DEFAULT_SETTINGS = new SecuritySettings(5, 30, 6, false);
    
    // 复杂密码正则：至少包含数字、字母
    private static final Pattern COMPLEX_PASSWORD_PATTERN = Pattern.compile("^(?=.*[0-9])(?=.*[a-zA-Z]).+$");
    
    @Override
    public SecuritySettings getSecuritySettings() {
        try {
            SecuritySettings settings = securitySettingsMapper.getSecuritySettings();
            if (settings == null) {
                LOG.info("未找到安全设置，返回默认设置");
                return DEFAULT_SETTINGS;
            }
            return settings;
        } catch (Exception e) {
            LOG.error("获取安全设置失败，返回默认设置", e);
            return DEFAULT_SETTINGS;
        }
    }
    
    @Override
    public boolean saveSecuritySettings(SecuritySettings securitySettings) {
        try {
            // 验证参数
            if (securitySettings == null) {
                LOG.warn("安全设置对象为空");
                return false;
            }
            
            // 设置默认值和边界检查
            if (securitySettings.getMaxLoginFailCount() == null || 
                securitySettings.getMaxLoginFailCount() < 3 || 
                securitySettings.getMaxLoginFailCount() > 10) {
                securitySettings.setMaxLoginFailCount(5);
            }
            
            if (securitySettings.getLockDurationMinutes() == null || 
                securitySettings.getLockDurationMinutes() < 5 || 
                securitySettings.getLockDurationMinutes() > 1440) {
                securitySettings.setLockDurationMinutes(30);
            }
            
            if (securitySettings.getMinPasswordLength() == null || 
                securitySettings.getMinPasswordLength() < 6 || 
                securitySettings.getMinPasswordLength() > 20) {
                securitySettings.setMinPasswordLength(6);
            }
            
            if (securitySettings.getRequireComplexPassword() == null) {
                securitySettings.setRequireComplexPassword(false);
            }
            
            // 检查是否已存在设置
            int count = securitySettingsMapper.countSettings();
            int result;
            
            if (count == 0) {
                // 插入新记录
                result = securitySettingsMapper.insertSecuritySettings(securitySettings);
                LOG.info("插入新的安全设置: {}", securitySettings);
            } else {
                // 更新现有记录
                SecuritySettings existingSettings = securitySettingsMapper.getSecuritySettings();
                securitySettings.setId(existingSettings.getId());
                result = securitySettingsMapper.updateSecuritySettings(securitySettings);
                LOG.info("更新安全设置: {}", securitySettings);
            }
            
            return result > 0;
            
        } catch (Exception e) {
            LOG.error("保存安全设置失败", e);
            return false;
        }
    }
    
    @Override
    public int getMaxLoginFailCount() {
        return getSecuritySettings().getMaxLoginFailCount();
    }
    
    @Override
    public int getLockDurationMinutes() {
        return getSecuritySettings().getLockDurationMinutes();
    }
    
    @Override
    public int getMinPasswordLength() {
        return getSecuritySettings().getMinPasswordLength();
    }
    
    @Override
    public boolean isRequireComplexPassword() {
        return getSecuritySettings().getRequireComplexPassword();
    }
    
    @Override
    public String validatePassword(String password) {
        if (password == null || password.trim().isEmpty()) {
            return "密码不能为空";
        }
        
        SecuritySettings settings = getSecuritySettings();
        
        // 检查长度
        if (password.length() < settings.getMinPasswordLength()) {
            return String.format("密码长度不能少于%d位", settings.getMinPasswordLength());
        }
        
        // 检查复杂度
        if (settings.getRequireComplexPassword()) {
            if (!COMPLEX_PASSWORD_PATTERN.matcher(password).matches()) {
                return "密码必须包含数字和字母";
            }
        }
        
        return null; // 验证通过
    }
}
