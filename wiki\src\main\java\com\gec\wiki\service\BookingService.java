package com.gec.wiki.service;

import com.gec.wiki.pojo.req.BookingCreateReq;
import com.gec.wiki.pojo.resp.CommonResp;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 预约服务接口
 */
public interface BookingService {
    
    /**
     * 创建预约
     */
    CommonResp<Object> createBooking(BookingCreateReq req, Long userId);
    
    /**
     * 获取用户预约列表
     */
    CommonResp<Object> getBookingList(Long userId, Integer page, Integer size);
    
    /**
     * 获取预约详情
     */
    CommonResp<Object> getBookingDetail(Long id, Long userId);
    
    /**
     * 取消预约
     */
    CommonResp<Object> cancelBooking(Long id, Long userId);
    
    /**
     * 获取可用时间段
     */
    CommonResp<Object> getAvailableTimes(LocalDate date, Long serviceId);
    
    /**
     * 获取维修店今日预约列表
     */
    CommonResp<Object> getTodayBookingsForShop(Long shopId);
    
    /**
     * 更新预约状态
     */
    CommonResp<Object> updateBookingStatus(Long bookingId, Integer status, Long shopId);
    
    /**
     * 获取维修店预约统计
     */
    CommonResp<Object> getBookingStatsForShop(Long shopId);
    
    /**
     * 获取维修店所有预约列表（分页）
     */
    CommonResp<Object> getBookingsForShop(Long shopId, Integer page, Integer size, Integer status);
}
