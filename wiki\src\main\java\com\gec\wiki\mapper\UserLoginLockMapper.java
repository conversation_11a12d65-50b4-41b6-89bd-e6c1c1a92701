package com.gec.wiki.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gec.wiki.pojo.UserLoginLock;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 用户登录锁定记录Mapper
 */
@Mapper
public interface UserLoginLockMapper extends BaseMapper<UserLoginLock> {
    
    /**
     * 根据用户名和IP地址查找锁定记录
     */
    @Select("SELECT * FROM user_login_lock WHERE username = #{username} AND ip_address = #{ipAddress} LIMIT 1")
    UserLoginLock findByUsernameAndIp(@Param("username") String username, @Param("ipAddress") String ipAddress);
    
    /**
     * 根据用户名查找锁定记录
     */
    @Select("SELECT * FROM user_login_lock WHERE username = #{username} LIMIT 1")
    UserLoginLock findByUsername(@Param("username") String username);
    
}
