<template>
  <div class="order-detail">
    <div class="page-header">
      <a-button @click="$router.go(-1)">
        <ArrowLeftOutlined />
        返回
      </a-button>
      <h1>订单详情</h1>
    </div>

    <a-card title="订单信息" style="margin-bottom: 24px;">
      <a-descriptions :column="2" bordered>
        <a-descriptions-item label="订单号">{{ order.orderNo }}</a-descriptions-item>
        <a-descriptions-item label="状态">
          <a-tag :color="getStatusColor(order.status)">
            {{ getStatusText(order.status) }}
          </a-tag>
        </a-descriptions-item>
        <a-descriptions-item label="客户姓名">{{ order.customerName }}</a-descriptions-item>
        <a-descriptions-item label="联系电话">{{ order.customerPhone }}</a-descriptions-item>
        <a-descriptions-item label="车辆信息">{{ order.vehicleInfo }}</a-descriptions-item>
        <a-descriptions-item label="预约时间">{{ order.bookingTime }}</a-descriptions-item>
        <a-descriptions-item label="服务项目">{{ order.serviceName }}</a-descriptions-item>
        <a-descriptions-item label="服务金额">¥{{ order.amount }}</a-descriptions-item>
        <a-descriptions-item label="创建时间" :span="2">{{ order.createTime }}</a-descriptions-item>
        <a-descriptions-item label="服务描述" :span="2">{{ order.serviceDescription }}</a-descriptions-item>
      </a-descriptions>
    </a-card>

    <a-card title="操作记录" style="margin-bottom: 24px;">
      <a-timeline>
        <a-timeline-item v-for="log in orderLogs" :key="log.id">
          <div class="log-item">
            <div class="log-title">{{ log.action }}</div>
            <div class="log-time">{{ log.timestamp }}</div>
            <div class="log-operator">操作人: {{ log.operator }}</div>
          </div>
        </a-timeline-item>
      </a-timeline>
    </a-card>

    <a-card title="订单操作">
      <a-space>
        <a-button 
          type="primary" 
          v-if="order.status === 1" 
          @click="confirmOrder"
        >
          确认订单
        </a-button>
        <a-button 
          type="primary" 
          v-if="order.status === 2" 
          @click="startService"
        >
          开始服务
        </a-button>
        <a-button 
          type="primary" 
          v-if="order.status === 3" 
          @click="completeService"
        >
          完成服务
        </a-button>
        <a-button 
          danger 
          v-if="[1, 2].includes(order.status)" 
          @click="cancelOrder"
        >
          取消订单
        </a-button>
      </a-space>
    </a-card>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { ArrowLeftOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';

export default defineComponent({
  name: 'OrderDetail',
  components: {
    ArrowLeftOutlined
  },
  setup() {
    const route = useRoute();
    const orderId = Array.isArray(route.params.id) ? route.params.id[0] : route.params.id;

    const order = ref({
      id: null as string | number | null,
      orderNo: '',
      status: 1,
      customerName: '',
      customerPhone: '',
      vehicleInfo: '',
      bookingTime: '',
      serviceName: '',
      amount: 0,
      createTime: '',
      serviceDescription: ''
    });

    const orderLogs = ref<any[]>([]);

    const getStatusColor = (status: number) => {
      const colors: Record<number, string> = {
        1: 'orange', 2: 'blue', 3: 'cyan', 4: 'green', 5: 'red'
      };
      return colors[status] || 'default';
    };

    const getStatusText = (status: number) => {
      const texts: Record<number, string> = {
        1: '待确认', 2: '已确认', 3: '服务中', 4: '已完成', 5: '已取消'
      };
      return texts[status] || '未知';
    };

    const confirmOrder = () => {
      order.value.status = 2;
      orderLogs.value.unshift({
        id: Date.now(),
        action: '确认订单',
        timestamp: new Date().toLocaleString(),
        operator: '维修店'
      });
      message.success('订单已确认');
    };

    const startService = () => {
      order.value.status = 3;
      orderLogs.value.unshift({
        id: Date.now(),
        action: '开始服务',
        timestamp: new Date().toLocaleString(),
        operator: '维修店'
      });
      message.success('服务已开始');
    };

    const completeService = () => {
      order.value.status = 4;
      orderLogs.value.unshift({
        id: Date.now(),
        action: '完成服务',
        timestamp: new Date().toLocaleString(),
        operator: '维修店'
      });
      message.success('服务已完成');
    };

    const cancelOrder = () => {
      order.value.status = 5;
      orderLogs.value.unshift({
        id: Date.now(),
        action: '取消订单',
        timestamp: new Date().toLocaleString(),
        operator: '维修店'
      });
      message.success('订单已取消');
    };

    const loadOrderDetail = () => {
      // TODO: 实现API调用
      // 模拟数据
      order.value = {
        id: orderId,
        orderNo: 'ORD20240115001',
        status: 2,
        customerName: '张先生',
        customerPhone: '13800138001',
        vehicleInfo: '大众朗逸 京A12345',
        bookingTime: '2024-01-15 10:00',
        serviceName: '机油更换',
        amount: 150,
        createTime: '2024-01-14 15:30',
        serviceDescription: '更换全合成机油，机油滤芯'
      };

      orderLogs.value = [
        {
          id: 2,
          action: '确认订单',
          timestamp: '2024-01-14 16:00',
          operator: '维修店'
        },
        {
          id: 1,
          action: '创建订单',
          timestamp: '2024-01-14 15:30',
          operator: '客户'
        }
      ];
    };

    onMounted(() => {
      loadOrderDetail();
    });

    return {
      order,
      orderLogs,
      getStatusColor,
      getStatusText,
      confirmOrder,
      startService,
      completeService,
      cancelOrder
    };
  }
});
</script>

<style scoped>
.order-detail {
  padding: 24px;
}

.page-header {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 0 16px;
  font-size: 24px;
  color: #333;
}

.log-item {
  margin-bottom: 8px;
}

.log-title {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.log-time {
  font-size: 12px;
  color: #999;
  margin-bottom: 2px;
}

.log-operator {
  font-size: 12px;
  color: #666;
}
</style>
