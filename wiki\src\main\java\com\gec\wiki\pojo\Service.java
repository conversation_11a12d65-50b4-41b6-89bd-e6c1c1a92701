package com.gec.wiki.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 汽车维修服务
 * @TableName service
 */
@TableName(value = "service")
public class Service implements Serializable {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 服务名称
     */
    @TableField(value = "name")
    private String name;

    /**
     * 一级分类
     */
    @TableField(value = "category1_id")
    private Long category1Id;

    /**
     * 二级分类
     */
    @TableField(value = "category2_id")
    private Long category2Id;

    /**
     * 服务描述
     */
    @TableField(value = "description")
    private String description;

    /**
     * 服务详细内容
     */
    @TableField(value = "content")
    private String content;

    /**
     * 服务图片
     */
    @TableField(value = "cover")
    private String cover;

    /**
     * 服务图片集(JSON格式)
     */
    @TableField(value = "images")
    private String images;

    /**
     * 服务价格
     */
    @TableField(value = "price")
    private BigDecimal price;

    /**
     * 原价
     */
    @TableField(value = "original_price")
    private BigDecimal originalPrice;

    /**
     * 预约次数
     */
    @TableField(value = "booking_count")
    private Integer bookingCount;

    /**
     * 完成次数
     */
    @TableField(value = "complete_count")
    private Integer completeCount;

    /**
     * 好评数
     */
    @TableField(value = "rating_count")
    private Integer ratingCount;

    /**
     * 评分
     */
    @TableField(value = "rating_score")
    private BigDecimal ratingScore;

    /**
     * 服务时长(分钟)
     */
    @TableField(value = "duration")
    private Integer duration;

    /**
     * 是否推荐(0-否 1-是)
     */
    @TableField(value = "is_recommend")
    private Integer isRecommend;

    /**
     * 状态(0-下架 1-上架)
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 维修店ID(空表示平台服务)
     */
    @TableField(value = "shop_id")
    private Long shopId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time")
    private LocalDateTime updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getCategory1Id() {
        return category1Id;
    }

    public void setCategory1Id(Long category1Id) {
        this.category1Id = category1Id;
    }

    public Long getCategory2Id() {
        return category2Id;
    }

    public void setCategory2Id(Long category2Id) {
        this.category2Id = category2Id;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getCover() {
        return cover;
    }

    public void setCover(String cover) {
        this.cover = cover;
    }

    public String getImages() {
        return images;
    }

    public void setImages(String images) {
        this.images = images;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getOriginalPrice() {
        return originalPrice;
    }

    public void setOriginalPrice(BigDecimal originalPrice) {
        this.originalPrice = originalPrice;
    }

    public Integer getBookingCount() {
        return bookingCount;
    }

    public void setBookingCount(Integer bookingCount) {
        this.bookingCount = bookingCount;
    }

    public Integer getCompleteCount() {
        return completeCount;
    }

    public void setCompleteCount(Integer completeCount) {
        this.completeCount = completeCount;
    }

    public Integer getRatingCount() {
        return ratingCount;
    }

    public void setRatingCount(Integer ratingCount) {
        this.ratingCount = ratingCount;
    }

    public BigDecimal getRatingScore() {
        return ratingScore;
    }

    public void setRatingScore(BigDecimal ratingScore) {
        this.ratingScore = ratingScore;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public Integer getIsRecommend() {
        return isRecommend;
    }

    public void setIsRecommend(Integer isRecommend) {
        this.isRecommend = isRecommend;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "Service{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", category1Id=" + category1Id +
                ", category2Id=" + category2Id +
                ", description='" + description + '\'' +
                ", content='" + content + '\'' +
                ", cover='" + cover + '\'' +
                ", images='" + images + '\'' +
                ", price=" + price +
                ", originalPrice=" + originalPrice +
                ", bookingCount=" + bookingCount +
                ", completeCount=" + completeCount +
                ", ratingCount=" + ratingCount +
                ", ratingScore=" + ratingScore +
                ", duration=" + duration +
                ", isRecommend=" + isRecommend +
                ", status=" + status +
                ", shopId=" + shopId +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
