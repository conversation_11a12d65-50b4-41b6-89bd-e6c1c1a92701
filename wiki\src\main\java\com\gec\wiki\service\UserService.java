package com.gec.wiki.service;

import com.gec.wiki.pojo.User;
import com.gec.wiki.pojo.req.UserLoginReq;
import com.gec.wiki.pojo.req.UserRegisterReq;
import com.gec.wiki.pojo.resp.CommonResp;

/**
 * 用户服务接口
 */
public interface UserService {
    
    /**
     * 用户登录
     */
    CommonResp<Object> login(UserLoginReq req);
    
    /**
     * 用户注册
     */
    CommonResp<Object> register(UserRegisterReq req);
    
    /**
     * 根据用户名查询用户
     */
    User findByUsername(String username);
    
    /**
     * 根据手机号查询用户
     */
    User findByPhone(String phone);
    
    /**
     * 更新最后登录时间
     */
     void updateLastLoginTime(Long userId);
    
    /**
     * 根据真实姓名、手机号和邮箱查找用户（忘记密码功能）
     */
    User findByRealNameAndPhoneAndEmail(String realName, String phone, String email);
    
    /**
     * 重置用户密码
     * @param userId 用户ID
     * @param newPassword 新密码（明文）
     * @return 是否更新成功
     */
    boolean resetPassword(Long userId, String newPassword);
}
