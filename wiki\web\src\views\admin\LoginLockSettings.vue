<template>
  <div class="login-lock-settings">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1>
            <LockOutlined />
            登录锁定设置
          </h1>
          <p>管理用户登录错误锁定策略，保护系统安全</p>
        </div>
      </div>
    </div>

    <!-- 设置表单区域 -->
    <div class="settings-container">
      <a-card class="settings-card" title="锁定策略设置">
        <a-form
          :model="settingsForm"
          :rules="settingsRules"
          ref="settingsFormRef"
          layout="vertical"
          @finish="handleUpdateSettings"
        >
          <a-form-item 
            name="maxFailCount" 
            label="最大失败次数"
            extra="用户连续登录失败超过此次数后将被锁定"
          >
            <a-input-number
              v-model:value="settingsForm.maxFailCount"
              :min="1"
              :max="100"
              style="width: 200px"
              placeholder="请输入最大失败次数"
            />
            <span style="margin-left: 8px; color: #666;">次</span>
          </a-form-item>
          
          <a-form-item label="锁定时长">
            <a-input
              value="30分钟"
              disabled
              style="width: 200px"
            />
            <span style="margin-left: 8px; color: #999;">（固定时长，不可修改）</span>
          </a-form-item>
          
          <a-form-item>
            <a-button
              type="primary"
              html-type="submit"
              :loading="updateLoading"
              size="large"
            >
              <SaveOutlined />
              保存设置
            </a-button>
            <a-button
              style="margin-left: 12px"
              @click="loadSettings"
              :loading="loadingSettings"
            >
              <ReloadOutlined />
              重新加载
            </a-button>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 用户解锁区域 -->
    <div class="unlock-container">
      <a-card class="unlock-card" title="手动解锁用户">
        <a-form
          :model="unlockForm"
          :rules="unlockRules"
          ref="unlockFormRef"
          layout="vertical"
          @finish="handleUnlockUser"
        >
          <a-form-item 
            name="username" 
            label="用户名"
            extra="输入需要解锁的用户名"
          >
            <a-input
              v-model:value="unlockForm.username"
              placeholder="请输入用户名"
              style="width: 300px"
            >
              <template #prefix>
                <UserOutlined />
              </template>
            </a-input>
          </a-form-item>
          
          <a-form-item>
            <a-button
              type="primary"
              html-type="submit"
              :loading="unlockLoading"
              danger
              size="large"
            >
              <UnlockOutlined />
              立即解锁
            </a-button>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 系统信息区域 -->
    <div class="info-container">
      <a-card class="info-card" title="系统信息">
        <div class="info-grid">
          <div class="info-item">
            <div class="info-label">当前设置</div>
            <div class="info-value">
              连续失败 {{ currentSettings.maxFailCount }} 次后锁定 {{ currentSettings.lockDurationMinutes }} 分钟
            </div>
          </div>
          <div class="info-item">
            <div class="info-label">安全提醒</div>
            <div class="info-value">
              建议将最大失败次数设置为 3-10 次，既能防止暴力破解，又不会过度影响正常用户
            </div>
          </div>
          <div class="info-item">
            <div class="info-label">锁定规则</div>
            <div class="info-value">
              • 基于用户名和IP地址进行锁定<br/>
              • 登录成功后自动清除失败记录<br/>
              • 锁定期间无法登录，需等待解锁或手动解锁
            </div>
          </div>
        </div>
      </a-card>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue';
import { 
  LockOutlined,
  SaveOutlined,
  ReloadOutlined,
  UserOutlined,
  UnlockOutlined
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import axios from 'axios';

export default defineComponent({
  name: 'LoginLockSettings',
  components: {
    LockOutlined,
    SaveOutlined,
    ReloadOutlined,
    UserOutlined,
    UnlockOutlined
  },
  setup() {
    const settingsFormRef = ref();
    const unlockFormRef = ref();
    const loadingSettings = ref(false);
    const updateLoading = ref(false);
    const unlockLoading = ref(false);

    // 设置表单
    const settingsForm = ref({
      maxFailCount: 5
    });

    const settingsRules = {
      maxFailCount: [
        { required: true, message: '请输入最大失败次数', trigger: 'blur' },
        { type: 'number', min: 1, max: 100, message: '最大失败次数必须在1-100之间', trigger: 'blur' }
      ]
    };

    // 解锁表单
    const unlockForm = ref({
      username: ''
    });

    const unlockRules = {
      username: [
        { required: true, message: '请输入用户名', trigger: 'blur' },
        { min: 1, message: '用户名不能为空', trigger: 'blur' }
      ]
    };

    // 当前设置信息
    const currentSettings = ref({
      maxFailCount: 5,
      lockDurationMinutes: 30
    });

    // 加载设置
    const loadSettings = async () => {
      loadingSettings.value = true;
      try {
        console.log('🔍 加载登录锁定设置...');
        const response = await axios.get('/admin/settings/login-lock');
        
        if (response.data.success) {
          const settings = response.data.content;
          settingsForm.value.maxFailCount = settings.maxFailCount;
          currentSettings.value = {
            maxFailCount: settings.maxFailCount,
            lockDurationMinutes: settings.lockDurationMinutes
          };
          
          console.log('✅ 设置加载成功:', settings);
        } else {
          message.error(response.data.message || '加载设置失败');
        }
      } catch (error) {
        console.error('❌ 加载设置失败:', error);
        message.error('加载设置失败，请检查网络连接');
      } finally {
        loadingSettings.value = false;
      }
    };

    // 更新设置
    const handleUpdateSettings = async () => {
      updateLoading.value = true;
      try {
        console.log('⚙️ 更新登录锁定设置:', settingsForm.value);
        
        const response = await axios.post('/admin/settings/login-lock', {
          maxFailCount: settingsForm.value.maxFailCount
        });
        
        if (response.data.success) {
          message.success('设置更新成功');
          currentSettings.value.maxFailCount = settingsForm.value.maxFailCount;
          console.log('✅ 设置更新成功');
        } else {
          message.error(response.data.message || '设置更新失败');
        }
      } catch (error) {
        console.error('❌ 更新设置失败:', error);
        message.error('设置更新失败，请重试');
      } finally {
        updateLoading.value = false;
      }
    };

    // 解锁用户
    const handleUnlockUser = async () => {
      unlockLoading.value = true;
      try {
        console.log('🔓 解锁用户:', unlockForm.value.username);
        
        const response = await axios.post('/admin/settings/unlock-user', {
          username: unlockForm.value.username.trim()
        });
        
        if (response.data.success) {
          message.success(`用户 ${unlockForm.value.username} 解锁成功`);
          unlockForm.value.username = '';
          console.log('✅ 用户解锁成功');
        } else {
          message.error(response.data.message || '解锁失败');
        }
      } catch (error) {
        console.error('❌ 解锁用户失败:', error);
        message.error('解锁失败，请重试');
      } finally {
        unlockLoading.value = false;
      }
    };

    onMounted(() => {
      loadSettings();
    });

    return {
      settingsFormRef,
      unlockFormRef,
      loadingSettings,
      updateLoading,
      unlockLoading,
      settingsForm,
      settingsRules,
      unlockForm,
      unlockRules,
      currentSettings,
      loadSettings,
      handleUpdateSettings,
      handleUnlockUser
    };
  }
});
</script>

<style scoped>
.login-lock-settings {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0;
  position: relative;
}

.login-lock-settings::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.03)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

/* 页面头部 */
.page-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 0 0 24px 24px;
  padding: 32px;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
}

.title-section h1 {
  margin: 0 0 8px 0;
  color: #1a1a1a;
  font-size: 32px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 12px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.title-section p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

/* 设置容器 */
.settings-container,
.unlock-container,
.info-container {
  padding: 0 32px 24px;
  position: relative;
  z-index: 1;
  max-width: 1200px;
  margin: 0 auto;
}

.settings-card,
.unlock-card,
.info-card {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.settings-card .ant-card-head,
.unlock-card .ant-card-head,
.info-card .ant-card-head {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

/* 信息网格 */
.info-grid {
  display: grid;
  gap: 24px;
}

.info-item {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e8e8e8;
}

.info-label {
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  font-size: 16px;
}

.info-value {
  color: #666;
  line-height: 1.6;
  font-size: 14px;
}

/* 表单样式优化 */
.ant-form-item-label > label {
  font-weight: 500;
  color: #333;
}

.ant-form-item-extra {
  color: #999;
  font-size: 13px;
}

/* 按钮样式 */
.ant-btn {
  border-radius: 8px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.ant-btn-lg {
  height: 44px;
  padding: 0 24px;
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    padding: 24px 20px;
    border-radius: 0;
  }
  
  .settings-container,
  .unlock-container,
  .info-container {
    padding: 0 20px 24px;
  }
  
  .title-section h1 {
    font-size: 24px;
  }
}

@media (max-width: 576px) {
  .page-header {
    padding: 20px 16px;
  }
  
  .settings-container,
  .unlock-container,
  .info-container {
    padding: 0 16px 20px;
  }
  
  .title-section h1 {
    font-size: 20px;
  }
  
  .info-grid {
    gap: 16px;
  }
  
  .info-item {
    padding: 16px;
  }
}
</style>
