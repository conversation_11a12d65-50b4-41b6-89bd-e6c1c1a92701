package com.gec.wiki.pojo.req;

// import lombok.Data;

// @Data
public class VehicleQueryReq extends PageReq{
    private Long id;
    private String licensePlate;    //车牌号
    private String brand;          //品牌
    private String model;          //型号
    private Long userId;       //用户ID

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLicensePlate() {
        return licensePlate;
    }

    public void setLicensePlate(String licensePlate) {
        this.licensePlate = licensePlate;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }
}
