<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="257e56e0-5dd1-4371-b367-90a1cfa1cc49" name="更改" comment="">
      <change afterPath="$PROJECT_DIR$/wiki/web/public/image/大白鲨.jpg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wiki/web/public/image/巨型乌贼.jpg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wiki/web/public/image/座头鲸.jpg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wiki/web/public/image/抹香鲸.jpg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wiki/web/public/image/水母.jpg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wiki/web/public/image/绿海龟.jpg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wiki/web/public/image/蓝环章鱼.jpg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/wiki/web/public/image/蓝鲸.jpg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/wiki/web/package-lock.json" beforeDir="false" afterPath="$PROJECT_DIR$/wiki/web/package-lock.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/wiki/web/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/wiki/web/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/wiki/web/src/App.vue" beforeDir="false" afterPath="$PROJECT_DIR$/wiki/web/src/App.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/wiki/web/src/components/HelloWorld.vue" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/wiki/web/src/main.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/wiki/web/src/router/index.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/wiki/web/src/store/index.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/wiki/web/src/views/About.vue" beforeDir="false" afterPath="$PROJECT_DIR$/wiki/web/src/views/About.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/wiki/web/src/views/Home.vue" beforeDir="false" afterPath="$PROJECT_DIR$/wiki/web/src/views/Home.vue" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/wiki/web" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="E:\BrowserDownloads\apache-maven-3.5.0" />
        <option name="localRepository" value="E:\BrowserDownloads\apache-maven-3.5.0\repo" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="useMavenConfig" value="false" />
        <option name="userSettingsFile" value="E:\BrowserDownloads\apache-maven-3.5.0\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 6
}</component>
  <component name="ProjectId" id="2zMserb2zmqCD5IMetHFQ5cgMLx" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;HTTP 请求.test_vehicle_add_api | 2. 获取用户车辆列表.executor&quot;: &quot;Run&quot;,
    &quot;HTTP 请求.test_vehicle_add_api | 4. 删除车辆.executor&quot;: &quot;Run&quot;,
    &quot;HTTP 请求.test_vehicle_add_api | 测试车辆添加功能.executor&quot;: &quot;Run&quot;,
    &quot;HTTP 请求.test_vehicle_add_fixed | 2. 测试添加车辆 - 使用正确的字段映射.executor&quot;: &quot;Run&quot;,
    &quot;HTTP 请求.test_vehicle_add_fixed | 3. 测试添加另一辆车辆.executor&quot;: &quot;Run&quot;,
    &quot;HTTP 请求.test_vehicle_add_fixed | 5. 测试不完整数据 - 缺少必填字段.executor&quot;: &quot;Run&quot;,
    &quot;PowerShell.test_api_simple.ps1 (1).executor&quot;: &quot;Run&quot;,
    &quot;PowerShell.test_api_simple.ps1.executor&quot;: &quot;Run&quot;,
    &quot;PowerShell.test_vehicle_simple.ps1.executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;Spring Boot.WikiApplication.executor&quot;: &quot;Run&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/JavaCar/wiki/wiki/web/dist/image&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;npm.serve-dev.executor&quot;: &quot;Run&quot;,
    &quot;project.structure.last.edited&quot;: &quot;SDK&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;configurable.group.other&quot;,
    &quot;ts.external.directory.path&quot;: &quot;D:\\JavaCar\\wiki\\wiki\\web\\node_modules\\typescript\\lib&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql&quot;
    ]
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="D:\JavaCar\wiki\wiki\web\dist\image" />
      <recent name="D:\idea\wiki\wiki\web\public\image" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\JavaCar\wiki\wiki\web\dist\image" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.WikiApplication">
    <configuration name="test_vehicle_add_api | 2. 获取用户车辆列表" type="HttpClient.HttpRequestRunConfigurationType" factoryName="HTTP Request" temporary="true" nameIsGenerated="true" path="$PROJECT_DIR$/wiki/test_vehicle_add_api.http" executionIdentifier="2. 获取用户车辆列表" index="2" runType="运行单个请求">
      <method v="2" />
    </configuration>
    <configuration name="test_vehicle_add_api | 4. 删除车辆" type="HttpClient.HttpRequestRunConfigurationType" factoryName="HTTP Request" temporary="true" nameIsGenerated="true" path="$PROJECT_DIR$/wiki/test_vehicle_add_api.http" executionIdentifier="4. 删除车辆" index="4" runType="运行单个请求">
      <method v="2" />
    </configuration>
    <configuration name="test_vehicle_add_api | 测试车辆添加功能" type="HttpClient.HttpRequestRunConfigurationType" factoryName="HTTP Request" temporary="true" nameIsGenerated="true" path="$PROJECT_DIR$/wiki/test_vehicle_add_api.http" executionIdentifier="测试车辆添加功能" runType="运行单个请求">
      <method v="2" />
    </configuration>
    <configuration name="WikiApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" temporary="true" nameIsGenerated="true">
      <module name="wiki" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.gec.wiki.WikiApplication" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.gec.wiki.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="serve-dev" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/wiki/web/package.json" />
      <command value="run" />
      <scripts>
        <script value="serve-dev" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Spring Boot.WikiApplication" />
        <item itemvalue="npm.serve-dev" />
        <item itemvalue="HTTP 请求.test_vehicle_add_api | 4. 删除车辆" />
        <item itemvalue="HTTP 请求.test_vehicle_add_api | 2. 获取用户车辆列表" />
        <item itemvalue="HTTP 请求.test_vehicle_add_api | 测试车辆添加功能" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-a94e463ab2e7-intellij.indexing.shared.core-IU-243.26053.27" />
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-IU-243.26053.27" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="257e56e0-5dd1-4371-b367-90a1cfa1cc49" name="更改" comment="" />
      <created>1750178359785</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750178359785</updated>
      <workItem from="1751552375599" duration="6342000" />
      <workItem from="1751559582900" duration="4199000" />
      <workItem from="1751601804968" duration="757000" />
      <workItem from="1751605080219" duration="49000" />
      <workItem from="1751605814243" duration="20000" />
      <workItem from="1751606551243" duration="4187000" />
      <workItem from="1751611592156" duration="3196000" />
      <workItem from="1751626497159" duration="26000" />
      <workItem from="1756711460453" duration="816000" />
      <workItem from="1756713308685" duration="78000" />
      <workItem from="1756784544094" duration="696000" />
      <workItem from="1756794372961" duration="691000" />
      <workItem from="1756796213970" duration="1081000" />
      <workItem from="1756798796756" duration="5013000" />
      <workItem from="1756813853312" duration="1425000" />
      <workItem from="1756822297340" duration="14000" />
      <workItem from="1756898269666" duration="7846000" />
      <workItem from="1756956985429" duration="16834000" />
      <workItem from="1756981648053" duration="6485000" />
      <workItem from="1756999505887" duration="3436000" />
      <workItem from="1757041447067" duration="2104000" />
      <workItem from="1757047257713" duration="2507000" />
      <workItem from="1757053824315" duration="3411000" />
      <workItem from="1757070748666" duration="13433000" />
      <workItem from="1757223186992" duration="2008000" />
      <workItem from="1757225418371" duration="7450000" />
      <workItem from="1757300314805" duration="2042000" />
      <workItem from="1757313510049" duration="3288000" />
      <workItem from="1757319611583" duration="851000" />
      <workItem from="1757323410688" duration="79000" />
      <workItem from="1757323522280" duration="1765000" />
      <workItem from="1757402171942" duration="4165000" />
      <workItem from="1757406969397" duration="2200000" />
      <workItem from="1757409256105" duration="1686000" />
      <workItem from="1757411281986" duration="394000" />
      <workItem from="1757415186880" duration="7852000" />
      <workItem from="1757423549752" duration="1159000" />
      <workItem from="1757424895166" duration="1414000" />
      <workItem from="1757426353358" duration="31000" />
      <workItem from="1757426465546" duration="202000" />
      <workItem from="1757428648150" duration="246000" />
      <workItem from="1757471358855" duration="3684000" />
      <workItem from="1757481147213" duration="11243000" />
      <workItem from="1757493169600" duration="1505000" />
      <workItem from="1757494725656" duration="371000" />
      <workItem from="1757502346180" duration="2145000" />
      <workItem from="1757504533851" duration="2768000" />
      <workItem from="1757507485938" duration="1535000" />
      <workItem from="1757509042451" duration="3053000" />
      <workItem from="1757513886548" duration="2457000" />
      <workItem from="1757563536595" duration="6879000" />
      <workItem from="1757581448112" duration="1141000" />
      <workItem from="1757582608527" duration="90000" />
      <workItem from="1757585867120" duration="3266000" />
      <workItem from="1757589250970" duration="10164000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>