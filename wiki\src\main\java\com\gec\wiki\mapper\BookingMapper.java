package com.gec.wiki.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gec.wiki.pojo.Booking;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;

/**
 * 预约数据访问层
 */
@Mapper
public interface BookingMapper extends BaseMapper<Booking> {
    
    /**
     * 根据用户ID查询预约列表
     */
    List<Booking> findByUserId(@Param("userId") Long userId);
    
    /**
     * 根据日期和技师ID查询已预约的时间段
     */
    List<LocalTime> findBookedTimesByDateAndTechnician(@Param("date") LocalDate date, 
                                                      @Param("technicianId") Long technicianId);
    
    /**
     * 根据日期查询已预约的时间段
     */
    List<LocalTime> findBookedTimesByDate(@Param("date") LocalDate date);
    
    /**
     * 检查时间段是否可用
     */
    int checkTimeAvailable(@Param("date") LocalDate date, 
                          @Param("time") LocalTime time, 
                          @Param("technicianId") Long technicianId);
    
    /**
     * 获取维修店今日预约列表
     */
    List<Booking> findTodayBookingsByShopId(@Param("shopId") Long shopId, 
                                           @Param("today") LocalDate today);
    
    /**
     * 获取维修店预约统计
     */
    Map<String, Object> getBookingStatsByShopId(@Param("shopId") Long shopId);
    
    /**
     * 查询维修店的预约列表（分页）
     */
    List<Map<String, Object>> findBookingsForShop(@Param("shopId") Long shopId, 
                                                  @Param("offset") Integer offset, 
                                                  @Param("limit") Integer limit, 
                                                  @Param("status") Integer status);
    
    /**
     * 统计维修店的预约总数
     */
    int countBookingsForShop(@Param("shopId") Long shopId, 
                           @Param("status") Integer status);
}
