<template>
  <div class="admin-reports">
    <div class="page-header">
      <h1>数据报告</h1>
      <p>查看平台运营数据和统计分析</p>
    </div>

    <a-row :gutter="24">
      <a-col :span="24">
        <a-card title="平台概览统计">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-statistic title="总用户数" :value="stats.totalUsers" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="总订单数" :value="stats.totalOrders" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="总收入" :value="stats.totalRevenue" prefix="¥" />
            </a-col>
            <a-col :span="6">
              <a-statistic title="活跃维修店" :value="stats.activeShops" />
            </a-col>
          </a-row>
        </a-card>
      </a-col>
    </a-row>

    <a-row :gutter="24" style="margin-top: 24px;">
      <a-col :span="12">
        <a-card title="用户增长趋势">
          <div class="chart-placeholder">
            <p>用户增长趋势图（需要集成图表库）</p>
          </div>
        </a-card>
      </a-col>
      <a-col :span="12">
        <a-card title="收入趋势">
          <div class="chart-placeholder">
            <p>收入趋势图（需要集成图表库）</p>
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue';

export default defineComponent({
  name: 'AdminReports',
  setup() {
    const stats = ref({
      totalUsers: 1248,
      totalOrders: 3456,
      totalRevenue: 856300,
      activeShops: 89
    });

    return {
      stats
    };
  }
});
</script>

<style scoped>
.admin-reports {
  padding: 24px;
  flex: 1;
  background: #f0f2f5;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #333;
}

.page-header p {
  margin: 0;
  color: #666;
}

.chart-placeholder {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 4px;
}
</style>
