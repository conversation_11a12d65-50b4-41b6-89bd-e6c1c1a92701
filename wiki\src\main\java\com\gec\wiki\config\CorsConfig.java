package com.gec.wiki.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.io.File;

@Configuration
public class CorsConfig implements WebMvcConfigurer {

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")
                .allowedOriginPatterns("*")//allowedOriginPatterns("*")高版本使用这个
                .allowedHeaders(CorsConfiguration.ALL)
                .allowedMethods(CorsConfiguration.ALL)
                .allowCredentials(true)
                .maxAge(3600); // 1小时内不需要再预检（发OPTIONS请求）
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 配置静态资源访问
        String projectPath = System.getProperty("user.dir");

        // 构建图片目录路径
        String imagePath = "file:" + projectPath + File.separator + "web" + File.separator + "public" + File.separator + "image" + File.separator;

        System.out.println("📁 当前工作目录：" + projectPath);
        System.out.println("📁 配置图片静态资源路径：" + imagePath);

        // 检查目录是否存在
        File imageDir = new File(projectPath + File.separator + "web" + File.separator + "public" + File.separator + "image");
        System.out.println("📁 图片目录是否存在：" + imageDir.exists());
        System.out.println("📁 图片目录绝对路径：" + imageDir.getAbsolutePath());

        registry.addResourceHandler("/image/**")
                .addResourceLocations(imagePath)
                .setCachePeriod(3600); // 缓存1小时
    }

}
