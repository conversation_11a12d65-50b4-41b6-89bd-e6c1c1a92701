-- 为service表添加shop_id字段以支持维修店服务管理
-- 执行时间: 2024-01-20

-- 添加shop_id字段
ALTER TABLE service 
ADD COLUMN shop_id BIGINT NULL COMMENT '维修店ID(空表示平台服务)' 
AFTER status;

-- 为shop_id字段添加索引以提高查询性能
CREATE INDEX idx_service_shop_id ON service(shop_id);

-- 为现有数据设置默认值（可选，如果需要将现有服务归属到某个维修店）
-- UPDATE service SET shop_id = 1 WHERE shop_id IS NULL;

-- 如果需要外键约束（取决于是否有shop表）
-- ALTER TABLE service 
-- ADD CONSTRAINT fk_service_shop_id 
-- FOREIGN KEY (shop_id) REFERENCES users(id) 
-- ON DELETE SET NULL;
