package com.gec.wiki.pojo.req;

import java.math.BigDecimal;

/**
 * 服务保存请求类
 */
public class ServiceSaveReq {
    
    /**
     * id
     */
    private Long id;
    
    /**
     * 服务名称
     */
    private String name;
    
    /**
     * 一级分类
     */
    private Long category1Id;
    
    /**
     * 二级分类
     */
    private Long category2Id;
    
    /**
     * 服务描述
     */
    private String description;
    
    /**
     * 服务详细内容
     */
    private String content;
    
    /**
     * 服务图片
     */
    private String cover;
    
    /**
     * 服务图片集(JSON格式)
     */
    private String images;
    
    /**
     * 服务价格
     */
    private BigDecimal price;
    
    /**
     * 原价
     */
    private BigDecimal originalPrice;
    
    /**
     * 服务时长(分钟)
     */
    private Integer duration;
    
    /**
     * 是否推荐(0-否 1-是)
     */
    private Integer isRecommend;
    
    /**
     * 状态(0-下架 1-上架)
     */
    private Integer status;
    
    /**
     * 维修店ID(空表示平台服务)
     */
    private Long shopId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getCategory1Id() {
        return category1Id;
    }

    public void setCategory1Id(Long category1Id) {
        this.category1Id = category1Id;
    }

    public Long getCategory2Id() {
        return category2Id;
    }

    public void setCategory2Id(Long category2Id) {
        this.category2Id = category2Id;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getCover() {
        return cover;
    }

    public void setCover(String cover) {
        this.cover = cover;
    }

    public String getImages() {
        return images;
    }

    public void setImages(String images) {
        this.images = images;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getOriginalPrice() {
        return originalPrice;
    }

    public void setOriginalPrice(BigDecimal originalPrice) {
        this.originalPrice = originalPrice;
    }

    public Integer getDuration() {
        return duration;
    }

    public void setDuration(Integer duration) {
        this.duration = duration;
    }

    public Integer getIsRecommend() {
        return isRecommend;
    }

    public void setIsRecommend(Integer isRecommend) {
        this.isRecommend = isRecommend;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    @Override
    public String toString() {
        return "ServiceSaveReq{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", category1Id=" + category1Id +
                ", category2Id=" + category2Id +
                ", description='" + description + '\'' +
                ", content='" + content + '\'' +
                ", cover='" + cover + '\'' +
                ", images='" + images + '\'' +
                ", price=" + price +
                ", originalPrice=" + originalPrice +
                ", duration=" + duration +
                ", isRecommend=" + isRecommend +
                ", status=" + status +
                ", shopId=" + shopId +
                '}';
    }
}
