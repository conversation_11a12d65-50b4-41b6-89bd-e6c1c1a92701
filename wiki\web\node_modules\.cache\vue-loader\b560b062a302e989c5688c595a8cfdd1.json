{"remainingRequest": "D:\\JavaCar\\wiki\\wiki\\web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\JavaCar\\wiki\\wiki\\web\\src\\views\\Register.vue?vue&type=template&id=63ae9146&scoped=true&ts=true", "dependencies": [{"path": "D:\\JavaCar\\wiki\\wiki\\web\\src\\views\\Register.vue", "mtime": 1757594543302}, {"path": "D:\\JavaCar\\wiki\\wiki\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750678170000}, {"path": "D:\\JavaCar\\wiki\\wiki\\web\\node_modules\\ts-loader\\index.js", "mtime": 1750678172000}, {"path": "D:\\JavaCar\\wiki\\wiki\\web\\node_modules\\vue-loader-v16\\dist\\templateLoader.js", "mtime": 1750678172000}, {"path": "D:\\JavaCar\\wiki\\wiki\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750678170000}, {"path": "D:\\JavaCar\\wiki\\wiki\\web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1750678172000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\JavaCar\\wiki\\wiki\\web\\src\\views\\Register.vue"], "names": [], "mappings": ";EACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1B,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACrD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB;QACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB;YACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBACjC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBAChC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC5C,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACtC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;gBAClC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACrB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;kBACjC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC7C,CAAC,CAAC,CAAC,CAAC,CAAC;cACP,CAAC,CAAC,CAAC,CAAC,CAAC;YACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB;YACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB;YACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB;YACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB;YACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB;YACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACb;UACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB;YACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACpC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB;YACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACtB;YACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxB;YACE,CAAC;UACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEb,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC;UACJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACV,CAAC,CAAC,CAAC,CAAC,CAAC;;IAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB;MACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAElD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEtC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAErC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAErC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEhC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAElC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEvC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAErC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChD,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACvE,CAAC;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAET,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB;MACE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,CAAC,CAAC,CAAC,CAAC;;QAEJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpB,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC,CAAC;;QAEJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC;;QAEJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC;;QAEJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACrB,CAAC,CAAC,CAAC,CAAC;;QAEJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtB,CAAC,CAAC,CAAC,CAAC;;QAEJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACzB,CAAC,CAAC,CAAC,CAAC;;QAEJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC;;QAEJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACd,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC;;QAEJ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC,CAAC,CAAC,CAAC;;QAEJ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAEnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;UACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACjC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3B,CAAC,CAAC,CAAC,CAAC;;QAEJ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChD,CAAC,CAAC,CAAC,CAAC,CAAC;;MAEL,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;UACvE,CAAC;QACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC", "file": "D:/JavaCar/wiki/wiki/web/src/views/Register.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  <div class=\"register-container\">\n    <div class=\"register-box\">\n      <div class=\"register-header\">\n        <img src=\"/images/logo.png\" alt=\"汽车维修\" class=\"logo\" />\n        <h2>注册汽车维修服务平台</h2>\n        <p>加入我们，享受专业的汽车维修服务</p>\n      </div>\n      \n      <a-form\n        ref=\"registerFormRef\"\n        :model=\"registerForm\"\n        :rules=\"registerRules\"\n        @finish=\"handleRegister\"\n        layout=\"vertical\"\n        class=\"register-form\"\n      >\n        <!-- 用户类型选择 -->\n        <a-form-item name=\"userType\" label=\"用户类型\">\n          <a-radio-group\n            v-model:value=\"registerForm.userType\"\n            size=\"large\"\n            class=\"user-type-radio\"\n          >\n            <a-radio :value=\"1\" class=\"radio-option\">\n              <div class=\"radio-content\">\n                <CarOutlined class=\"radio-icon\" />\n                <div class=\"radio-text\">\n                  <div class=\"radio-title\">车主</div>\n                  <div class=\"radio-desc\">预约维修服务，管理车辆信息</div>\n                </div>\n              </div>\n            </a-radio>\n            <a-radio :value=\"2\" class=\"radio-option\">\n              <div class=\"radio-content\">\n                <ShopOutlined class=\"radio-icon\" />\n                <div class=\"radio-text\">\n                  <div class=\"radio-title\">维修店</div>\n                  <div class=\"radio-desc\">提供维修服务，管理技师和订单</div>\n                </div>\n              </div>\n            </a-radio>\n          </a-radio-group>\n        </a-form-item>\n\n        <!-- 车主用户名字段 -->\n        <a-form-item \n          v-if=\"registerForm.userType === 1\" \n          name=\"username\" \n          label=\"用户名\"\n        >\n          <a-input\n            v-model:value=\"registerForm.username\"\n            size=\"large\"\n            placeholder=\"请输入用户名\"\n          >\n            <template #prefix>\n              <UserOutlined />\n            </template>\n          </a-input>\n        </a-form-item>\n\n        <!-- 维修店名字段 -->\n        <a-form-item \n          v-if=\"registerForm.userType === 2\" \n          name=\"shopName\" \n          label=\"维修店名\"\n        >\n          <a-input\n            v-model:value=\"registerForm.shopName\"\n            size=\"large\"\n            placeholder=\"请输入维修店名称\"\n          >\n            <template #prefix>\n              <ShopOutlined />\n            </template>\n          </a-input>\n        </a-form-item>\n        \n        <a-form-item name=\"realName\" label=\"真实姓名\">\n          <a-input\n            v-model:value=\"registerForm.realName\"\n            size=\"large\"\n            placeholder=\"请输入真实姓名\"\n          >\n            <template #prefix>\n              <IdcardOutlined />\n            </template>\n          </a-input>\n        </a-form-item>\n\n        <a-form-item name=\"phone\" label=\"手机号\">\n          <a-input\n            v-model:value=\"registerForm.phone\"\n            size=\"large\"\n            placeholder=\"请输入手机号\"\n          >\n            <template #prefix>\n              <PhoneOutlined />\n            </template>\n          </a-input>\n        </a-form-item>\n\n        <a-form-item name=\"email\" label=\"邮箱\">\n          <a-input\n            v-model:value=\"registerForm.email\"\n            size=\"large\"\n            placeholder=\"请输入邮箱（可选）\"\n          >\n            <template #prefix>\n              <MailOutlined />\n            </template>\n          </a-input>\n        </a-form-item>\n\n        <!-- 维修店地址字段 -->\n        <a-form-item \n          v-if=\"registerForm.userType === 2\" \n          name=\"shopAddress\" \n          label=\"地铺地址\"\n        >\n          <a-input\n            v-model:value=\"registerForm.shopAddress\"\n            size=\"large\"\n            placeholder=\"请输入维修店地址\"\n          >\n            <template #prefix>\n              <EnvironmentOutlined />\n            </template>\n          </a-input>\n        </a-form-item>\n        \n        <a-form-item name=\"password\" label=\"密码\">\n          <a-input-password\n            v-model:value=\"registerForm.password\"\n            size=\"large\"\n            placeholder=\"请输入密码\"\n          >\n            <template #prefix>\n              <LockOutlined />\n            </template>\n          </a-input-password>\n        </a-form-item>\n\n        <a-form-item name=\"confirmPassword\" label=\"确认密码\">\n          <a-input-password\n            v-model:value=\"registerForm.confirmPassword\"\n            size=\"large\"\n            placeholder=\"请再次输入密码\"\n          >\n            <template #prefix>\n              <LockOutlined />\n            </template>\n          </a-input-password>\n        </a-form-item>\n        \n        <a-form-item>\n          <a-checkbox v-model:checked=\"agreeTerms\">\n            我已阅读并同意 <a href=\"#\" @click.prevent=\"showTerms\">《用户协议》</a> 和 <a href=\"#\" @click.prevent=\"showPrivacy\">《隐私政策》</a>\n          </a-checkbox>\n        </a-form-item>\n        \n        <a-form-item>\n          <a-button\n            type=\"primary\"\n            html-type=\"submit\"\n            size=\"large\"\n            :loading=\"loading\"\n            :disabled=\"!agreeTerms\"\n            class=\"register-button\"\n          >\n            注册\n          </a-button>\n        </a-form-item>\n        \n        <div class=\"login-link\">\n          已有账户？ \n          <router-link to=\"/login\">立即登录</router-link>\n        </div>\n      </a-form>\n    </div>\n\n    <!-- 用户协议模态框 -->\n    <a-modal\n      v-model:visible=\"userAgreementVisible\"\n      title=\"用户协议\"\n      width=\"800px\"\n      :footer=\"null\"\n      class=\"agreement-modal\"\n    >\n      <div class=\"agreement-content\">\n        <h3>汽车维修服务平台用户协议</h3>\n        \n        <h4>第一条 协议的范围和效力</h4>\n        <p>1.1 本协议是您与汽车维修服务平台之间关于使用平台服务所订立的协议。</p>\n        <p>1.2 您通过注册、登录、使用平台服务，即表示您已阅读、理解并完全接受本协议的全部条款。</p>\n        \n        <h4>第二条 服务内容</h4>\n        <p>2.1 平台为车主用户提供汽车维修服务预约、维修店查找、服务评价等功能。</p>\n        <p>2.2 平台为维修店用户提供服务发布、订单管理、客户沟通等功能。</p>\n        <p>2.3 平台有权根据业务发展需要，增加、修改或终止部分服务功能。</p>\n        \n        <h4>第三条 用户注册和账户管理</h4>\n        <p>3.1 用户应提供真实、准确、完整的注册信息。</p>\n        <p>3.2 用户对账户和密码的安全负有责任，因账户被盗用造成的损失由用户自行承担。</p>\n        <p>3.3 用户不得将账户转让、出售或以其他方式提供给第三方使用。</p>\n        \n        <h4>第四条 用户行为规范</h4>\n        <p>4.1 用户应遵守国家法律法规，不得利用平台从事违法违规活动。</p>\n        <p>4.2 用户不得发布虚假信息、恶意评价或进行其他损害平台声誉的行为。</p>\n        <p>4.3 维修店用户应确保提供的服务符合相关技术标准和安全要求。</p>\n        \n        <h4>第五条 服务费用和支付</h4>\n        <p>5.1 平台基础服务免费提供，部分增值服务可能收取相应费用。</p>\n        <p>5.2 维修服务费用由车主与维修店直接结算，平台不参与资金交易。</p>\n        <p>5.3 平台有权调整收费标准，并提前30天通知用户。</p>\n        \n        <h4>第六条 知识产权</h4>\n        <p>6.1 平台的所有内容，包括但不限于文字、图片、软件等，均受知识产权法保护。</p>\n        <p>6.2 用户在平台发布的内容，应确保不侵犯他人知识产权。</p>\n        \n        <h4>第七条 免责声明</h4>\n        <p>7.1 平台仅提供信息发布和匹配服务，对维修服务的质量不承担直接责任。</p>\n        <p>7.2 因不可抗力、系统故障等原因导致的服务中断，平台不承担责任。</p>\n        \n        <h4>第八条 协议修改和终止</h4>\n        <p>8.1 平台有权修改本协议，修改后的协议将在平台公布。</p>\n        <p>8.2 用户违反协议条款的，平台有权终止向该用户提供服务。</p>\n        \n        <h4>第九条 争议解决</h4>\n        <p>9.1 因本协议产生的争议，双方应友好协商解决。</p>\n        <p>9.2 协商不成的，任何一方均可向平台所在地人民法院提起诉讼。</p>\n        \n        <p class=\"effective-date\">本协议自2024年1月1日起生效。</p>\n      </div>\n      \n      <div class=\"modal-footer\">\n        <a-button type=\"primary\" @click=\"userAgreementVisible = false\">\n          我已阅读并同意\n        </a-button>\n        <a-button @click=\"userAgreementVisible = false\" style=\"margin-left: 8px;\">\n          关闭\n        </a-button>\n      </div>\n    </a-modal>\n\n    <!-- 隐私政策模态框 -->\n    <a-modal\n      v-model:visible=\"privacyPolicyVisible\"\n      title=\"隐私政策\"\n      width=\"800px\"\n      :footer=\"null\"\n      class=\"agreement-modal\"\n    >\n      <div class=\"agreement-content\">\n        <h3>汽车维修服务平台隐私政策</h3>\n        \n        <h4>第一条 信息收集</h4>\n        <p>1.1 我们收集您主动提供的信息：</p>\n        <ul>\n          <li>注册信息：用户名、真实姓名、手机号、邮箱等</li>\n          <li>车主用户：车辆信息、维修历史等</li>\n          <li>维修店用户：店铺信息、服务项目、营业执照等</li>\n        </ul>\n        \n        <p>1.2 我们自动收集的信息：</p>\n        <ul>\n          <li>设备信息：设备型号、操作系统、应用版本等</li>\n          <li>日志信息：IP地址、访问时间、操作记录等</li>\n          <li>位置信息：用于提供附近维修店推荐服务</li>\n        </ul>\n        \n        <h4>第二条 信息使用</h4>\n        <p>2.1 提供和改善服务：</p>\n        <ul>\n          <li>为您匹配合适的维修服务</li>\n          <li>处理您的服务请求和投诉</li>\n          <li>改善用户体验和服务质量</li>\n        </ul>\n        \n        <p>2.2 安全保护：</p>\n        <ul>\n          <li>验证用户身份，防止欺诈行为</li>\n          <li>检测和预防安全威胁</li>\n          <li>维护平台运行安全</li>\n        </ul>\n        \n        <p>2.3 法律义务：</p>\n        <ul>\n          <li>遵守适用的法律法规要求</li>\n          <li>配合监管部门的合法调查</li>\n        </ul>\n        \n        <h4>第三条 信息共享</h4>\n        <p>3.1 我们不会向第三方出售、租赁或交易您的个人信息。</p>\n        <p>3.2 在以下情况下，我们可能共享您的信息：</p>\n        <ul>\n          <li>获得您的明确同意</li>\n          <li>为完成服务匹配（如向维修店提供车主联系方式）</li>\n          <li>法律法规要求或政府部门要求</li>\n          <li>保护平台和用户的合法权益</li>\n        </ul>\n        \n        <h4>第四条 信息存储和安全</h4>\n        <p>4.1 数据存储：</p>\n        <ul>\n          <li>您的信息将存储在中国境内的服务器上</li>\n          <li>我们将在必要期限内保留您的信息</li>\n        </ul>\n        \n        <p>4.2 安全措施：</p>\n        <ul>\n          <li>采用行业标准的加密技术保护数据传输</li>\n          <li>建立严格的数据访问权限控制</li>\n          <li>定期进行安全评估和漏洞检测</li>\n        </ul>\n        \n        <h4>第五条 您的权利</h4>\n        <p>5.1 您有权：</p>\n        <ul>\n          <li>查询、更正您的个人信息</li>\n          <li>删除您的个人信息（法律法规另有规定的除外）</li>\n          <li>撤回您的信息使用同意</li>\n          <li>要求我们停止处理您的个人信息</li>\n        </ul>\n        \n        <p>5.2 行使权利方式：</p>\n        <ul>\n          <li>通过平台设置页面进行操作</li>\n          <li>联系客服热线：400-123-4567</li>\n          <li>发送邮件至：<EMAIL></li>\n        </ul>\n        \n        <h4>第六条 未成年人保护</h4>\n        <p>6.1 我们不会主动收集未满18周岁的未成年人个人信息。</p>\n        <p>6.2 如发现收集了未成年人信息，我们将立即删除相关数据。</p>\n        \n        <h4>第七条 政策更新</h4>\n        <p>7.1 我们可能不时更新本隐私政策。</p>\n        <p>7.2 重大变更将通过平台公告或其他方式通知您。</p>\n        <p>7.3 继续使用服务即视为您接受更新后的政策。</p>\n        \n        <h4>第八条 联系我们</h4>\n        <p>如果您对本隐私政策有任何疑问，请通过以下方式联系我们：</p>\n        <ul>\n          <li>客服热线：400-123-4567</li>\n          <li>邮箱：<EMAIL></li>\n          <li>地址：北京市朝阳区某某街道123号</li>\n        </ul>\n        \n        <p class=\"effective-date\">本政策自2024年1月1日起生效。</p>\n      </div>\n      \n      <div class=\"modal-footer\">\n        <a-button type=\"primary\" @click=\"privacyPolicyVisible = false\">\n          我已阅读并了解\n        </a-button>\n        <a-button @click=\"privacyPolicyVisible = false\" style=\"margin-left: 8px;\">\n          关闭\n        </a-button>\n      </div>\n    </a-modal>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent, ref, onMounted, watch } from 'vue';\nimport { \n  UserOutlined, \n  LockOutlined, \n  PhoneOutlined, \n  MailOutlined,\n  IdcardOutlined,\n  CarOutlined,\n  ShopOutlined,\n  EnvironmentOutlined\n} from '@ant-design/icons-vue';\nimport { message } from 'ant-design-vue';\nimport axios from 'axios';\nimport { useRouter } from 'vue-router';\n\nexport default defineComponent({\n  name: 'Register',\n  components: {\n    UserOutlined,\n    LockOutlined,\n    PhoneOutlined,\n    MailOutlined,\n    IdcardOutlined,\n    CarOutlined,\n    ShopOutlined,\n    EnvironmentOutlined\n  },\n  setup() {\n    const router = useRouter();\n    const loading = ref(false);\n    const agreeTerms = ref(false);\n    const userAgreementVisible = ref(false);\n    const privacyPolicyVisible = ref(false);\n    const registerFormRef = ref();\n\n    const registerForm = ref({\n      username: '',\n      shopName: '',\n      realName: '',\n      phone: '',\n      email: '',\n      shopAddress: '',\n      password: '',\n      confirmPassword: '',\n      userType: 1 // 默认选择车主\n    });\n\n    // 安全设置\n    const securitySettings = ref({\n      minPasswordLength: 6,\n      requireComplexPassword: false\n    });\n    \n    // 加载安全设置\n    const loadSecuritySettings = async () => {\n      try {\n        const response = await axios.get('/api/admin/security-settings');\n        if (response.data.success) {\n          securitySettings.value = response.data.content;\n        }\n      } catch (error) {\n        console.error('加载安全设置失败:', error);\n      }\n    };\n\n    \n    // 创建验证规则\n    const createValidationRules = () => ({\n      realName: [\n        { required: true, message: '请输入真实姓名', trigger: 'blur' }\n      ],\n      phone: [\n        { required: true, message: '请输入手机号', trigger: 'blur' },\n        { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }\n      ],\n      email: [\n        { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }\n      ],\n      userType: [\n        { required: true, message: '请选择用户类型', trigger: 'change' }\n      ],\n      password: [\n        { required: true, message: '请输入密码', trigger: 'blur' },\n        { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }\n      ],\n        confirmPassword: [\n          { required: true, message: '请确认密码', trigger: 'blur' },\n          {\n            validator: (_rule: any, value: string) => {\n              if (!value) return Promise.resolve();\n              if (value !== registerForm.value.password) {\n                return Promise.reject(new Error('两次输入的密码不一致'));\n              }\n              return Promise.resolve();\n            },\n            trigger: 'blur'\n          }\n        ],\n      username: [\n        { required: true, message: '请输入用户名', trigger: 'blur' },\n        { min: 3, max: 20, message: '用户名长度为3-20个字符', trigger: 'blur' }\n      ],\n      shopName: [\n        { required: true, message: '请输入维修店名', trigger: 'blur' },\n        { min: 2, max: 50, message: '维修店名长度为2-50个字符', trigger: 'blur' }\n      ],\n      shopAddress: [\n        { required: true, message: '请输入地铺地址', trigger: 'blur' },\n        { min: 5, max: 200, message: '地址长度为5-200个字符', trigger: 'blur' }\n      ]\n    });\n\n    const registerRules = ref(createValidationRules());\n    \n    const handleRegister = async () => {\n      if (!agreeTerms.value) {\n        message.warning('请先同意用户协议和隐私政策');\n        return;\n      }\n      \n      loading.value = true;\n      try {\n        // 根据用户类型准备不同的数据\n        const requestData: any = {\n          realName: registerForm.value.realName,\n          phone: registerForm.value.phone,\n          email: registerForm.value.email,\n          password: registerForm.value.password,\n          userType: registerForm.value.userType\n        };\n\n        if (registerForm.value.userType === 1) {\n          // 车主用户\n          requestData.username = registerForm.value.username;\n        } else if (registerForm.value.userType === 2) {\n          // 维修店用户\n          requestData.shopName = registerForm.value.shopName;\n          requestData.shopAddress = registerForm.value.shopAddress;\n          requestData.username = registerForm.value.shopName; // 使用店名作为用户名\n        }\n\n        const response = await axios.post('/auth/register', requestData);\n        const data = response.data;\n        \n        if (data.success) {\n          message.success('注册成功，请登录');\n          router.push('/login');\n        } else {\n          message.error(data.message || '注册失败');\n        }\n      } catch (error) {\n        message.error('注册失败，请检查网络连接');\n        console.error('Register error:', error);\n      } finally {\n        loading.value = false;\n      }\n    };\n    \n    const showTerms = () => {\n      userAgreementVisible.value = true;\n    };\n    \n    const showPrivacy = () => {\n      privacyPolicyVisible.value = true;\n    };\n\n    // 监听密码变化，重新验证确认密码\n    watch(\n      () => registerForm.value.password,\n      () => {\n        // 当密码改变时，如果确认密码已有值，则清除验证状态\n        if (registerForm.value.confirmPassword && registerFormRef.value) {\n          registerFormRef.value.clearValidate(['confirmPassword']);\n        }\n      }\n    );\n\n    // 组件挂载时加载安全设置\n    onMounted(() => {\n      loadSecuritySettings();\n    });\n\n    return {\n      registerForm,\n      registerFormRef,\n      registerRules,\n      loading,\n      agreeTerms,\n      securitySettings,\n      userAgreementVisible,\n      privacyPolicyVisible,\n      handleRegister,\n      showTerms,\n      showPrivacy\n    };\n  }\n});\n</script>\n\n<style scoped>\n.register-container {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n}\n\n.register-box {\n  background: white;\n  padding: 40px;\n  border-radius: 12px;\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);\n  width: 100%;\n  max-width: 450px;\n  max-height: 90vh;\n  overflow-y: auto;\n}\n\n.register-header {\n  text-align: center;\n  margin-bottom: 32px;\n}\n\n.logo {\n  width: 60px;\n  height: 60px;\n  margin-bottom: 16px;\n}\n\n.register-header h2 {\n  color: #333;\n  margin-bottom: 8px;\n  font-size: 24px;\n}\n\n.register-header p {\n  color: #666;\n  margin-bottom: 0;\n}\n\n.register-form {\n  margin-top: 32px;\n}\n\n.register-button {\n  width: 100%;\n  height: 44px;\n  font-size: 16px;\n}\n\n.login-link {\n  text-align: center;\n  margin-top: 24px;\n  color: #666;\n}\n\n.login-link a {\n  color: #1890ff;\n  text-decoration: none;\n}\n\n.login-link a:hover {\n  text-decoration: underline;\n}\n\n.ant-checkbox-wrapper a {\n  color: #1890ff;\n  text-decoration: none;\n}\n\n.ant-checkbox-wrapper a:hover {\n  text-decoration: underline;\n}\n\n/* 用户类型选择样式 */\n.user-type-radio {\n  width: 100%;\n}\n\n.radio-option {\n  width: 100%;\n  margin-bottom: 12px;\n  padding: 16px;\n  border: 2px solid #e8e8e8;\n  border-radius: 8px;\n  transition: all 0.3s;\n  display: flex;\n  align-items: flex-start;\n}\n\n.radio-option:hover {\n  border-color: #1890ff;\n  background-color: #f0f5ff;\n}\n\n.radio-option.ant-radio-wrapper-checked {\n  border-color: #1890ff;\n  background-color: #f0f5ff;\n  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n}\n\n.radio-content {\n  display: flex;\n  align-items: center;\n  width: 100%;\n  margin-left: 8px;\n}\n\n.radio-icon {\n  font-size: 24px;\n  color: #1890ff;\n  margin-right: 12px;\n}\n\n.radio-text {\n  flex: 1;\n}\n\n.radio-title {\n  font-weight: 600;\n  font-size: 16px;\n  color: #333;\n  margin-bottom: 4px;\n}\n\n.radio-desc {\n  font-size: 14px;\n  color: #666;\n  line-height: 1.4;\n}\n\n/* 协议模态框样式 */\n.agreement-modal .ant-modal-body {\n  max-height: 60vh;\n  overflow-y: auto;\n  padding: 24px;\n}\n\n.agreement-content {\n  line-height: 1.6;\n  color: #333;\n}\n\n.agreement-content h3 {\n  text-align: center;\n  color: #1890ff;\n  margin-bottom: 24px;\n  font-size: 20px;\n  font-weight: 600;\n}\n\n.agreement-content h4 {\n  color: #333;\n  margin: 20px 0 12px 0;\n  font-size: 16px;\n  font-weight: 600;\n  border-left: 4px solid #1890ff;\n  padding-left: 12px;\n}\n\n.agreement-content p {\n  margin: 8px 0;\n  text-indent: 0;\n  font-size: 14px;\n}\n\n.agreement-content ul {\n  margin: 8px 0;\n  padding-left: 20px;\n}\n\n.agreement-content li {\n  margin: 4px 0;\n  font-size: 14px;\n}\n\n.effective-date {\n  text-align: center;\n  font-weight: 600;\n  color: #666;\n  margin-top: 24px;\n  padding-top: 16px;\n  border-top: 1px solid #e8e8e8;\n}\n\n.modal-footer {\n  text-align: center;\n  padding: 16px 0;\n  border-top: 1px solid #e8e8e8;\n  margin-top: 16px;\n}\n</style>\n"]}]}