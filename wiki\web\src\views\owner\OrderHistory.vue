<template>
  <div class="order-history">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1>
            <HistoryOutlined />
            维修记录
          </h1>
          <p>查看您的维修历史记录，掌握爱车服务状态</p>
          <div class="order-stats">
            <div class="stat-item">
              <span class="stat-number">{{ orders.length }}</span>
              <span class="stat-label">总订单数</span>
            </div>
            <div class="stat-divider"></div>
            <div class="stat-item">
              <span class="stat-number">{{ completedOrders }}</span>
              <span class="stat-label">已完成</span>
            </div>
            <div class="stat-divider"></div>
            <div class="stat-item">
              <span class="stat-number">¥{{ totalSpent.toLocaleString() }}</span>
              <span class="stat-label">总消费</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选区域 -->
    <div class="filter-container">
      <a-card class="filter-card">
        <div class="filter-section">
          <div class="filter-row">
            <div class="filter-item">
              <label>订单状态</label>
              <a-select
                v-model:value="filterForm.status"
                placeholder="全部状态"
                allowClear
                style="width: 140px"
              >
                <a-select-option :value="1">待确认</a-select-option>
                <a-select-option :value="2">已确认</a-select-option>
                <a-select-option :value="3">服务中</a-select-option>
                <a-select-option :value="4">已完成</a-select-option>
                <a-select-option :value="5">已取消</a-select-option>
              </a-select>
            </div>
            
            <div class="filter-item">
              <label>时间范围</label>
              <a-range-picker 
                v-model:value="filterForm.dateRange"
                style="width: 240px"
              />
            </div>
            
            <div class="filter-item">
              <label>服务类型</label>
              <a-select
                v-model:value="filterForm.serviceType"
                placeholder="全部服务"
                allowClear
                style="width: 140px"
              >
                <a-select-option value="maintenance">保养维护</a-select-option>
                <a-select-option value="repair">故障维修</a-select-option>
                <a-select-option value="inspection">检测服务</a-select-option>
                <a-select-option value="beauty">美容清洗</a-select-option>
              </a-select>
            </div>
            
            <div class="filter-actions">
              <a-button type="primary" @click="handleFilter">
                <SearchOutlined />
                筛选
              </a-button>
              <a-button @click="resetFilter">
                <ReloadOutlined />
                重置
              </a-button>
            </div>
          </div>
        </div>
      </a-card>
    </div>

    <!-- 订单列表 -->
    <div class="orders-container">
      <div v-if="orders.length === 0" class="empty-state">
        <div class="empty-illustration">
          <FileTextOutlined />
        </div>
        <h2>暂无订单记录</h2>
        <p>您还没有任何维修订单，去预约服务吧</p>
        <a-button type="primary" size="large">
          <CalendarOutlined />
          立即预约
        </a-button>
      </div>

      <div v-else class="orders-list">
        <div 
          v-for="item in orders" 
          :key="item.id"
          class="order-card"
        >
          <div class="order-header">
            <div class="order-avatar">
              <a-avatar :size="48" :style="{ backgroundColor: getStatusColor(item.status) }">
                <ToolOutlined />
              </a-avatar>
            </div>
            
            <div class="order-basic-info">
              <h3>{{ item.serviceName }}</h3>
              <div class="shop-info">
                <ShopOutlined />
                <span>{{ item.shopName }}</span>
              </div>
              <div class="order-number">
                订单号：{{ item.orderNo }}
              </div>
            </div>
            
            <div class="order-status">
              <a-tag 
                :color="getStatusColor(item.status)"
                class="status-tag"
              >
                {{ getStatusText(item.status) }}
              </a-tag>
              <div class="order-amount">¥{{ item.amount }}</div>
            </div>
          </div>
          
          <div class="order-details">
            <div class="detail-grid">
              <div class="detail-item">
                <div class="detail-icon vehicle">
                  <CarOutlined />
                </div>
                <div class="detail-info">
                  <span class="detail-label">服务车辆</span>
                  <span class="detail-value">{{ item.vehicleInfo }}</span>
                </div>
              </div>
              
              <div class="detail-item">
                <div class="detail-icon time">
                  <ClockCircleOutlined />
                </div>
                <div class="detail-info">
                  <span class="detail-label">预约时间</span>
                  <span class="detail-value">{{ item.bookingTime }}</span>
                </div>
              </div>
              
              <div class="detail-item">
                <div class="detail-icon service">
                  <SettingOutlined />
                </div>
                <div class="detail-info">
                  <span class="detail-label">服务内容</span>
                  <span class="detail-value">{{ item.serviceDescription }}</span>
                </div>
              </div>
              
              <div class="detail-item">
                <div class="detail-icon date">
                  <CalendarOutlined />
                </div>
                <div class="detail-info">
                  <span class="detail-label">创建时间</span>
                  <span class="detail-value">{{ item.createTime }}</span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="order-actions">
            <a-button @click="viewDetail(item.id)">
              <EyeOutlined />
              查看详情
            </a-button>
            
            <a-button 
              v-if="item.status === 4" 
              type="primary"
              ghost
              @click="rateService(item.id)"
            >
              <StarOutlined />
              评价服务
            </a-button>
            
            <a-button 
              v-if="[1,2].includes(item.status)"
              danger
              @click="cancelOrder(item.id)"
            >
              <CloseOutlined />
              取消订单
            </a-button>
            
            <a-dropdown v-if="item.status === 4">
              <template #overlay>
                <a-menu>
                  <a-menu-item key="1">
                    <DownloadOutlined />
                    下载发票
                  </a-menu-item>
                  <a-menu-item key="2">
                    <CopyOutlined />
                    复制订单号
                  </a-menu-item>
                  <a-menu-divider />
                  <a-menu-item key="3">
                    <ReloadOutlined />
                    再次预约
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button>
                <MoreOutlined />
              </a-button>
            </a-dropdown>
          </div>
        </div>
      </div>
      
      <!-- 分页 -->
      <div v-if="orders.length > 0" class="pagination-container">
        <a-pagination
          v-model:current="pagination.current"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :show-size-changer="true"
          :show-quick-jumper="true"
          :show-total="(total) => `共 ${total} 条记录`"
          size="default"
        />
      </div>
    </div>

    <!-- 评价弹窗 -->
    <a-modal
      v-model:open="rateModalVisible"
      title="服务评价"
      @ok="submitRating"
      @cancel="rateModalVisible = false"
    >
      <a-form layout="vertical">
        <a-form-item label="服务评分">
          <a-rate v-model:value="ratingForm.rating" />
        </a-form-item>
        <a-form-item label="评价内容">
          <a-textarea
            v-model:value="ratingForm.comment"
            placeholder="请分享您的服务体验"
            :rows="4"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted } from 'vue';
import { 
  SearchOutlined, 
  ToolOutlined,
  HistoryOutlined,
  ReloadOutlined,
  FileTextOutlined,
  CalendarOutlined,
  ShopOutlined,
  CarOutlined,
  ClockCircleOutlined,
  SettingOutlined,
  EyeOutlined,
  StarOutlined,
  CloseOutlined,
  DownloadOutlined,
  CopyOutlined,
  MoreOutlined
} from '@ant-design/icons-vue';
import { message, Modal } from 'ant-design-vue';

export default defineComponent({
  name: 'OrderHistory',
  components: {
    SearchOutlined,
    ToolOutlined,
    HistoryOutlined,
    ReloadOutlined,
    FileTextOutlined,
    CalendarOutlined,
    ShopOutlined,
    CarOutlined,
    ClockCircleOutlined,
    SettingOutlined,
    EyeOutlined,
    StarOutlined,
    CloseOutlined,
    DownloadOutlined,
    CopyOutlined,
    MoreOutlined
  },
  setup() {
    const loading = ref(false);
    const orders = ref<any[]>([]);
    const rateModalVisible = ref(false);
    const currentOrderId = ref<number | null>(null);

    const filterForm = ref({
      status: undefined,
      dateRange: undefined,
      serviceType: undefined
    });
    
    // 计算已完成订单数
    const completedOrders = computed(() => {
      return orders.value.filter(order => order.status === 4).length;
    });
    
    // 计算总消费
    const totalSpent = computed(() => {
      return orders.value
        .filter(order => order.status === 4)
        .reduce((total, order) => total + order.amount, 0);
    });

    const ratingForm = ref({
      rating: 5,
      comment: ''
    });

    const pagination = ref({
      current: 1,
      pageSize: 5,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total: number) => `共 ${total} 条记录`
    });

    const getStatusColor = (status: number) => {
      const colors: Record<number, string> = {
        1: 'orange', 2: 'blue', 3: 'cyan', 4: 'green', 5: 'red'
      };
      return colors[status] || 'default';
    };

    const getStatusText = (status: number) => {
      const texts: Record<number, string> = {
        1: '待确认', 2: '已确认', 3: '服务中', 4: '已完成', 5: '已取消'
      };
      return texts[status] || '未知';
    };

    const handleFilter = () => {
      pagination.value.current = 1;
      loadOrders();
    };

    const resetFilter = () => {
      filterForm.value = {
        status: undefined,
        dateRange: undefined,
        serviceType: undefined
      };
      handleFilter();
    };

    const viewDetail = (id: number) => {
      message.info(`查看订单详情 ${id}`);
    };

    const rateService = (id: number) => {
      currentOrderId.value = id;
      ratingForm.value = {
        rating: 5,
        comment: ''
      };
      rateModalVisible.value = true;
    };

    const submitRating = () => {
      // TODO: 实现评价API
      message.success('评价提交成功');
      rateModalVisible.value = false;
      loadOrders();
    };

    const cancelOrder = (id: number) => {
      Modal.confirm({
        title: '确认取消',
        content: '确定要取消这个订单吗？',
        onOk() {
          // TODO: 实现取消订单API
          message.success('订单已取消');
          loadOrders();
        }
      });
    };

    const loadOrders = async () => {
      loading.value = true;
      try {
        // TODO: 实现API调用
        // 模拟数据
        orders.value = [
          {
            id: 1,
            orderNo: 'ORD20240115001',
            serviceName: '机油更换',
            shopName: '汽车之家维修店',
            vehicleInfo: '大众朗逸 京A12345',
            serviceDescription: '更换全合成机油，机油滤芯',
            amount: 150,
            status: 4,
            bookingTime: '2024-01-15 10:00',
            createTime: '2024-01-14 15:30',
            completeTime: '2024-01-15 11:30'
          },
          {
            id: 2,
            orderNo: 'ORD20240112001',
            serviceName: '轮胎更换',
            shopName: '快修连锁店',
            vehicleInfo: '大众朗逸 京A12345',
            serviceDescription: '更换四条轮胎',
            amount: 800,
            status: 2,
            bookingTime: '2024-01-16 14:00',
            createTime: '2024-01-12 09:15',
            completeTime: null
          }
        ] as any;
        pagination.value.total = 2;
      } finally {
        loading.value = false;
      }
    };

    onMounted(() => {
      loadOrders();
    });

    return {
      loading,
      orders,
      completedOrders,
      totalSpent,
      filterForm,
      ratingForm,
      pagination,
      rateModalVisible,
      currentOrderId,
      getStatusColor,
      getStatusText,
      handleFilter,
      resetFilter,
      viewDetail,
      rateService,
      submitRating,
      cancelOrder
    };
  }
});
</script>

<style scoped>
.order-history {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0;
  position: relative;
}

.order-history::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.03)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

/* 页面头部 */
.page-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 0 0 24px 24px;
  padding: 32px;
  margin-bottom: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.title-section h1 {
  margin: 0 0 8px 0;
  color: #1a1a1a;
  font-size: 32px;
  font-weight: 700;
  display: flex;
  align-items: center;
  gap: 12px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.title-section p {
  margin: 0 0 20px 0;
  color: #666;
  font-size: 16px;
}

.order-stats {
  display: flex;
  align-items: center;
  gap: 20px;
}

.stat-item {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #667eea;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.stat-divider {
  width: 1px;
  height: 24px;
  background: rgba(0, 0, 0, 0.1);
}

/* 筛选区域 */
.filter-container {
  padding: 0 32px 24px;
  position: relative;
  z-index: 1;
}

.filter-card {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.filter-section {
  padding: 8px;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 24px;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-item label {
  font-size: 14px;
  font-weight: 500;
  color: #666;
}

.filter-actions {
  display: flex;
  gap: 12px;
  margin-left: auto;
}

.filter-actions .ant-btn {
  border-radius: 8px;
  font-weight: 500;
}

/* 订单容器 */
.orders-container {
  padding: 0 32px 32px;
  position: relative;
  z-index: 1;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80px 24px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.empty-illustration {
  width: 120px;
  height: 120px;
  margin: 0 auto 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48px;
  color: #667eea;
}

.empty-state h2 {
  margin: 0 0 12px 0;
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
}

.empty-state p {
  margin: 0 0 32px 0;
  color: #666;
  font-size: 16px;
}

/* 订单列表 */
.orders-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.order-card {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.order-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
}

/* 订单头部 */
.order-header {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.order-avatar {
  flex-shrink: 0;
}

.order-basic-info {
  flex: 1;
}

.order-basic-info h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
}

.shop-info {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 8px;
  color: #666;
  font-size: 14px;
}

.order-number {
  font-size: 12px;
  color: #999;
  font-family: 'Courier New', monospace;
}

.order-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.status-tag {
  font-size: 14px;
  font-weight: 500;
  padding: 4px 12px;
  border-radius: 12px;
}

.order-amount {
  font-size: 24px;
  font-weight: 700;
  background: linear-gradient(135deg, #f093fb, #f5576c);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 订单详情 */
.order-details {
  margin-bottom: 24px;
}

.detail-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 12px;
  transition: all 0.3s;
}

.detail-item:hover {
  background: rgba(248, 250, 252, 1);
  transform: translateY(-1px);
}

.detail-icon {
  width: 36px;
  height: 36px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: white;
  flex-shrink: 0;
}

.detail-icon.vehicle {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.detail-icon.time {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.detail-icon.service {
  background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.detail-icon.date {
  background: linear-gradient(135deg, #fa709a, #fee140);
}

.detail-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-label {
  font-size: 12px;
  color: #666;
  line-height: 1;
}

.detail-value {
  font-size: 14px;
  color: #1a1a1a;
  font-weight: 500;
  line-height: 1.4;
}

/* 订单操作 */
.order-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.order-actions .ant-btn {
  border-radius: 8px;
  font-weight: 500;
}

/* 分页容器 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 32px;
  padding: 24px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .detail-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 24px 20px;
    border-radius: 0;
  }
  
  .filter-container,
  .orders-container {
    padding: 0 20px 24px;
  }
  
  .title-section h1 {
    font-size: 24px;
  }
  
  .order-stats {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .filter-row {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .filter-actions {
    margin-left: 0;
  }
  
  .order-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .order-status {
    align-items: flex-start;
  }
  
  .detail-grid {
    grid-template-columns: 1fr;
  }
  
  .order-actions {
    flex-direction: column;
  }
  
  .empty-state {
    padding: 60px 20px;
  }
  
  .empty-illustration {
    width: 100px;
    height: 100px;
    font-size: 40px;
  }
}

@media (max-width: 480px) {
  .page-header {
    padding: 20px 16px;
  }
  
  .filter-container,
  .orders-container {
    padding: 0 16px 20px;
  }
  
  .title-section h1 {
    font-size: 20px;
  }
  
  .order-card {
    padding: 20px;
  }
  
  .order-header {
    gap: 12px;
  }
  
  .order-basic-info h3 {
    font-size: 18px;
  }
  
  .order-amount {
    font-size: 20px;
  }
  
  .detail-item {
    padding: 12px;
  }
  
  .detail-icon {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }
}
</style>
