package com.gec.wiki.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gec.wiki.pojo.Category;
import com.gec.wiki.pojo.req.CategoryQueryReq;
import com.gec.wiki.pojo.resp.CategoryQueryResp;
import com.gec.wiki.pojo.req.CategorySaveReq;
import com.gec.wiki.pojo.resp.CommonResp;
import com.gec.wiki.pojo.resp.PageResp;
import com.gec.wiki.service.CategoryService;
import com.gec.wiki.utils.CopyUtil;
import com.gec.wiki.utils.SnowFlake;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/category")
public class CategoryController {

    @Autowired
     CategoryService categoryService;

    @GetMapping("category")
    public List<Category> categoryList(@RequestParam("ids") List<Long> ids){

        return categoryService.getByIds(ids);
    }

    @GetMapping("/getCategoryByCategoryReq")
    public CommonResp getCategoryByCategoryReq(CategoryQueryReq req){
        List<Category> list;
        System.out.println("req = " + req);
        if (!ObjectUtils.isEmpty(req.getName())){
            QueryWrapper<Category> wrapper = new QueryWrapper<>();
            wrapper.like("name",req.getName());
            list = categoryService.list(wrapper);
        } else {
            list = categoryService.list();
        }
        List<CategoryQueryResp> categoryQueryResps = CopyUtil.copyList(list, CategoryQueryResp.class);
        CommonResp<List<CategoryQueryResp>> resp = new CommonResp<>();
        resp.setContent(categoryQueryResps);
        return resp;
    }
    //打印日志
    private static final Logger LOG = LoggerFactory.getLogger(CategoryService.class);

    @GetMapping("/getcategoryListByPage")
    public CommonResp getcategoryListByPage(CategoryQueryReq categoryReq){
        Page<Category> page = new Page<>(categoryReq.getPage(),categoryReq.getSize());
        page = categoryService.page(page);
        List<Category> list = page.getRecords();

        LOG.info("总行数：{}",page.getTotal()+"");
        LOG.info("总页数：{}",page.getPages()+"");

        List<CategoryQueryResp> categoryQueryResps =CopyUtil.copyList(list, CategoryQueryResp.class);

        //分页处理
        PageResp<CategoryQueryResp> pageResp=new PageResp<>();
        //设置总行数
        pageResp.setTotal(page.getTotal());
        //存储分页的具体数据
        pageResp.setList(categoryQueryResps);

        CommonResp< PageResp<CategoryQueryResp> > resp = new CommonResp<>();
        resp.setContent(pageResp);
        return resp;
    }

    @PostMapping("/save")
    public CommonResp save(@RequestBody CategorySaveReq req){
        CommonResp resp = new CommonResp();
        try {
            LOG.info("💾 保存分类，参数：{}", req);
            
            // 验证必填字段
            if (ObjectUtils.isEmpty(req.getName()) || req.getName().trim().isEmpty()) {
                resp.setSuccess(false);
                resp.setMessage("分类名称不能为空");
                LOG.warn("❌ 分类名称为空");
                return resp;
            }
            
            // 检查分类名称是否已存在（在同一父级下）
            QueryWrapper<Category> checkWrapper = new QueryWrapper<>();
            checkWrapper.eq("name", req.getName().trim());
            if (req.getParent() != null) {
                checkWrapper.eq("parent", req.getParent());
            } else {
                checkWrapper.eq("parent", 0);
            }
            
            // 如果是编辑，排除自己
            if (!ObjectUtils.isEmpty(req.getId())) {
                checkWrapper.ne("id", req.getId());
            }
            
            Category existingCategory = categoryService.getOne(checkWrapper);
            if (existingCategory != null) {
                resp.setSuccess(false);
                resp.setMessage("分类名称已存在，请使用其他名称");
                LOG.warn("❌ 分类名称已存在：{}", req.getName());
                return resp;
            }
            
            // 格式化请求
            Category category = CopyUtil.copy(req, Category.class);
            
            if (ObjectUtils.isEmpty(req.getId())) {
                // 新增分类
                SnowFlake snowFlake = new SnowFlake();
                category.setId(snowFlake.nextId());
                
                // 设置默认排序
                if (ObjectUtils.isEmpty(category.getSort())) {
                    // 计算同级分类的最大排序号
                    QueryWrapper<Category> sortWrapper = new QueryWrapper<>();
                    if (category.getParent() != null && category.getParent() != 0) {
                        sortWrapper.eq("parent", category.getParent());
                    } else {
                        sortWrapper.eq("parent", 0);
                        category.setParent(0L); // 确保一级分类的parent为0
                    }
                    sortWrapper.orderByDesc("sort");
                    Category lastCategory = categoryService.getOne(sortWrapper);
                    int nextSort = (lastCategory != null && lastCategory.getSort() != null) ? 
                        lastCategory.getSort() + 1 : 1;
                    category.setSort(nextSort);
                }
                
                boolean result = categoryService.saveOrUpdate(category);
                if (result) {
                    resp.setSuccess(true);
                    resp.setMessage("添加成功");
                    resp.setContent(category); // 返回新创建的分类信息
                    LOG.info("✅ 分类添加成功，ID：{}, 名称：{}", category.getId(), category.getName());
                } else {
                    resp.setSuccess(false);
                    resp.setMessage("添加失败");
                    LOG.error("❌ 分类添加失败");
                }
            } else {
                // 更新分类
                boolean result = categoryService.saveOrUpdate(category);
                if (result) {
                    resp.setSuccess(true);
                    resp.setMessage("修改成功");
                    LOG.info("✅ 分类修改成功，ID：{}, 名称：{}", category.getId(), category.getName());
                } else {
                    resp.setSuccess(false);
                    resp.setMessage("修改失败");
                    LOG.error("❌ 分类修改失败");
                }
            }
        } catch (Exception e) {
            LOG.error("💥 保存分类失败", e);
            resp.setSuccess(false);
            resp.setMessage("操作失败：" + e.getMessage());
        }
        return resp;
    }
    @GetMapping("/remove")
    public CommonResp remove(long id){
        CommonResp<Object> resp = new CommonResp<>();
        try {
            LOG.info("🗑️ 删除分类，ID：{}", id);
            
            // 检查是否有子分类
            if (categoryService.hasChildren(id)) {
                resp.setSuccess(false);
                resp.setMessage("该分类下还有子分类，请先删除子分类");
                LOG.warn("❌ 分类删除失败：存在子分类，ID：{}", id);
                return resp;
            }
            
            boolean result = categoryService.deleteCategoryById(id);
            if (result) {
                resp.setSuccess(true);
                resp.setMessage("删除成功");
                LOG.info("✅ 分类删除成功，ID：{}", id);
            } else {
                resp.setSuccess(false);
                resp.setMessage("删除失败");
                LOG.error("❌ 分类删除失败，ID：{}", id);
            }
        } catch (Exception e) {
            LOG.error("💥 删除分类失败", e);
            resp.setSuccess(false);
            resp.setMessage("删除失败：" + e.getMessage());
        }
        return resp;
    }

    @GetMapping("/allList")
    public CommonResp allList(CategoryQueryReq req){
        CommonResp<List<CategoryQueryResp>> listCommonResp = new CommonResp<>();
        try {
            LOG.info("📚 查询所有分类列表，参数：{}", req);
            List<CategoryQueryResp> resp = categoryService.allList(req);
            listCommonResp.setContent(resp);
            listCommonResp.setSuccess(true);
            listCommonResp.setMessage("查询成功");
            LOG.info("✅ 分类查询完成，返回 {} 条记录", resp.size());
        } catch (Exception e) {
            LOG.error("❌ 查询分类列表失败", e);
            listCommonResp.setSuccess(false);
            listCommonResp.setMessage("查询失败：" + e.getMessage());
        }
        return listCommonResp;
    }

    @GetMapping("/tree")
    public CommonResp getTreeList(){
        CommonResp<List<CategoryQueryResp>> resp = new CommonResp<>();
        try {
            LOG.info("🌲 获取分类树形结构");
            List<CategoryQueryResp> treeList = categoryService.getTreeList();
            resp.setContent(treeList);
            resp.setSuccess(true);
            resp.setMessage("查询成功");
            LOG.info("✅ 分类树形结构查询完成，根节点数量：{}", treeList.size());
        } catch (Exception e) {
            LOG.error("❌ 查询分类树形结构失败", e);
            resp.setSuccess(false);
            resp.setMessage("查询失败：" + e.getMessage());
        }
        return resp;
    }

    @GetMapping("/children/{parentId}")
    public CommonResp getChildrenByParentId(@PathVariable Long parentId){
        CommonResp<List<CategoryQueryResp>> resp = new CommonResp<>();
        try {
            LOG.info("📂 获取父分类 {} 的子分类", parentId);
            List<CategoryQueryResp> children = categoryService.getChildrenByParentId(parentId);
            resp.setContent(children);
            resp.setSuccess(true);
            resp.setMessage("查询成功");
            LOG.info("✅ 子分类查询完成，数量：{}", children.size());
        } catch (Exception e) {
            LOG.error("❌ 查询子分类失败", e);
            resp.setSuccess(false);
            resp.setMessage("查询失败：" + e.getMessage());
        }
        return resp;
    }

    @GetMapping("/hasChildren/{id}")
    public CommonResp hasChildren(@PathVariable Long id){
        CommonResp<Boolean> resp = new CommonResp<>();
        try {
            boolean hasChildren = categoryService.hasChildren(id);
            resp.setContent(hasChildren);
            resp.setSuccess(true);
            resp.setMessage("查询成功");
        } catch (Exception e) {
            LOG.error("❌ 查询子分类失败", e);
            resp.setSuccess(false);
            resp.setMessage("查询失败：" + e.getMessage());
        }
        return resp;
    }

    @PostMapping("/validate")
    public CommonResp validateCategoryName(@RequestBody CategorySaveReq req){
        CommonResp<Boolean> resp = new CommonResp<>();
        try {
            if (ObjectUtils.isEmpty(req.getName()) || req.getName().trim().isEmpty()) {
                resp.setSuccess(false);
                resp.setMessage("分类名称不能为空");
                return resp;
            }
            
            boolean exists = categoryService.isNameExists(req.getName().trim(), req.getParent(), req.getId());
            resp.setContent(!exists);
            resp.setSuccess(true);
            if (exists) {
                resp.setMessage("分类名称已存在");
            } else {
                resp.setMessage("分类名称可用");
            }
        } catch (Exception e) {
            LOG.error("❌ 验证分类名称失败", e);
            resp.setSuccess(false);
            resp.setMessage("验证失败：" + e.getMessage());
        }
        return resp;
    }
}