package com.gec.wiki.service;

/**
 * 邮件服务接口
 */
public interface EmailService {
    
    /**
     * 发送验证码邮件
     * @param to 收件人邮箱
     * @param code 验证码
     * @return 是否发送成功
     */
    boolean sendVerificationCode(String to, String code);
    
    /**
     * 使用用户提供的授权码发送验证码邮件
     * @param to 收件人邮箱
     * @param code 验证码
     * @param userEmail 用户QQ邮箱
     * @param authCode 用户提供的QQ邮箱授权码
     * @return 是否发送成功
     */
    boolean sendVerificationCodeWithAuthCode(String to, String code, String userEmail, String authCode);
    
    /**
     * 生成6位数字验证码
     * @return 验证码
     */
    String generateVerificationCode();
    
    /**
     * 存储验证码到Redis（5分钟有效期）
     * @param email 邮箱
     * @param code 验证码
     */
    void storeVerificationCode(String email, String code);
    
    /**
     * 验证验证码是否正确
     * @param email 邮箱
     * @param code 用户输入的验证码
     * @return 是否验证成功
     */
    boolean verifyCode(String email, String code);
    
    /**
     * 删除验证码（验证成功后清除）
     * @param email 邮箱
     */
    void removeVerificationCode(String email);
    
    /**
     * 生成临时密码（8位数字+字母）
     * @return 临时密码
     */
    String generateTempPassword();
}


