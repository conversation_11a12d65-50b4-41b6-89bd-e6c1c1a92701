<template>
  <div class="admin-layout">
    <div class="admin-header">
      <h2>汽车维修服务管理</h2>
      <p>管理系统中的汽车维修服务项目信息</p>
    </div>

    <div class="search-form-wrapper">
      <a-form layout="inline" :model="param">
        <a-form-item>
          <a-input v-model="param.name" placeholder="输入服务名称搜索" allow-clear>
            <template #prefix>
              <search-outlined />
            </template>
          </a-input>
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="handleQuery({page: 1, size: pagination.pageSize})">
            <template #icon><search-outlined /></template>
            查询
          </a-button>
        </a-form-item>
              <a-form-item>
        <a-button type="primary" @click="add">
          <template #icon><plus-outlined /></template>
          新增
        </a-button>
        <a-button type="default" @click="testModal" style="margin-left: 8px;">
          测试模态框
        </a-button>
      </a-form-item>
      </a-form>
    </div>

    <div class="table-container">
      <a-table
          :columns="columns"
          :row-key="record => record.id"
          :data-source="services"
          :pagination="pagination"
          :loading="loading"
          @change="handleTableChange"
          class="custom-table"
          :rowClassName="() => 'custom-table-row'"
          :scroll="{ y: 400 }"
      >
        <template #cover="{ text: cover }">
          <div class="cover-wrapper">
            <img v-if="cover" :src="cover" alt="服务图片" class="cover-image"/>
            <img v-else src="/image/default-service.png" alt="默认图片" class="cover-image"/>
          </div>
        </template>

        <template #price="{ text: price }">
          <span class="price-display">¥{{ price || 0 }}</span>
        </template>

        <template #duration="{ text: duration }">
          <a-tag color="blue">{{ duration || 0 }}分钟</a-tag>
        </template>

        <template #action="{ record }">
          <a-space>
            <a-button type="link" size="small" @click="edit(record)">
              <template #icon><edit-outlined /></template>
              编辑
            </a-button>
            <a-popconfirm
                title="确认删除此服务？"
                @confirm="handleDelete(record.id)"
                ok-text="确认"
                cancel-text="取消"
            >
              <a-button type="link" size="small" danger>
                <template #icon><delete-outlined /></template>
                删除
              </a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </a-table>
    </div>

    <a-modal
      title="服务信息"
      :visible="modalVisible"
      :maskClosable="false"
      @ok="handleModalOk"
      width="800px"
      :confirmLoading="modalLoading"
      @cancel="() => { console.log('模态框被取消'); modalVisible = false; }"
  >
    <a-form
        :model="service"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
        ref="serviceForm"
    >
      <a-form-item label="服务名称" name="name" :rules="[{ required: true, message: '请输入服务名称', trigger: 'blur' }]">
        <a-input v-model="service.name" placeholder="请输入服务名称"/>
      </a-form-item>

      <a-form-item label="一级分类" name="category1Id" :rules="[{ required: true, message: '请选择一级分类', trigger: 'change' }]">
        <a-select
            v-model="service.category1Id"
            placeholder="请选择一级分类"
            @change="handleCategory1Change"
            allow-clear
        >
          <a-select-option v-for="c in level1" :key="c.id" :value="c.id">{{c.name}}</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="二级分类" name="category2Id" :rules="[{ required: true, message: '请选择二级分类', trigger: 'change' }]">
        <a-select
            v-model="service.category2Id"
            placeholder="请选择二级分类"
            :disabled="!service.category1Id"
            allow-clear
        >
          <a-select-option v-for="c in level2" :key="c.id" :value="c.id">{{c.name}}</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="服务描述" name="description">
        <a-textarea v-model="service.description" :rows="4" placeholder="请输入服务描述"/>
      </a-form-item>

      <a-form-item label="服务价格" name="price" :rules="[{ required: true, message: '请输入服务价格' }]">
        <a-input-number
          v-model="service.price"
          :min="0"
          :precision="2"
          placeholder="请输入服务价格"
          style="width: 100%"
        />
      </a-form-item>

      <a-form-item label="服务时长" name="duration">
        <a-input-number
          v-model="service.duration"
          :min="0"
          placeholder="请输入服务时长(分钟)"
          style="width: 100%"
        />
      </a-form-item>

      <a-form-item label="服务图片">
        <a-upload
            :file-list="fileList"
            :before-upload="beforeUpload"
            @remove="handleRemove"
            list-type="picture-card"
            class="avatar-uploader"
        >
          <div v-if="fileList.length < 1">
            <plus-outlined />
            <div style="margin-top: 8px">上传图片</div>
          </div>
        </a-upload>
      </a-form-item>
    </a-form>
  </a-modal>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, ref, reactive } from 'vue';
import axios from 'axios';
import { message } from 'ant-design-vue';
import { SearchOutlined, PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons-vue';

interface ServiceData {
  id?: number;
  name?: string;
  category1Id?: number | null;
  category2Id?: number | null;
  description?: string;
  cover?: string;
  price?: number;
  originalPrice?: number;
  duration?: number;
  bookingCount?: number;
  completeCount?: number;
  ratingCount?: number;
  status?: number;
  isRecommend?: number;
}

export default defineComponent({
  name: 'AdminService',
  components: {
    SearchOutlined,
    PlusOutlined,
    EditOutlined,
    DeleteOutlined
  },
  setup() {
    const param = ref();
    param.value = {};
    const services = ref();
    const pagination = ref({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total: number, range: number[]) => `共 ${total} 条记录 第 ${range[0]}-${range[1]} 条`
    });
    const loading = ref(false);

    const columns = [
      {
        title: 'ID',
        dataIndex: 'id',
        width: 80,
      },
      {
        title: '服务图片',
        dataIndex: 'cover',
        slots: { customRender: 'cover' },
        width: 120,
      },
      {
        title: '服务名称',
        dataIndex: 'name',
        width: 200,
      },
      {
        title: '服务价格',
        dataIndex: 'price',
        slots: { customRender: 'price' },
        width: 120,
      },
      {
        title: '服务时长',
        dataIndex: 'duration',
        slots: { customRender: 'duration' },
        width: 120,
      },
      {
        title: '预约次数',
        dataIndex: 'bookingCount',
        width: 100,
      },
      {
        title: '完成次数',
        dataIndex: 'completeCount',
        width: 100,
      },
      {
        title: '好评数',
        dataIndex: 'ratingCount',
        width: 100,
      },
      {
        title: '操作',
        key: 'action',
        slots: { customRender: 'action' },
        width: 200,
      }
    ];

    /**
     * 数据查询
     **/
    const handleQuery = (params: any) => {
      loading.value = true;
      // 如果不传参数，就使用页面当前值
      if (params) {
        pagination.value.current = params.page;
        pagination.value.pageSize = params.size;
      }
      axios.get("/service/getServiceListByPage", {
        params: {
          page: pagination.value.current,
          size: pagination.value.pageSize,
          name: param.value.name
        }
      }).then((response) => {
        loading.value = false;
        const data = response.data;
        if (data.success) {
          services.value = data.content.list;

          // 重置分页按钮
          pagination.value.current = (params && params.page) ? params.page : pagination.value.current;
          pagination.value.total = data.content.total;
        } else {
          message.error(data.message);
        }
      }).catch((error: any) => {
        loading.value = false;
        console.error('加载服务列表失败:', error);
        message.error('加载服务列表失败，请检查网络连接');
      });
    };

    /**
     * 表格点击页码时触发
     */
    const handleTableChange = (paginationData: any) => {
      handleQuery({
        page: paginationData.current,
        size: paginationData.pageSize
      });
    };

    // -------- 表单 ---------
    const service = reactive<ServiceData>({
      name: '',
      category1Id: undefined,
      category2Id: undefined,
      description: '',
      price: 0,
      duration: 60,
      status: 1,
      isRecommend: 0
    });
    const modalVisible = ref(false);
    const modalLoading = ref(false);
    const serviceForm = ref();

    const handleModalOk = () => {
      if (!serviceForm.value) {
        message.error("表单初始化失败，请重新打开");
        return;
      }

      serviceForm.value.validate().then(() => {
        modalLoading.value = true;

        // 准备保存数据
        const saveData = {
          ...service,
          status: service.status || 1,
          isRecommend: service.isRecommend || 0
        };

        axios.post("/service/save", saveData).then((response) => {
          modalLoading.value = false;
          const data = response.data;
          if (data.success) {
            modalVisible.value = false;
            handleQuery({
              page: pagination.value.current,
              size: pagination.value.pageSize,
            });
            message.success(data.message || "操作成功");
          } else {
            message.error(data.message);
          }
        }).catch(() => {
          modalLoading.value = false;
          message.error("网络错误，请稍后重试");
        });
      }).catch(() => {
        message.error("请检查表单填写是否正确");
      });
    };

    /**
     * 编辑
     */
    const edit = async (record: any) => {
      console.log('✏️ 开始编辑服务:', record);

      // 深拷贝记录数据并赋值给reactive对象
      Object.assign(service, JSON.parse(JSON.stringify(record)));

      // 先加载分类数据，再显示模态框
      try {
        await handleCategoryQuery();

        // 为选择的一级分类加载二级分类
        if (service.category1Id) {
          handleCategory1Change(service.category1Id);
        }

        // 如果有图片，设置文件列表
        if (service.cover) {
          fileList.value = [{
            uid: '-1',
            name: 'image',
            status: 'done',
            url: service.cover,
          }];
        } else {
          fileList.value = [];
        }

        modalVisible.value = true;

        // 清除表单验证状态 - 需要等模态框完全显示后
        setTimeout(() => {
          if (serviceForm.value) {
            serviceForm.value.clearValidate();
            console.log('✅ 编辑模式表单验证状态已清除');
          }
        }, 300);
      } catch (error) {
        console.error('❌ 加载分类数据失败，无法编辑服务');
      }
    };

    /**
     * 新增
     */
    const add = async () => {
      console.log('🆕 开始新增服务...');

      // 初始化服务数据
      Object.assign(service, {
        name: '',
        category1Id: undefined,
        category2Id: undefined,
        description: '',
        price: 0,
        duration: 60,
        status: 1,
        isRecommend: 0
      });

      fileList.value = [];
      level2.value = []; // 清空二级分类

      // 先加载分类数据，再显示模态框
      try {
        await handleCategoryQuery();
        modalVisible.value = true;

        // 清除表单验证状态 - 需要等模态框完全显示后
        setTimeout(() => {
          if (serviceForm.value) {
            serviceForm.value.clearValidate();
            console.log('✅ 新增模式表单验证状态已清除');
          }
        }, 300);
      } catch (error) {
        console.error('❌ 加载分类数据失败，无法新增服务');
      }
    };

    const handleDelete = (id: number) => {
      axios.get("/service/remove?id=" + id).then((response) => {
        const data = response.data; // data = commonResp
        if (data.success) {
          // 重新加载列表
          handleQuery({
            page: pagination.value.current,
            size: pagination.value.pageSize,
          });
        }
      });
    };

    // 分类数据
    const level1 = ref<any[]>([]);
    const level2 = ref<any[]>([]);

    const handleCategoryQuery = () => {
      console.log('🏷️ 开始加载分类数据...');
      return axios.get("/category/allList").then((resp) => {
        console.log('🏷️ 分类数据响应:', resp.data);
        const data = resp.data;
        if (data.success) {
          const categorys = data.content;
          level1.value = [];
          level2.value = [];

          // 手动处理分类数据
          level1.value = categorys.filter((item: any) => item.parent === 0);
          console.log('🏷️ 一级分类数据:', level1.value);

          // 为每个一级分类添加子分类
          level1.value.forEach((parent: any) => {
            parent.children = categorys.filter((item: any) => item.parent === parent.id);
          });

          console.log('✅ 分类数据加载完成');
        } else {
          console.error('❌ 分类数据加载失败:', data.message);
          message.error('分类数据加载失败: ' + data.message);
        }
      }).catch((error) => {
        console.error('❌ 分类数据请求失败:', error);
        message.error('分类数据加载失败，请检查网络连接');
      });
    };

    const handleCategory1Change = (value: any) => {
      // 清空二级分类
      level2.value = [];
      service.category2Id = undefined;

      // 加载对应的二级分类
      if (level1.value && Array.isArray(level1.value)) {
        level1.value.forEach((item: any) => {
          if (item.id === value) {
            level2.value = item.children || [];
          }
        });
      }
    };

    const handleCategory2Change = () => {
      // 二级分类变化处理
    };

    // 文件上传
    const fileList = ref<any[]>([]);

    const beforeUpload = (file: any) => {
      const formData = new FormData();
      formData.append('file', file);

      axios.post('/service/uploadImage', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }).then((response) => {
        const data = response.data;
        if (data.success) {
          service.cover = data.content;
          fileList.value = [{
            uid: '-1',
            name: file.name,
            status: 'done',
            url: data.content,
          }];
          message.success('图片上传成功');
        } else {
          message.error('图片上传失败');
        }
      }).catch(() => {
        message.error('图片上传失败');
      });

      return false; // 阻止默认上传行为
    };

    const handleRemove = () => {
      service.cover = '';
      fileList.value = [];
    };






    onMounted(() => {
      handleQuery({
        page: 1,
        size: pagination.value.pageSize,
      });
    });

    return {
      param,
      services,
      pagination,
      columns,
      loading,
      handleTableChange,
      handleQuery,

      edit,
      add,

      service,
      modalVisible,
      modalLoading,
      serviceForm,
      handleModalOk,
      handleDelete,

      level1,
      level2,
      handleCategoryQuery,
      handleCategory1Change,

      fileList,
      beforeUpload,
      handleRemove,
      handleCategory2Change
    }
  }
});
</script>

<style scoped>
.admin-layout {
  padding: 24px;
  background: #f0f2f5;
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
}

.custom-table {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.admin-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e8e8e8;
}

.admin-header h2 {
  color: #001529;
  margin-bottom: 8px;
}

.admin-header p {
  color: #666;
  margin: 0;
}

.search-form-wrapper {
  background: #fff;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  position: relative;
  z-index: 1;
}

.custom-table-row {
  transition: all 0.3s;
}

.custom-table-row:hover {
  background-color: #f5f5f5 !important;
}

.cover-wrapper {
  width: 60px;
  height: 80px;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.price-display {
  font-weight: bold;
  color: #ff4d4f;
  font-size: 16px;
}

.avatar-uploader .ant-upload {
  width: 128px;
  height: 128px;
}

.avatar-uploader .ant-upload-select-picture-card i {
  font-size: 32px;
  color: #999;
}

.avatar-uploader .ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}
</style>
