package com.gec.wiki.controller;

import com.gec.wiki.pojo.Service;
import com.gec.wiki.pojo.req.ServiceQueryReq;
import com.gec.wiki.pojo.req.ServiceSaveReq;
import com.gec.wiki.pojo.resp.CommonResp;
import com.gec.wiki.pojo.resp.PageResp;
import com.gec.wiki.pojo.resp.ServiceQueryResp;
import com.gec.wiki.service.ServiceService;
import com.gec.wiki.service.CategoryService;
import com.gec.wiki.pojo.resp.CategoryQueryResp;
import com.gec.wiki.utils.CopyUtil;
import com.gec.wiki.utils.RequestContext;
import com.gec.wiki.utils.TokenManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 维修店服务管理控制器
 */
@RestController
@RequestMapping("/shop/service")
public class ShopServiceController {

    private static final Logger LOG = LoggerFactory.getLogger(ShopServiceController.class);

    @Autowired
    private ServiceService serviceService;

    @Autowired
    private CategoryService categoryService;
    
    @Autowired
    private TokenManager tokenManager;

    /**
     * 获取当前维修店的服务列表（分页）
     */
    @GetMapping("/list")
    public CommonResp<PageResp<ServiceQueryResp>> getShopServiceList(ServiceQueryReq req) {
        CommonResp<PageResp<ServiceQueryResp>> resp = new CommonResp<>();
        try {
            LOG.info("🏪 获取维修店服务列表，参数：{}", req);
            
            // 从请求上下文中获取当前用户ID作为shopId
            Long shopId = getCurrentShopId();
            if (shopId == null) {
                resp.setSuccess(false);
                resp.setMessage("用户未登录或非维修店用户");
                return resp;
            }
            
            // 验证分页参数
            if (req.getPage() == null || req.getPage() < 1) {
                req.setPage(1);
            }
            if (req.getSize() == null || req.getSize() < 1) {
                req.setSize(10);
            }
            
            PageResp<ServiceQueryResp> pageResp = serviceService.getShopServiceListByPage(req, shopId);
            resp.setContent(pageResp);
            resp.setSuccess(true);
            resp.setMessage("查询成功");
            LOG.info("✅ 维修店 {} 查询到 {} 条服务记录", shopId, pageResp.getTotal());
        } catch (Exception e) {
            LOG.error("❌ 获取维修店服务列表失败", e);
            resp.setSuccess(false);
            resp.setMessage("查询失败：" + e.getMessage());
        }
        return resp;
    }

    /**
     * 获取所有服务分类（用于创建/编辑服务时选择）
     */
    @GetMapping("/categories")
    public CommonResp<List<CategoryQueryResp>> getServiceCategories() {
        CommonResp<List<CategoryQueryResp>> resp = new CommonResp<>();
        try {
            LOG.info("📋 获取服务分类列表");
            List<CategoryQueryResp> categories = categoryService.getTreeList();
            resp.setContent(categories);
            resp.setSuccess(true);
            resp.setMessage("查询成功");
        } catch (Exception e) {
            LOG.error("❌ 获取服务分类失败", e);
            resp.setSuccess(false);
            resp.setMessage("查询失败：" + e.getMessage());
        }
        return resp;
    }

    /**
     * 根据ID获取服务详情
     */
    @GetMapping("/{id}")
    public CommonResp<ServiceQueryResp> getServiceById(@PathVariable Long id) {
        CommonResp<ServiceQueryResp> resp = new CommonResp<>();
        try {
            LOG.info("🔍 获取服务详情，ID：{}", id);
            
            Long shopId = getCurrentShopId();
            if (shopId == null) {
                resp.setSuccess(false);
                resp.setMessage("用户未登录或非维修店用户");
                return resp;
            }
            
            ServiceQueryResp service = serviceService.getServiceById(id);
            if (service == null) {
                resp.setSuccess(false);
                resp.setMessage("服务不存在");
                return resp;
            }
            
            // 检查服务是否属于当前维修店
            if (!shopId.equals(service.getShopId())) {
                resp.setSuccess(false);
                resp.setMessage("无权访问该服务");
                return resp;
            }
            
            resp.setContent(service);
            resp.setSuccess(true);
            resp.setMessage("查询成功");
        } catch (Exception e) {
            LOG.error("❌ 获取服务详情失败", e);
            resp.setSuccess(false);
            resp.setMessage("查询失败：" + e.getMessage());
        }
        return resp;
    }

    /**
     * 保存或更新服务
     */
    @PostMapping("/save")
    public CommonResp<Void> saveService(@RequestBody ServiceSaveReq req) {
        CommonResp<Void> resp = new CommonResp<>();
        try {
            LOG.info("💾 维修店保存服务，参数：{}", req);
            
            Long shopId = getCurrentShopId();
            if (shopId == null) {
                resp.setSuccess(false);
                resp.setMessage("用户未登录或非维修店用户");
                return resp;
            }
            
            // 验证必填字段
            if (ObjectUtils.isEmpty(req.getName()) || req.getName().trim().isEmpty()) {
                resp.setSuccess(false);
                resp.setMessage("服务名称不能为空");
                return resp;
            }
            
            if (ObjectUtils.isEmpty(req.getPrice())) {
                resp.setSuccess(false);
                resp.setMessage("服务价格不能为空");
                return resp;
            }
            
            if (ObjectUtils.isEmpty(req.getCategory1Id()) || ObjectUtils.isEmpty(req.getCategory2Id())) {
                resp.setSuccess(false);
                resp.setMessage("请选择服务分类");
                return resp;
            }
            
            if (ObjectUtils.isEmpty(req.getDuration())) {
                resp.setSuccess(false);
                resp.setMessage("服务时长不能为空");
                return resp;
            }
            
            // 如果是更新操作，检查服务是否属于当前维修店
            if (!ObjectUtils.isEmpty(req.getId())) {
                ServiceQueryResp existingService = serviceService.getServiceById(req.getId());
                if (existingService != null && !shopId.equals(existingService.getShopId())) {
                    resp.setSuccess(false);
                    resp.setMessage("无权修改该服务");
                    return resp;
                }
            }
            
            Service service = CopyUtil.copy(req, Service.class);
            service.setShopId(shopId); // 设置维修店ID
            
            boolean result = serviceService.saveOrUpdateService(service);
            if (result) {
                resp.setSuccess(true);
                resp.setMessage(ObjectUtils.isEmpty(req.getId()) ? "添加成功" : "修改成功");
                LOG.info("✅ 维修店 {} 服务保存成功，ID：{}", shopId, service.getId());
            } else {
                resp.setSuccess(false);
                resp.setMessage("操作失败");
                LOG.error("❌ 维修店 {} 服务保存失败", shopId);
            }
        } catch (Exception e) {
            LOG.error("💥 维修店保存服务失败", e);
            resp.setSuccess(false);
            resp.setMessage("操作失败：" + e.getMessage());
        }
        return resp;
    }

    /**
     * 删除服务
     */
    @DeleteMapping("/{id}")
    public CommonResp<Void> deleteService(@PathVariable Long id) {
        CommonResp<Void> resp = new CommonResp<>();
        try {
            LOG.info("🗑️ 删除服务，ID：{}", id);
            
            Long shopId = getCurrentShopId();
            if (shopId == null) {
                resp.setSuccess(false);
                resp.setMessage("用户未登录或非维修店用户");
                return resp;
            }
            
            // 检查服务是否属于当前维修店
            ServiceQueryResp service = serviceService.getServiceById(id);
            if (service == null) {
                resp.setSuccess(false);
                resp.setMessage("服务不存在");
                return resp;
            }
            
            if (!shopId.equals(service.getShopId())) {
                resp.setSuccess(false);
                resp.setMessage("无权删除该服务");
                return resp;
            }
            
            boolean result = serviceService.removeById(id);
            if (result) {
                resp.setSuccess(true);
                resp.setMessage("删除成功");
                LOG.info("✅ 维修店 {} 删除服务成功，ID：{}", shopId, id);
            } else {
                resp.setSuccess(false);
                resp.setMessage("删除失败");
            }
        } catch (Exception e) {
            LOG.error("❌ 删除服务失败", e);
            resp.setSuccess(false);
            resp.setMessage("删除失败：" + e.getMessage());
        }
        return resp;
    }

    /**
     * 更新服务状态（上架/下架）
     */
    @PutMapping("/{id}/status")
    public CommonResp<Void> updateServiceStatus(@PathVariable Long id, @RequestParam Integer status) {
        CommonResp<Void> resp = new CommonResp<>();
        try {
            LOG.info("🔄 更新服务状态，ID：{}，状态：{}", id, status);
            
            Long shopId = getCurrentShopId();
            if (shopId == null) {
                resp.setSuccess(false);
                resp.setMessage("用户未登录或非维修店用户");
                return resp;
            }
            
            // 检查服务是否属于当前维修店
            ServiceQueryResp service = serviceService.getServiceById(id);
            if (service == null) {
                resp.setSuccess(false);
                resp.setMessage("服务不存在");
                return resp;
            }
            
            if (!shopId.equals(service.getShopId())) {
                resp.setSuccess(false);
                resp.setMessage("无权修改该服务");
                return resp;
            }
            
            Service updateService = new Service();
            updateService.setId(id);
            updateService.setStatus(status);
            
            boolean result = serviceService.updateById(updateService);
            if (result) {
                resp.setSuccess(true);
                resp.setMessage("状态更新成功");
                LOG.info("✅ 维修店 {} 更新服务状态成功，ID：{}，状态：{}", shopId, id, status);
            } else {
                resp.setSuccess(false);
                resp.setMessage("状态更新失败");
            }
        } catch (Exception e) {
            LOG.error("❌ 更新服务状态失败", e);
            resp.setSuccess(false);
            resp.setMessage("状态更新失败：" + e.getMessage());
        }
        return resp;
    }

    /**
     * 上传服务图片
     */
    @PostMapping("/upload")
    public CommonResp<String> uploadServiceImage(@RequestParam("file") MultipartFile file) {
        CommonResp<String> resp = new CommonResp<>();
        try {
            LOG.info("📸 上传服务图片，文件名：{}", file.getOriginalFilename());
            
            Long shopId = getCurrentShopId();
            if (shopId == null) {
                resp.setSuccess(false);
                resp.setMessage("用户未登录或非维修店用户");
                return resp;
            }
            
            String imageUrl = serviceService.uploadServiceImage(file);
            resp.setContent(imageUrl);
            resp.setSuccess(true);
            resp.setMessage("图片上传成功");
            LOG.info("✅ 维修店 {} 图片上传成功，URL：{}", shopId, imageUrl);
        } catch (Exception e) {
            LOG.error("❌ 上传服务图片失败", e);
            resp.setSuccess(false);
            resp.setMessage("图片上传失败：" + e.getMessage());
        }
        return resp;
    }

    /**
     * 获取当前登录的维修店ID
     */
    private Long getCurrentShopId() {
        try {
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            
            // 从请求头中获取token
            String token = request.getHeader("Authorization");
            if (token == null || token.trim().isEmpty()) {
                LOG.error("未找到Authorization头，用户可能未登录");
                return null;
            }
            
            // 使用TokenManager获取用户信息
            TokenManager.UserInfo userInfo = tokenManager.getUserInfoByToken(token);
            if (userInfo == null) {
                LOG.error("根据token未找到用户信息，token可能已过期: {}", token);
                return null;
            }
            
            // 检查用户类型是否为维修店（userType=2）
            if (userInfo.getUserType() == null || userInfo.getUserType() != 2) {
                LOG.error("用户不是维修店类型，userType={}, userId={}", userInfo.getUserType(), userInfo.getUserId());
                return null;
            }
            
            LOG.debug("获取当前维修店ID成功: userId={}, username={}", userInfo.getUserId(), userInfo.getUsername());
            return userInfo.getUserId();
        } catch (Exception e) {
            LOG.error("获取当前维修店ID失败", e);
            return null;
        }
    }
}
