package com.gec.wiki.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gec.wiki.pojo.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户数据访问层
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {
    
    /**
     * 根据用户名查询用户
     */
    User findByUsername(@Param("username") String username);
    
    /**
     * 根据手机号查询用户
     */
    User findByPhone(@Param("phone") String phone);
    
    /**
     * 根据邮箱查询用户
     */
    User findByEmail(@Param("email") String email);
    
    /**
     * 更新最后登录时间
     */
    int updateLastLoginTime(@Param("id") Long id);
    
    /**
     * 根据真实姓名、手机号和邮箱查找用户（忘记密码功能）
     */
    User findByRealNameAndPhoneAndEmail(@Param("realName") String realName, 
                                       @Param("phone") String phone, 
                                       @Param("email") String email);
    
    /**
     * 更新用户密码
     */
    int updatePassword(@Param("id") Long id, @Param("password") String password);
}
