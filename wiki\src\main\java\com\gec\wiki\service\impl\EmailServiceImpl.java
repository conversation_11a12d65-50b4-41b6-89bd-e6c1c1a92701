package com.gec.wiki.service.impl;

import com.gec.wiki.service.EmailService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Random;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;
import java.util.Properties;

/**
 * 邮件服务实现类
 */
@Service
public class EmailServiceImpl implements EmailService {
    
    private static final Logger LOG = LoggerFactory.getLogger(EmailServiceImpl.class);
    
    @Resource
    private JavaMailSender mailSender;
    
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    
    private static final String VERIFICATION_CODE_PREFIX = "verification:code:";
    private static final int CODE_EXPIRE_TIME = 5; // 验证码有效期5分钟
    
    // 内存存储验证码（临时方案，避免Redis依赖）
    private final Map<String, String> codeCache = new ConcurrentHashMap<>();
    private final Map<String, Long> codeExpireTime = new ConcurrentHashMap<>();
    
    @Override
    public boolean sendVerificationCode(String to, String code) {
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom("<EMAIL>"); // 发送方邮箱，需要与配置文件中的username一致
            message.setTo(to);
            message.setSubject("汽车维修服务平台 - 密码找回验证码");
            message.setText(buildEmailContent(code));
            
            mailSender.send(message);
            LOG.info("验证码邮件发送成功，收件人：{}", to);
            return true;
        } catch (Exception e) {
            LOG.error("验证码邮件发送失败，收件人：{}，错误：", to, e);
            return false;
        }
    }
    
    @Override
    public boolean sendVerificationCodeWithAuthCode(String to, String code, String userEmail, String authCode) {
        try {
            // 创建临时的邮件发送器，使用用户提供的授权码
            JavaMailSenderImpl tempMailSender = new JavaMailSenderImpl();
            tempMailSender.setHost("smtp.qq.com");
            tempMailSender.setPort(587);
            tempMailSender.setUsername(userEmail);
            tempMailSender.setPassword(authCode);
            
            // 设置SMTP属性
            Properties props = new Properties();
            props.put("mail.smtp.auth", "true");
            props.put("mail.smtp.starttls.enable", "true");
            props.put("mail.smtp.starttls.required", "true");
            tempMailSender.setJavaMailProperties(props);
            
            // 构建并发送邮件
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(userEmail);
            message.setTo(to);
            message.setSubject("汽车维修服务平台 - 密码找回验证码");
            message.setText(buildEmailContentWithAuthCode(code, userEmail));
            
            tempMailSender.send(message);
            LOG.info("使用用户授权码发送验证码邮件成功，发送方：{}，收件人：{}", userEmail, to);
            return true;
        } catch (Exception e) {
            LOG.error("使用用户授权码发送验证码邮件失败，发送方：{}，收件人：{}，错误：", userEmail, to, e);
            return false;
        }
    }
    
    @Override
    public String generateVerificationCode() {
        Random random = new Random();
        StringBuilder code = new StringBuilder();
        for (int i = 0; i < 6; i++) {
            code.append(random.nextInt(10));
        }
        return code.toString();
    }
    
    @Override
    public void storeVerificationCode(String email, String code) {
        String key = VERIFICATION_CODE_PREFIX + email;
        
        // 尝试使用Redis，如果失败则使用内存存储
        try {
            stringRedisTemplate.opsForValue().set(key, code, CODE_EXPIRE_TIME, TimeUnit.MINUTES);
            LOG.info("验证码已存储到Redis，邮箱：{}，有效期：{}分钟", email, CODE_EXPIRE_TIME);
        } catch (Exception e) {
            // Redis不可用时，使用内存存储
            codeCache.put(key, code);
            codeExpireTime.put(key, System.currentTimeMillis() + CODE_EXPIRE_TIME * 60 * 1000L);
            LOG.warn("Redis不可用，验证码已存储到内存，邮箱：{}，有效期：{}分钟", email, CODE_EXPIRE_TIME);
        }
    }
    
    @Override
    public boolean verifyCode(String email, String code) {
        String key = VERIFICATION_CODE_PREFIX + email;
        String storedCode = null;
        
        // 尝试从Redis获取，如果失败则从内存获取
        try {
            storedCode = stringRedisTemplate.opsForValue().get(key);
        } catch (Exception e) {
            LOG.warn("Redis不可用，尝试从内存获取验证码，邮箱：{}", email);
        }
        
        // 如果Redis中没有，检查内存存储
        if (storedCode == null) {
            storedCode = codeCache.get(key);
            Long expireTime = codeExpireTime.get(key);
            
            if (storedCode != null && expireTime != null) {
                if (System.currentTimeMillis() > expireTime) {
                    // 验证码已过期，清除
                    codeCache.remove(key);
                    codeExpireTime.remove(key);
                    LOG.warn("内存中的验证码已过期，邮箱：{}", email);
                    return false;
                }
            }
        }
        
        if (storedCode == null) {
            LOG.warn("验证码不存在或已过期，邮箱：{}", email);
            return false;
        }
        
        boolean isValid = storedCode.equals(code);
        LOG.info("验证码验证结果，邮箱：{}，结果：{}", email, isValid ? "成功" : "失败");
        return isValid;
    }
    
    @Override
    public void removeVerificationCode(String email) {
        String key = VERIFICATION_CODE_PREFIX + email;
        
        // 尝试从Redis删除
        try {
            stringRedisTemplate.delete(key);
            LOG.info("验证码已从Redis清除，邮箱：{}", email);
        } catch (Exception e) {
            LOG.warn("Redis不可用，无法清除Redis中的验证码，邮箱：{}", email);
        }
        
        // 同时从内存中删除
        codeCache.remove(key);
        codeExpireTime.remove(key);
        LOG.info("验证码已从内存清除，邮箱：{}", email);
    }
    
    /**
     * 构建邮件内容
     */
    private String buildEmailContent(String code) {
        return "尊敬的用户：\n\n" +
               "您正在进行密码找回操作，您的验证码为：" + code + "\n\n" +
               "验证码有效期为5分钟，请及时使用。\n" +
               "如果这不是您的操作，请忽略此邮件。\n\n" +
               "汽车维修服务平台\n" +
               "此邮件为系统自动发送，请勿回复。";
    }
    
    /**
     * 构建使用用户授权码发送的邮件内容
     */
    private String buildEmailContentWithAuthCode(String code, String senderEmail) {
        return "尊敬的用户：\n\n" +
               "您正在进行密码找回操作，您的验证码为：" + code + "\n\n" +
               "验证码有效期为5分钟，请及时使用。\n" +
               "如果这不是您的操作，请忽略此邮件。\n\n" +
               "本邮件由您的QQ邮箱（" + senderEmail + "）发送，感谢您的授权。\n\n" +
               "汽车维修服务平台\n" +
               "此邮件为系统自动发送，请勿回复。";
    }
    
    @Override
    public String generateTempPassword() {
        // 生成8位临时密码，包含数字和字母
        String characters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        Random random = new Random();
        StringBuilder tempPassword = new StringBuilder();
        for (int i = 0; i < 8; i++) {
            tempPassword.append(characters.charAt(random.nextInt(characters.length())));
        }
        return tempPassword.toString();
    }
}

