<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gec.wiki.mapper.ServiceMapper">

    <resultMap id="BaseResultMap" type="com.gec.wiki.pojo.Service">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="category1Id" column="category1_id" jdbcType="BIGINT"/>
        <result property="category2Id" column="category2_id" jdbcType="BIGINT"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>
        <result property="cover" column="cover" jdbcType="VARCHAR"/>
        <result property="images" column="images" jdbcType="VARCHAR"/>
        <result property="price" column="price" jdbcType="DECIMAL"/>
        <result property="originalPrice" column="original_price" jdbcType="DECIMAL"/>
        <result property="bookingCount" column="booking_count" jdbcType="INTEGER"/>
        <result property="completeCount" column="complete_count" jdbcType="INTEGER"/>
        <result property="ratingCount" column="rating_count" jdbcType="INTEGER"/>
        <result property="ratingScore" column="rating_score" jdbcType="DECIMAL"/>
        <result property="duration" column="duration" jdbcType="INTEGER"/>
        <result property="isRecommend" column="is_recommend" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, name, category1_id, category2_id, description, content, cover, images,
        price, original_price, booking_count, complete_count, rating_count, rating_score,
        duration, is_recommend, status, create_time, update_time
    </sql>

</mapper>
