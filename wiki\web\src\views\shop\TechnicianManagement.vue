<template>
  <div class="technician-management">
    <div class="page-header">
      <h2>技师管理</h2>
      <a-button type="primary" @click="showAddModal">
        <PlusOutlined />
        添加技师
      </a-button>
    </div>

    <a-card class="technician-stats">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-statistic title="总技师数" :value="technicianStats.total" prefix="👥" />
        </a-col>
        <a-col :span="6">
          <a-statistic title="在线技师" :value="technicianStats.online" prefix="🟢" />
        </a-col>
        <a-col :span="6">
          <a-statistic title="今日服务" :value="technicianStats.todayServices" prefix="🔧" />
        </a-col>
        <a-col :span="6">
          <a-statistic title="平均评分" :value="technicianStats.avgRating" :precision="1" suffix="/5.0" prefix="⭐" />
        </a-col>
      </a-row>
    </a-card>

    <a-card class="technician-list-card">
      <template #title>
        <div class="table-header">
          <span>技师列表</span>
          <div class="table-actions">
            <a-input-search
              v-model:value="searchKeyword"
              placeholder="搜索技师姓名或技能"
              style="width: 250px;"
              @search="handleSearch"
            />
          </div>
        </div>
      </template>

      <a-table 
        :dataSource="technicians" 
        :columns="columns" 
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        rowKey="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'avatar'">
            <a-avatar :src="record.avatar" :size="40">
              {{ record.name.charAt(0) }}
            </a-avatar>
          </template>
          <template v-else-if="column.key === 'status'">
            <a-tag :color="record.status === 1 ? 'green' : 'default'">
              {{ record.status === 1 ? '在线' : '离线' }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'skills'">
            <a-tag v-for="skill in record.skills" :key="skill" color="blue">
              {{ skill }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'rating'">
            <a-rate :value="record.rating" disabled allow-half />
            <span class="rating-text">({{ record.rating }})</span>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button size="small" @click="editTechnician(record)">
                编辑
              </a-button>
              <a-button size="small" @click="viewSchedule(record.id)">
                排班
              </a-button>
              <a-button size="small" @click="viewPerformance(record.id)">
                业绩
              </a-button>
              <a-popconfirm
                title="确定删除这个技师吗？"
                @confirm="deleteTechnician(record.id)"
              >
                <a-button size="small" danger>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 添加/编辑技师模态框 -->
    <a-modal
      v-model:visible="modalVisible"
      :title="editingTechnician ? '编辑技师' : '添加技师'"
      width="600px"
      @ok="handleSubmit"
      @cancel="handleCancel"
    >
      <a-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="技师姓名" name="name">
              <a-input v-model:value="formData.name" placeholder="请输入技师姓名" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="联系电话" name="phone">
              <a-input v-model:value="formData.phone" placeholder="请输入联系电话" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="工作经验" name="experience">
              <a-select v-model:value="formData.experience" placeholder="请选择工作经验">
                <a-select-option value="1">1年以下</a-select-option>
                <a-select-option value="2">1-3年</a-select-option>
                <a-select-option value="3">3-5年</a-select-option>
                <a-select-option value="4">5-10年</a-select-option>
                <a-select-option value="5">10年以上</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="入职日期" name="joinDate">
              <a-date-picker 
                v-model:value="formData.joinDate" 
                style="width: 100%"
                placeholder="请选择入职日期"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="专业技能" name="skills">
          <a-select
            v-model:value="formData.skills"
            mode="multiple"
            placeholder="请选择专业技能"
            :options="skillOptions"
          />
        </a-form-item>

        <a-form-item label="技师简介" name="description">
          <a-textarea 
            v-model:value="formData.description" 
            placeholder="请输入技师简介"
            :rows="3"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 排班管理模态框 -->
    <a-modal
      v-model:visible="scheduleVisible"
      title="排班管理"
      width="800px"
      :footer="null"
    >
      <div class="schedule-content">
        <a-calendar v-model:value="selectedDate" @select="handleDateSelect">
          <template #dateCellRender="{ current }">
            <div class="schedule-cell">
              <div v-if="getSchedule(current)" class="schedule-info">
                <div class="time-slot">{{ getSchedule(current).timeSlot }}</div>
                <div class="status">{{ getSchedule(current).status }}</div>
              </div>
            </div>
          </template>
        </a-calendar>
      </div>
    </a-modal>

    <!-- 业绩查看模态框 -->
    <a-modal
      v-model:visible="performanceVisible"
      title="技师业绩"
      width="800px"
      :footer="null"
    >
      <div class="performance-content">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-statistic title="本月服务次数" :value="currentPerformance.monthlyServices" prefix="🔧" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="客户满意度" :value="currentPerformance.satisfaction" suffix="%" prefix="😊" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="本月收入" :value="currentPerformance.monthlyIncome" prefix="¥" />
          </a-col>
          <a-col :span="6">
            <a-statistic title="平均评分" :value="currentPerformance.avgRating" :precision="1" suffix="/5.0" prefix="⭐" />
          </a-col>
        </a-row>
        
        <div class="performance-chart">
          <h3>最近6个月业绩趋势</h3>
          <!-- 这里可以集成图表库如 Chart.js 或 ECharts -->
          <div class="chart-placeholder">
            <p>业绩图表（待集成图表库）</p>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import type { Dayjs } from 'dayjs';
import axios from 'axios';

// 响应式数据
const loading = ref(false);
const technicians = ref([]);
const searchKeyword = ref('');
const modalVisible = ref(false);
const scheduleVisible = ref(false);
const performanceVisible = ref(false);
const editingTechnician = ref(null);
const selectedDate = ref<Dayjs>();
const currentPerformance = ref({});

// 统计数据
const technicianStats = ref({
  total: 12,
  online: 8,
  todayServices: 25,
  avgRating: 4.5
});

// 表单数据
const formData = reactive({
  name: '',
  phone: '',
  experience: '',
  joinDate: null,
  skills: [],
  description: ''
});

// 表单验证规则
const formRules = {
  name: [{ required: true, message: '请输入技师姓名' }],
  phone: [
    { required: true, message: '请输入联系电话' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
  ],
  experience: [{ required: true, message: '请选择工作经验' }],
  skills: [{ required: true, message: '请选择专业技能' }]
};

// 技能选项
const skillOptions = [
  { label: '发动机维修', value: '发动机维修' },
  { label: '变速箱维修', value: '变速箱维修' },
  { label: '刹车系统', value: '刹车系统' },
  { label: '空调系统', value: '空调系统' },
  { label: '电子系统', value: '电子系统' },
  { label: '轮胎服务', value: '轮胎服务' },
  { label: '钣金喷漆', value: '钣金喷漆' },
  { label: '美容养护', value: '美容养护' }
];

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条数据`
});

// 表格列定义
const columns = [
  {
    title: '头像',
    key: 'avatar',
    width: 80
  },
  {
    title: '技师姓名',
    dataIndex: 'name',
    key: 'name',
    width: 120
  },
  {
    title: '联系电话',
    dataIndex: 'phone',
    key: 'phone',
    width: 130
  },
  {
    title: '工作经验',
    dataIndex: 'experienceText',
    key: 'experience',
    width: 100
  },
  {
    title: '专业技能',
    key: 'skills',
    width: 200
  },
  {
    title: '状态',
    key: 'status',
    width: 80
  },
  {
    title: '评分',
    key: 'rating',
    width: 150
  },
  {
    title: '操作',
    key: 'action',
    width: 250
  }
];

// 加载技师数据
const loadTechnicians = async () => {
  try {
    loading.value = true;
    // 模拟数据
    technicians.value = [
      {
        id: 1,
        name: '张师傅',
        phone: '13800138001',
        experience: '3',
        experienceText: '3-5年',
        skills: ['发动机维修', '变速箱维修'],
        status: 1,
        rating: 4.8,
        avatar: '/image/default-service.png',
        description: '资深技师，专业维修经验丰富'
      },
      {
        id: 2,
        name: '李师傅',
        phone: '13800138002',
        experience: '4',
        experienceText: '5-10年',
        skills: ['刹车系统', '空调系统', '电子系统'],
        status: 1,
        rating: 4.6,
        avatar: '/image/default-service.png',
        description: '精通各类汽车电子系统维修'
      },
      {
        id: 3,
        name: '王师傅',
        phone: '13800138003',
        experience: '2',
        experienceText: '1-3年',
        skills: ['轮胎服务', '美容养护'],
        status: 0,
        rating: 4.3,
        avatar: '/image/default-service.png',
        description: '年轻有为，服务态度优秀'
      }
    ];
    pagination.total = technicians.value.length;
  } catch (error) {
    message.error('加载技师数据失败');
  } finally {
    loading.value = false;
  }
};

// 搜索处理
const handleSearch = () => {
  pagination.current = 1;
  loadTechnicians();
};

// 表格变化处理
const handleTableChange = (pag: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  loadTechnicians();
};

// 显示添加模态框
const showAddModal = () => {
  editingTechnician.value = null;
  Object.assign(formData, {
    name: '',
    phone: '',
    experience: '',
    joinDate: null,
    skills: [],
    description: ''
  });
  modalVisible.value = true;
};

// 编辑技师
const editTechnician = (record: any) => {
  editingTechnician.value = record;
  Object.assign(formData, { ...record });
  modalVisible.value = true;
};

// 提交表单
const handleSubmit = async () => {
  try {
    // 这里应该调用后端API
    message.success(editingTechnician.value ? '编辑成功' : '添加成功');
    modalVisible.value = false;
    loadTechnicians();
  } catch (error) {
    message.error('操作失败');
  }
};

// 取消操作
const handleCancel = () => {
  modalVisible.value = false;
};

// 删除技师
const deleteTechnician = async (id: number) => {
  try {
    // 这里应该调用后端API
    message.success('删除成功');
    loadTechnicians();
  } catch (error) {
    message.error('删除失败');
  }
};

// 查看排班
const viewSchedule = (technicianId: number) => {
  scheduleVisible.value = true;
};

// 查看业绩
const viewPerformance = (technicianId: number) => {
  currentPerformance.value = {
    monthlyServices: 45,
    satisfaction: 96,
    monthlyIncome: 8500,
    avgRating: 4.7
  };
  performanceVisible.value = true;
};

// 获取排班信息
const getSchedule = (date: Dayjs) => {
  // 这里应该根据日期获取排班信息
  return null;
};

// 选择日期处理
const handleDateSelect = (date: Dayjs) => {
  selectedDate.value = date;
};

// 组件挂载时加载数据
onMounted(() => {
  loadTechnicians();
});
</script>

<style scoped>
.technician-management {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0;
  color: #1890ff;
  font-size: 24px;
  font-weight: 600;
}

.technician-stats {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.technician-list-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-header span {
  font-weight: 600;
  color: #1890ff;
}

.rating-text {
  margin-left: 8px;
  color: #666;
  font-size: 12px;
}

.schedule-content, .performance-content {
  margin-top: 16px;
}

.schedule-cell {
  min-height: 60px;
  position: relative;
}

.schedule-info {
  background: #e6f7ff;
  border-radius: 4px;
  padding: 4px;
  font-size: 10px;
}

.time-slot {
  color: #1890ff;
  font-weight: 500;
}

.status {
  color: #52c41a;
}

.performance-chart {
  margin-top: 24px;
}

.performance-chart h3 {
  margin-bottom: 16px;
  color: #1890ff;
}

.chart-placeholder {
  height: 300px;
  background: #fafafa;
  border: 1px dashed #d9d9d9;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .technician-management {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .table-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
}
</style>
