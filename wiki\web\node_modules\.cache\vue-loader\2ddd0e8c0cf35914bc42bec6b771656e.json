{"remainingRequest": "D:\\JavaCar\\wiki\\wiki\\web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\JavaCar\\wiki\\wiki\\web\\src\\views\\Register.vue?vue&type=script&lang=ts", "dependencies": [{"path": "D:\\JavaCar\\wiki\\wiki\\web\\src\\views\\Register.vue", "mtime": 1757594878524}, {"path": "D:\\JavaCar\\wiki\\wiki\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750678170000}, {"path": "D:\\JavaCar\\wiki\\wiki\\web\\node_modules\\ts-loader\\index.js", "mtime": 1750678172000}, {"path": "D:\\JavaCar\\wiki\\wiki\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750678170000}, {"path": "D:\\JavaCar\\wiki\\wiki\\web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1750678172000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["D:\\JavaCar\\wiki\\wiki\\web\\src\\views\\Register.vue"], "names": [], "mappings": ";AAgXA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5D,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;EACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACpB,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AAC9B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACxC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzB,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEtC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EACpB,CAAC;EACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;IACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC1B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACvC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;IAE7B,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACnB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACtB,CAAC,CAAC;;IAEF,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC9B,CAAC,CAAC;;IAEF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;MACvC,CAAC,CAAC,EAAE;QACF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACzB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD;MACF,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACnC;IACF,CAAC;;;IAGD,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;MACnC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACxD,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACtD,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACpE,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACL,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC1D,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC1D,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACrD,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACnD,CAAC;QACC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UACf,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;UACrD;YACE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;cACxC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cACpC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAC1D,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;gBACzC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;cAChD;cACA,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1B,CAAC;YACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC5B;QACF,CAAC;MACH,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACtD,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAC/D,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACvD,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChE,CAAC;MACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACX,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACvD,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChE;IACF,CAAC,CAAC;;IAEF,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;IAElD,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;MACjC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACrB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACR;;MAEA,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,EAAE;QACF,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACf,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE;UACvB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC/B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACrC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACnD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACtC,CAAC;;QAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;UACrC,CAAC,EAAE,CAAC,CAAC,CAAC;UACN,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACpD,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;UAC5C,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAClD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UACxD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjE;;QAEA,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAChE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;;QAE1B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;UAC3B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,EAAE,CAAC,CAAC,CAAC,EAAE;UACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvC;MACF,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACzC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;QACR,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACvB;IACF,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;MACtB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC;;IAED,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;MACxB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC;IACnC,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC,CAAC,CAAC,CAAC;MACH,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACjC,CAAC,EAAE,CAAC,EAAE;QACJ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC/D,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3D;MACF;IACF,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACf,CAAC,CAAC,CAAC,CAAC,CAAC;MACH,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACxC,CAAC,EAAE,CAAC,EAAE;QACJ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;UAC9F,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC3D;MACF;IACF,CAAC;;IAED,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACxB,CAAC,CAAC;;IAEF,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MACL,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACZ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACf,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACP,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;MACT,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACZ,CAAC;EACH;AACF,CAAC,CAAC", "file": "D:/JavaCar/wiki/wiki/web/src/views/Register.vue", "sourceRoot": "", "sourcesContent": ["<template>\n  <div class=\"register-container\">\n    <div class=\"register-box\">\n      <div class=\"register-header\">\n        <img src=\"/images/logo.png\" alt=\"汽车维修\" class=\"logo\" />\n        <h2>注册汽车维修服务平台</h2>\n        <p>加入我们，享受专业的汽车维修服务</p>\n      </div>\n      \n      <a-form\n        ref=\"registerFormRef\"\n        :model=\"registerForm\"\n        :rules=\"registerRules\"\n        @finish=\"handleRegister\"\n        layout=\"vertical\"\n        class=\"register-form\"\n      >\n        <!-- 用户类型选择 -->\n        <a-form-item name=\"userType\" label=\"用户类型\">\n          <a-radio-group\n            v-model:value=\"registerForm.userType\"\n            size=\"large\"\n            class=\"user-type-radio\"\n          >\n            <a-radio :value=\"1\" class=\"radio-option\">\n              <div class=\"radio-content\">\n                <CarOutlined class=\"radio-icon\" />\n                <div class=\"radio-text\">\n                  <div class=\"radio-title\">车主</div>\n                  <div class=\"radio-desc\">预约维修服务，管理车辆信息</div>\n                </div>\n              </div>\n            </a-radio>\n            <a-radio :value=\"2\" class=\"radio-option\">\n              <div class=\"radio-content\">\n                <ShopOutlined class=\"radio-icon\" />\n                <div class=\"radio-text\">\n                  <div class=\"radio-title\">维修店</div>\n                  <div class=\"radio-desc\">提供维修服务，管理技师和订单</div>\n                </div>\n              </div>\n            </a-radio>\n          </a-radio-group>\n        </a-form-item>\n\n        <!-- 车主用户名字段 -->\n        <a-form-item \n          v-if=\"registerForm.userType === 1\" \n          name=\"username\" \n          label=\"用户名\"\n        >\n          <a-input\n            v-model:value=\"registerForm.username\"\n            size=\"large\"\n            placeholder=\"请输入用户名\"\n          >\n            <template #prefix>\n              <UserOutlined />\n            </template>\n          </a-input>\n        </a-form-item>\n\n        <!-- 维修店名字段 -->\n        <a-form-item \n          v-if=\"registerForm.userType === 2\" \n          name=\"shopName\" \n          label=\"维修店名\"\n        >\n          <a-input\n            v-model:value=\"registerForm.shopName\"\n            size=\"large\"\n            placeholder=\"请输入维修店名称\"\n          >\n            <template #prefix>\n              <ShopOutlined />\n            </template>\n          </a-input>\n        </a-form-item>\n        \n        <a-form-item name=\"realName\" label=\"真实姓名\">\n          <a-input\n            v-model:value=\"registerForm.realName\"\n            size=\"large\"\n            placeholder=\"请输入真实姓名\"\n          >\n            <template #prefix>\n              <IdcardOutlined />\n            </template>\n          </a-input>\n        </a-form-item>\n\n        <a-form-item name=\"phone\" label=\"手机号\">\n          <a-input\n            v-model:value=\"registerForm.phone\"\n            size=\"large\"\n            placeholder=\"请输入手机号\"\n          >\n            <template #prefix>\n              <PhoneOutlined />\n            </template>\n          </a-input>\n        </a-form-item>\n\n        <a-form-item name=\"email\" label=\"邮箱\">\n          <a-input\n            v-model:value=\"registerForm.email\"\n            size=\"large\"\n            placeholder=\"请输入邮箱（可选）\"\n          >\n            <template #prefix>\n              <MailOutlined />\n            </template>\n          </a-input>\n        </a-form-item>\n\n        <!-- 维修店地址字段 -->\n        <a-form-item \n          v-if=\"registerForm.userType === 2\" \n          name=\"shopAddress\" \n          label=\"地铺地址\"\n        >\n          <a-input\n            v-model:value=\"registerForm.shopAddress\"\n            size=\"large\"\n            placeholder=\"请输入维修店地址\"\n          >\n            <template #prefix>\n              <EnvironmentOutlined />\n            </template>\n          </a-input>\n        </a-form-item>\n        \n        <a-form-item name=\"password\" label=\"密码\">\n          <a-input-password\n            v-model:value=\"registerForm.password\"\n            size=\"large\"\n            placeholder=\"请输入密码\"\n            autocomplete=\"new-password\"\n          >\n            <template #prefix>\n              <LockOutlined />\n            </template>\n          </a-input-password>\n        </a-form-item>\n\n        <a-form-item name=\"confirmPassword\" label=\"确认密码\">\n          <a-input-password\n            v-model:value=\"registerForm.confirmPassword\"\n            size=\"large\"\n            placeholder=\"请再次输入密码\"\n            autocomplete=\"new-password\"\n          >\n            <template #prefix>\n              <LockOutlined />\n            </template>\n          </a-input-password>\n        </a-form-item>\n        \n        <a-form-item>\n          <a-checkbox v-model:checked=\"agreeTerms\">\n            我已阅读并同意 <a href=\"#\" @click.prevent=\"showTerms\">《用户协议》</a> 和 <a href=\"#\" @click.prevent=\"showPrivacy\">《隐私政策》</a>\n          </a-checkbox>\n        </a-form-item>\n        \n        <a-form-item>\n          <a-button\n            type=\"primary\"\n            html-type=\"submit\"\n            size=\"large\"\n            :loading=\"loading\"\n            :disabled=\"!agreeTerms\"\n            class=\"register-button\"\n          >\n            注册\n          </a-button>\n        </a-form-item>\n        \n        <div class=\"login-link\">\n          已有账户？ \n          <router-link to=\"/login\">立即登录</router-link>\n        </div>\n      </a-form>\n    </div>\n\n    <!-- 用户协议模态框 -->\n    <a-modal\n      v-model:visible=\"userAgreementVisible\"\n      title=\"用户协议\"\n      width=\"800px\"\n      :footer=\"null\"\n      class=\"agreement-modal\"\n    >\n      <div class=\"agreement-content\">\n        <h3>汽车维修服务平台用户协议</h3>\n        \n        <h4>第一条 协议的范围和效力</h4>\n        <p>1.1 本协议是您与汽车维修服务平台之间关于使用平台服务所订立的协议。</p>\n        <p>1.2 您通过注册、登录、使用平台服务，即表示您已阅读、理解并完全接受本协议的全部条款。</p>\n        \n        <h4>第二条 服务内容</h4>\n        <p>2.1 平台为车主用户提供汽车维修服务预约、维修店查找、服务评价等功能。</p>\n        <p>2.2 平台为维修店用户提供服务发布、订单管理、客户沟通等功能。</p>\n        <p>2.3 平台有权根据业务发展需要，增加、修改或终止部分服务功能。</p>\n        \n        <h4>第三条 用户注册和账户管理</h4>\n        <p>3.1 用户应提供真实、准确、完整的注册信息。</p>\n        <p>3.2 用户对账户和密码的安全负有责任，因账户被盗用造成的损失由用户自行承担。</p>\n        <p>3.3 用户不得将账户转让、出售或以其他方式提供给第三方使用。</p>\n        \n        <h4>第四条 用户行为规范</h4>\n        <p>4.1 用户应遵守国家法律法规，不得利用平台从事违法违规活动。</p>\n        <p>4.2 用户不得发布虚假信息、恶意评价或进行其他损害平台声誉的行为。</p>\n        <p>4.3 维修店用户应确保提供的服务符合相关技术标准和安全要求。</p>\n        \n        <h4>第五条 服务费用和支付</h4>\n        <p>5.1 平台基础服务免费提供，部分增值服务可能收取相应费用。</p>\n        <p>5.2 维修服务费用由车主与维修店直接结算，平台不参与资金交易。</p>\n        <p>5.3 平台有权调整收费标准，并提前30天通知用户。</p>\n        \n        <h4>第六条 知识产权</h4>\n        <p>6.1 平台的所有内容，包括但不限于文字、图片、软件等，均受知识产权法保护。</p>\n        <p>6.2 用户在平台发布的内容，应确保不侵犯他人知识产权。</p>\n        \n        <h4>第七条 免责声明</h4>\n        <p>7.1 平台仅提供信息发布和匹配服务，对维修服务的质量不承担直接责任。</p>\n        <p>7.2 因不可抗力、系统故障等原因导致的服务中断，平台不承担责任。</p>\n        \n        <h4>第八条 协议修改和终止</h4>\n        <p>8.1 平台有权修改本协议，修改后的协议将在平台公布。</p>\n        <p>8.2 用户违反协议条款的，平台有权终止向该用户提供服务。</p>\n        \n        <h4>第九条 争议解决</h4>\n        <p>9.1 因本协议产生的争议，双方应友好协商解决。</p>\n        <p>9.2 协商不成的，任何一方均可向平台所在地人民法院提起诉讼。</p>\n        \n        <p class=\"effective-date\">本协议自2024年1月1日起生效。</p>\n      </div>\n      \n      <div class=\"modal-footer\">\n        <a-button type=\"primary\" @click=\"userAgreementVisible = false\">\n          我已阅读并同意\n        </a-button>\n        <a-button @click=\"userAgreementVisible = false\" style=\"margin-left: 8px;\">\n          关闭\n        </a-button>\n      </div>\n    </a-modal>\n\n    <!-- 隐私政策模态框 -->\n    <a-modal\n      v-model:visible=\"privacyPolicyVisible\"\n      title=\"隐私政策\"\n      width=\"800px\"\n      :footer=\"null\"\n      class=\"agreement-modal\"\n    >\n      <div class=\"agreement-content\">\n        <h3>汽车维修服务平台隐私政策</h3>\n        \n        <h4>第一条 信息收集</h4>\n        <p>1.1 我们收集您主动提供的信息：</p>\n        <ul>\n          <li>注册信息：用户名、真实姓名、手机号、邮箱等</li>\n          <li>车主用户：车辆信息、维修历史等</li>\n          <li>维修店用户：店铺信息、服务项目、营业执照等</li>\n        </ul>\n        \n        <p>1.2 我们自动收集的信息：</p>\n        <ul>\n          <li>设备信息：设备型号、操作系统、应用版本等</li>\n          <li>日志信息：IP地址、访问时间、操作记录等</li>\n          <li>位置信息：用于提供附近维修店推荐服务</li>\n        </ul>\n        \n        <h4>第二条 信息使用</h4>\n        <p>2.1 提供和改善服务：</p>\n        <ul>\n          <li>为您匹配合适的维修服务</li>\n          <li>处理您的服务请求和投诉</li>\n          <li>改善用户体验和服务质量</li>\n        </ul>\n        \n        <p>2.2 安全保护：</p>\n        <ul>\n          <li>验证用户身份，防止欺诈行为</li>\n          <li>检测和预防安全威胁</li>\n          <li>维护平台运行安全</li>\n        </ul>\n        \n        <p>2.3 法律义务：</p>\n        <ul>\n          <li>遵守适用的法律法规要求</li>\n          <li>配合监管部门的合法调查</li>\n        </ul>\n        \n        <h4>第三条 信息共享</h4>\n        <p>3.1 我们不会向第三方出售、租赁或交易您的个人信息。</p>\n        <p>3.2 在以下情况下，我们可能共享您的信息：</p>\n        <ul>\n          <li>获得您的明确同意</li>\n          <li>为完成服务匹配（如向维修店提供车主联系方式）</li>\n          <li>法律法规要求或政府部门要求</li>\n          <li>保护平台和用户的合法权益</li>\n        </ul>\n        \n        <h4>第四条 信息存储和安全</h4>\n        <p>4.1 数据存储：</p>\n        <ul>\n          <li>您的信息将存储在中国境内的服务器上</li>\n          <li>我们将在必要期限内保留您的信息</li>\n        </ul>\n        \n        <p>4.2 安全措施：</p>\n        <ul>\n          <li>采用行业标准的加密技术保护数据传输</li>\n          <li>建立严格的数据访问权限控制</li>\n          <li>定期进行安全评估和漏洞检测</li>\n        </ul>\n        \n        <h4>第五条 您的权利</h4>\n        <p>5.1 您有权：</p>\n        <ul>\n          <li>查询、更正您的个人信息</li>\n          <li>删除您的个人信息（法律法规另有规定的除外）</li>\n          <li>撤回您的信息使用同意</li>\n          <li>要求我们停止处理您的个人信息</li>\n        </ul>\n        \n        <p>5.2 行使权利方式：</p>\n        <ul>\n          <li>通过平台设置页面进行操作</li>\n          <li>联系客服热线：400-123-4567</li>\n          <li>发送邮件至：<EMAIL></li>\n        </ul>\n        \n        <h4>第六条 未成年人保护</h4>\n        <p>6.1 我们不会主动收集未满18周岁的未成年人个人信息。</p>\n        <p>6.2 如发现收集了未成年人信息，我们将立即删除相关数据。</p>\n        \n        <h4>第七条 政策更新</h4>\n        <p>7.1 我们可能不时更新本隐私政策。</p>\n        <p>7.2 重大变更将通过平台公告或其他方式通知您。</p>\n        <p>7.3 继续使用服务即视为您接受更新后的政策。</p>\n        \n        <h4>第八条 联系我们</h4>\n        <p>如果您对本隐私政策有任何疑问，请通过以下方式联系我们：</p>\n        <ul>\n          <li>客服热线：400-123-4567</li>\n          <li>邮箱：<EMAIL></li>\n          <li>地址：北京市朝阳区某某街道123号</li>\n        </ul>\n        \n        <p class=\"effective-date\">本政策自2024年1月1日起生效。</p>\n      </div>\n      \n      <div class=\"modal-footer\">\n        <a-button type=\"primary\" @click=\"privacyPolicyVisible = false\">\n          我已阅读并了解\n        </a-button>\n        <a-button @click=\"privacyPolicyVisible = false\" style=\"margin-left: 8px;\">\n          关闭\n        </a-button>\n      </div>\n    </a-modal>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent, ref, onMounted, watch } from 'vue';\nimport { \n  UserOutlined, \n  LockOutlined, \n  PhoneOutlined, \n  MailOutlined,\n  IdcardOutlined,\n  CarOutlined,\n  ShopOutlined,\n  EnvironmentOutlined\n} from '@ant-design/icons-vue';\nimport { message } from 'ant-design-vue';\nimport axios from 'axios';\nimport { useRouter } from 'vue-router';\n\nexport default defineComponent({\n  name: 'Register',\n  components: {\n    UserOutlined,\n    LockOutlined,\n    PhoneOutlined,\n    MailOutlined,\n    IdcardOutlined,\n    CarOutlined,\n    ShopOutlined,\n    EnvironmentOutlined\n  },\n  setup() {\n    const router = useRouter();\n    const loading = ref(false);\n    const agreeTerms = ref(false);\n    const userAgreementVisible = ref(false);\n    const privacyPolicyVisible = ref(false);\n    const registerFormRef = ref();\n\n    const registerForm = ref({\n      username: '',\n      shopName: '',\n      realName: '',\n      phone: '',\n      email: '',\n      shopAddress: '',\n      password: '',\n      confirmPassword: '',\n      userType: 1 // 默认选择车主\n    });\n\n    // 安全设置\n    const securitySettings = ref({\n      minPasswordLength: 6,\n      requireComplexPassword: false\n    });\n    \n    // 加载安全设置\n    const loadSecuritySettings = async () => {\n      try {\n        const response = await axios.get('/api/admin/security-settings');\n        if (response.data.success) {\n          securitySettings.value = response.data.content;\n        }\n      } catch (error) {\n        console.error('加载安全设置失败:', error);\n      }\n    };\n\n    \n    // 创建验证规则\n    const createValidationRules = () => ({\n      realName: [\n        { required: true, message: '请输入真实姓名', trigger: 'blur' }\n      ],\n      phone: [\n        { required: true, message: '请输入手机号', trigger: 'blur' },\n        { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }\n      ],\n      email: [\n        { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }\n      ],\n      userType: [\n        { required: true, message: '请选择用户类型', trigger: 'change' }\n      ],\n      password: [\n        { required: true, message: '请输入密码', trigger: 'blur' },\n        { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }\n      ],\n        confirmPassword: [\n          { required: true, message: '请确认密码', trigger: 'blur' },\n          {\n            validator: (_rule: any, value: string) => {\n              if (!value) return Promise.resolve();\n              if (!registerForm.value.password) return Promise.resolve();\n              if (value !== registerForm.value.password) {\n                return Promise.reject(new Error('两次输入的密码不一致'));\n              }\n              return Promise.resolve();\n            },\n            trigger: ['blur', 'change']\n          }\n        ],\n      username: [\n        { required: true, message: '请输入用户名', trigger: 'blur' },\n        { min: 3, max: 20, message: '用户名长度为3-20个字符', trigger: 'blur' }\n      ],\n      shopName: [\n        { required: true, message: '请输入维修店名', trigger: 'blur' },\n        { min: 2, max: 50, message: '维修店名长度为2-50个字符', trigger: 'blur' }\n      ],\n      shopAddress: [\n        { required: true, message: '请输入地铺地址', trigger: 'blur' },\n        { min: 5, max: 200, message: '地址长度为5-200个字符', trigger: 'blur' }\n      ]\n    });\n\n    const registerRules = ref(createValidationRules());\n    \n    const handleRegister = async () => {\n      if (!agreeTerms.value) {\n        message.warning('请先同意用户协议和隐私政策');\n        return;\n      }\n      \n      loading.value = true;\n      try {\n        // 根据用户类型准备不同的数据\n        const requestData: any = {\n          realName: registerForm.value.realName,\n          phone: registerForm.value.phone,\n          email: registerForm.value.email,\n          password: registerForm.value.password,\n          confirmPassword: registerForm.value.confirmPassword,\n          userType: registerForm.value.userType\n        };\n\n        if (registerForm.value.userType === 1) {\n          // 车主用户\n          requestData.username = registerForm.value.username;\n        } else if (registerForm.value.userType === 2) {\n          // 维修店用户\n          requestData.shopName = registerForm.value.shopName;\n          requestData.shopAddress = registerForm.value.shopAddress;\n          requestData.username = registerForm.value.shopName; // 使用店名作为用户名\n        }\n\n        const response = await axios.post('/auth/register', requestData);\n        const data = response.data;\n        \n        if (data.success) {\n          message.success('注册成功，请登录');\n          router.push('/login');\n        } else {\n          message.error(data.message || '注册失败');\n        }\n      } catch (error) {\n        message.error('注册失败，请检查网络连接');\n        console.error('Register error:', error);\n      } finally {\n        loading.value = false;\n      }\n    };\n    \n    const showTerms = () => {\n      userAgreementVisible.value = true;\n    };\n    \n    const showPrivacy = () => {\n      privacyPolicyVisible.value = true;\n    };\n\n    // 监听密码变化，重新验证确认密码\n    watch(\n      () => registerForm.value.password,\n      () => {\n        // 当密码改变时，如果确认密码已有值，则重新验证确认密码\n        if (registerForm.value.confirmPassword && registerFormRef.value) {\n          registerFormRef.value.validateFields(['confirmPassword']);\n        }\n      }\n    );\n\n    // 监听确认密码变化，实时验证\n    watch(\n      () => registerForm.value.confirmPassword,\n      () => {\n        // 当确认密码改变时，如果密码已有值，则重新验证确认密码\n        if (registerForm.value.password && registerForm.value.confirmPassword && registerFormRef.value) {\n          registerFormRef.value.validateFields(['confirmPassword']);\n        }\n      }\n    );\n\n    // 组件挂载时加载安全设置\n    onMounted(() => {\n      loadSecuritySettings();\n    });\n\n    return {\n      registerForm,\n      registerFormRef,\n      registerRules,\n      loading,\n      agreeTerms,\n      securitySettings,\n      userAgreementVisible,\n      privacyPolicyVisible,\n      handleRegister,\n      showTerms,\n      showPrivacy\n    };\n  }\n});\n</script>\n\n<style scoped>\n.register-container {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 20px;\n}\n\n.register-box {\n  background: white;\n  padding: 40px;\n  border-radius: 12px;\n  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);\n  width: 100%;\n  max-width: 450px;\n  max-height: 90vh;\n  overflow-y: auto;\n}\n\n.register-header {\n  text-align: center;\n  margin-bottom: 32px;\n}\n\n.logo {\n  width: 60px;\n  height: 60px;\n  margin-bottom: 16px;\n}\n\n.register-header h2 {\n  color: #333;\n  margin-bottom: 8px;\n  font-size: 24px;\n}\n\n.register-header p {\n  color: #666;\n  margin-bottom: 0;\n}\n\n.register-form {\n  margin-top: 32px;\n}\n\n.register-button {\n  width: 100%;\n  height: 44px;\n  font-size: 16px;\n}\n\n.login-link {\n  text-align: center;\n  margin-top: 24px;\n  color: #666;\n}\n\n.login-link a {\n  color: #1890ff;\n  text-decoration: none;\n}\n\n.login-link a:hover {\n  text-decoration: underline;\n}\n\n.ant-checkbox-wrapper a {\n  color: #1890ff;\n  text-decoration: none;\n}\n\n.ant-checkbox-wrapper a:hover {\n  text-decoration: underline;\n}\n\n/* 用户类型选择样式 */\n.user-type-radio {\n  width: 100%;\n}\n\n.radio-option {\n  width: 100%;\n  margin-bottom: 12px;\n  padding: 16px;\n  border: 2px solid #e8e8e8;\n  border-radius: 8px;\n  transition: all 0.3s;\n  display: flex;\n  align-items: flex-start;\n}\n\n.radio-option:hover {\n  border-color: #1890ff;\n  background-color: #f0f5ff;\n}\n\n.radio-option.ant-radio-wrapper-checked {\n  border-color: #1890ff;\n  background-color: #f0f5ff;\n  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);\n}\n\n.radio-content {\n  display: flex;\n  align-items: center;\n  width: 100%;\n  margin-left: 8px;\n}\n\n.radio-icon {\n  font-size: 24px;\n  color: #1890ff;\n  margin-right: 12px;\n}\n\n.radio-text {\n  flex: 1;\n}\n\n.radio-title {\n  font-weight: 600;\n  font-size: 16px;\n  color: #333;\n  margin-bottom: 4px;\n}\n\n.radio-desc {\n  font-size: 14px;\n  color: #666;\n  line-height: 1.4;\n}\n\n/* 协议模态框样式 */\n.agreement-modal .ant-modal-body {\n  max-height: 60vh;\n  overflow-y: auto;\n  padding: 24px;\n}\n\n.agreement-content {\n  line-height: 1.6;\n  color: #333;\n}\n\n.agreement-content h3 {\n  text-align: center;\n  color: #1890ff;\n  margin-bottom: 24px;\n  font-size: 20px;\n  font-weight: 600;\n}\n\n.agreement-content h4 {\n  color: #333;\n  margin: 20px 0 12px 0;\n  font-size: 16px;\n  font-weight: 600;\n  border-left: 4px solid #1890ff;\n  padding-left: 12px;\n}\n\n.agreement-content p {\n  margin: 8px 0;\n  text-indent: 0;\n  font-size: 14px;\n}\n\n.agreement-content ul {\n  margin: 8px 0;\n  padding-left: 20px;\n}\n\n.agreement-content li {\n  margin: 4px 0;\n  font-size: 14px;\n}\n\n.effective-date {\n  text-align: center;\n  font-weight: 600;\n  color: #666;\n  margin-top: 24px;\n  padding-top: 16px;\n  border-top: 1px solid #e8e8e8;\n}\n\n.modal-footer {\n  text-align: center;\n  padding: 16px 0;\n  border-top: 1px solid #e8e8e8;\n  margin-top: 16px;\n}\n</style>\n"]}]}