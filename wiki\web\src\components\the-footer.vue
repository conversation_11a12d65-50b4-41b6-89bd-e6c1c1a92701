<template>
     <div class="footer">
        <div class="footer-content">
            <div class="footer-logo">
                <span class="logo-text">汽车维修服务平台</span>
                <p class="slogan">专业汽车维修保养服务</p>
            </div>
            <div class="footer-links">
                <h3>快速链接</h3>
                <ul>
                    <li><router-link to="/">首页</router-link></li>
                    <li><a href="#services">服务项目</a></li>
                    <li><router-link to="/booking">立即预约</router-link></li>
                    <li><a href="#about">关于我们</a></li>
                </ul>
            </div>
            <div class="footer-contact">
                <h3>联系我们</h3>
                <p><mail-outlined /> <EMAIL></p>
                <p><phone-outlined /> ************</p>
                <p><environment-outlined /> 北京市朝阳区汽车街123号</p>
            </div>
        </div>
        <div class="copyright">
            © {{ currentYear }} 汽车维修服务平台 | 版权所有
        </div>
     </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';
import { MailOutlined, PhoneOutlined, EnvironmentOutlined } from '@ant-design/icons-vue';

export default defineComponent({
  name: 'the-footer',
  components: {
    MailOutlined,
    PhoneOutlined,
    EnvironmentOutlined
  },
  setup() {
    const currentYear = ref(new Date().getFullYear());
    
    return {
      currentYear
    };
  }
});
</script>

<style scoped>
.footer {
    background: #001529;
    color: #fff;
    padding: 24px 50px;
    margin-top: auto;
    flex-shrink: 0;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    margin-bottom: 24px;
}

.footer-logo {
    flex: 1;
}

.logo-text {
    font-size: 30px;
    font-weight: bold;
    background: linear-gradient(45deg, #1890ff, #722ed1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.slogan {
    margin-top: 16px;
    color: rgba(255, 255, 255, 0.7);
}

.footer-links, .footer-contact {
    flex: 1;
}

.footer-links h3, .footer-contact h3 {
    color: #fff;
    margin-bottom: 16px;
    font-size: 16px;
}

.footer-links ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.footer-links ul li {
    margin-bottom: 8px;
}

.footer-links ul li a {
    color: rgba(255, 255, 255, 0.7);
    transition: color 0.3s;
}

.footer-links ul li a:hover {
    color: #fff;
}

.footer-contact p {
    margin-bottom: 8px;
    color: rgba(255, 255, 255, 0.7);
}

.copyright {
    text-align: center;
    color: rgba(255, 255, 255, 0.5);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .footer {
    padding: 20px 24px;
  }
  
  .footer-content {
    flex-direction: column;
    gap: 30px;
    text-align: center;
    margin-bottom: 20px;
  }
  
  .footer-logo, .footer-links, .footer-contact {
    flex: none;
  }
  
  .logo-text {
    font-size: 20px;
  }
  
  .footer-links h3, .footer-contact h3 {
    font-size: 14px;
    margin-bottom: 12px;
  }
  
  .footer-links ul li {
    margin-bottom: 6px;
  }
  
  .footer-contact p {
    margin-bottom: 6px;
    font-size: 14px;
  }
}

@media (max-width: 576px) {
  .footer {
    padding: 16px 20px;
  }
  
  .footer-content {
    gap: 24px;
  }
  
  .logo-text {
    font-size: 18px;
  }
  
  .slogan {
    font-size: 14px;
  }
  
  .footer-links h3, .footer-contact h3 {
    font-size: 13px;
  }
  
  .footer-contact p {
    font-size: 13px;
  }
}
</style>