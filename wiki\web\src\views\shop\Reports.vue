<template>
  <div class="shop-reports">
    <div class="page-header">
      <h1>数据报告</h1>
      <p>查看营收和服务统计数据</p>
    </div>

    <a-row :gutter="24">
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="本月订单"
            :value="stats.monthlyOrders"
            :value-style="{ color: '#3f8600' }"
          />
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="本月收入"
            :value="stats.monthlyRevenue"
            prefix="¥"
            :value-style="{ color: '#cf1322' }"
          />
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="客户满意度"
            :value="stats.customerSatisfaction"
            suffix="%"
            :value-style="{ color: '#1890ff' }"
          />
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card>
          <a-statistic
            title="服务项目"
            :value="stats.totalServices"
            :value-style="{ color: '#722ed1' }"
          />
        </a-card>
      </a-col>
    </a-row>

    <a-row :gutter="24" style="margin-top: 24px;">
      <a-col :span="12">
        <a-card title="营收趋势">
          <div class="chart-placeholder">
            <p>营收趋势图（需要集成图表库）</p>
            <div class="trend-list">
              <div class="trend-item" v-for="item in revenueTrend" :key="item.month">
                <span>{{ item.month }}</span>
                <span>¥{{ item.revenue }}</span>
              </div>
            </div>
          </div>
        </a-card>
      </a-col>
      <a-col :span="12">
        <a-card title="热门服务">
          <a-list
            :data-source="popularServices"
            size="small"
          >
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta
                  :title="item.name"
                  :description="`完成次数: ${item.count}次 | 收入: ¥${item.revenue}`"
                />
              </a-list-item>
            </template>
          </a-list>
        </a-card>
      </a-col>
    </a-row>

    <a-row :gutter="24" style="margin-top: 24px;">
      <a-col :span="24">
        <a-card title="客户评价统计">
          <a-row :gutter="16">
            <a-col :span="4" v-for="(rating, index) in ratingStats" :key="index">
              <div class="rating-stat">
                <div class="rating-stars">
                  <span v-for="i in 5" :key="i">
                    <StarFilled v-if="i <= rating.stars" style="color: #faad14;" />
                    <StarOutlined v-else style="color: #d9d9d9;" />
                  </span>
                </div>
                <div class="rating-count">{{ rating.count }}条</div>
                <div class="rating-percentage">{{ rating.percentage }}%</div>
              </div>
            </a-col>
          </a-row>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue';
import { StarFilled, StarOutlined } from '@ant-design/icons-vue';

export default defineComponent({
  name: 'ShopReports',
  components: {
    StarFilled,
    StarOutlined
  },
  setup() {
    const stats = ref({
      monthlyOrders: 156,
      monthlyRevenue: 28600,
      customerSatisfaction: 92,
      totalServices: 12
    });

    const revenueTrend = ref([
      { month: '1月', revenue: 18500 },
      { month: '2月', revenue: 22300 },
      { month: '3月', revenue: 28600 },
      { month: '4月', revenue: 25800 },
      { month: '5月', revenue: 31200 },
      { month: '6月', revenue: 28600 }
    ]);

    const popularServices = ref([
      { name: '机油更换', count: 45, revenue: 6750 },
      { name: '轮胎更换', count: 28, revenue: 11200 },
      { name: '刹车维修', count: 22, revenue: 6600 },
      { name: '空调清洗', count: 35, revenue: 7000 }
    ]);

    const ratingStats = ref([
      { stars: 5, count: 128, percentage: 65 },
      { stars: 4, count: 48, percentage: 24 },
      { stars: 3, count: 16, percentage: 8 },
      { stars: 2, count: 4, percentage: 2 },
      { stars: 1, count: 2, percentage: 1 }
    ]);

    return {
      stats,
      revenueTrend,
      popularServices,
      ratingStats
    };
  }
});
</script>

<style scoped>
.shop-reports {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #333;
}

.page-header p {
  margin: 0;
  color: #666;
}

.chart-placeholder {
  height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 4px;
}

.trend-list {
  margin-top: 16px;
  width: 100%;
}

.trend-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 16px;
  border-bottom: 1px solid #f0f0f0;
}

.rating-stat {
  text-align: center;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
}

.rating-stars {
  margin-bottom: 8px;
}

.rating-count {
  font-size: 14px;
  color: #666;
  margin-bottom: 4px;
}

.rating-percentage {
  font-size: 12px;
  color: #999;
}
</style>
