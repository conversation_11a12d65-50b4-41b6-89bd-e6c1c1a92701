/* 
 * 汽车维修服务平台 - 全局主题配置
 * 统一设计风格和变量定义
 */

:root {
  /* 主色调 */
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --primary-color: #667eea;
  --primary-light: rgba(102, 126, 234, 0.1);
  --primary-dark: #764ba2;
  
  /* 辅助色彩 */
  --secondary-gradient: linear-gradient(135deg, #f093fb, #f5576c);
  --accent-gradient-1: linear-gradient(135deg, #4facfe, #00f2fe);
  --accent-gradient-2: linear-gradient(135deg, #43e97b, #38f9d7);
  --accent-gradient-3: linear-gradient(135deg, #fa709a, #fee140);
  
  /* 中性色 */
  --text-primary: #1a1a1a;
  --text-secondary: #666;
  --text-tertiary: #999;
  --border-light: rgba(0, 0, 0, 0.06);
  --border-medium: rgba(0, 0, 0, 0.1);
  
  /* 背景色 */
  --bg-primary: #ffffff;
  --bg-secondary: rgba(255, 255, 255, 0.95);
  --bg-tertiary: rgba(248, 250, 252, 0.8);
  --bg-overlay: rgba(0, 0, 0, 0.3);
  
  /* 阴影 */
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 20px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.12);
  --shadow-xl: 0 16px 48px rgba(0, 0, 0, 0.15);
  
  /* 圆角 */
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 20px;
  --radius-full: 50%;
  
  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 20px;
  --spacing-2xl: 24px;
  --spacing-3xl: 32px;
  
  /* 字体大小 */
  --text-xs: 12px;
  --text-sm: 14px;
  --text-base: 16px;
  --text-lg: 18px;
  --text-xl: 20px;
  --text-2xl: 24px;
  --text-3xl: 28px;
  --text-4xl: 32px;
  
  /* 字体权重 */
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  
  /* 过渡动画 */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
  --transition-bounce: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* 模糊效果 */
  --blur-sm: blur(10px);
  --blur-md: blur(20px);
  --blur-lg: blur(30px);
  
  /* Z-index层级 */
  --z-dropdown: 1000;
  --z-sticky: 1010;
  --z-fixed: 1020;
  --z-modal-backdrop: 1030;
  --z-modal: 1040;
  --z-popover: 1050;
  --z-tooltip: 1060;
  --z-toast: 1070;
}

/* 全局重置和基础样式 */
* {
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background: var(--bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 通用工具类 */
.gradient-text {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-bg {
  background: var(--primary-gradient);
}

.glass-card {
  background: var(--bg-secondary);
  backdrop-filter: var(--blur-md);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
}

.hover-lift {
  transition: all var(--transition-bounce);
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.fade-in {
  animation: fadeIn 0.6s ease-out;
}

.slide-up {
  animation: slideUp 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 纹理背景 */
.texture-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.03)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
  z-index: 0;
}

/* 状态颜色 */
.status-pending {
  color: #fa8c16;
  background: rgba(250, 140, 22, 0.1);
}

.status-confirmed {
  color: #1890ff;
  background: rgba(24, 144, 255, 0.1);
}

.status-processing {
  color: #13c2c2;
  background: rgba(19, 194, 194, 0.1);
}

.status-completed {
  color: #52c41a;
  background: rgba(82, 196, 26, 0.1);
}

.status-cancelled {
  color: #ff4d4f;
  background: rgba(255, 77, 79, 0.1);
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: var(--radius-sm);
  transition: background var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* Ant Design 组件自定义样式 */
.ant-btn {
  border-radius: var(--radius-md);
  font-weight: var(--font-medium);
  transition: all var(--transition-normal);
}

.ant-btn-primary {
  background: var(--primary-gradient);
  border: none;
  box-shadow: var(--shadow-sm);
}

.ant-btn-primary:hover {
  background: var(--primary-gradient);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.ant-card {
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
}

.ant-card-head {
  border-bottom: 1px solid var(--border-light);
}

.ant-tag {
  border-radius: var(--radius-md);
  font-weight: var(--font-medium);
}

.ant-input,
.ant-select-selector {
  border-radius: var(--radius-md);
  transition: all var(--transition-normal);
}

.ant-input:focus,
.ant-select-focused .ant-select-selector {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.ant-modal {
  border-radius: var(--radius-xl);
  overflow: hidden;
}

.ant-modal-content {
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
}

.ant-pagination-item {
  border-radius: var(--radius-md);
}

.ant-pagination-item-active {
  background: var(--primary-gradient);
  border-color: var(--primary-color);
}

.ant-pagination-item-active a {
  color: white;
}

/* 响应式断点 */
@media (max-width: 480px) {
  :root {
    --text-4xl: 20px;
    --text-3xl: 24px;
    --text-2xl: 20px;
    --spacing-3xl: 20px;
    --spacing-2xl: 16px;
  }
}

@media (max-width: 768px) {
  :root {
    --text-4xl: 24px;
    --text-3xl: 28px;
    --spacing-3xl: 24px;
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    background: white !important;
    color: black !important;
  }
  
  .glass-card {
    background: white !important;
    backdrop-filter: none !important;
    border: 1px solid #ddd !important;
    box-shadow: none !important;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  :root {
    --text-primary: #000000;
    --text-secondary: #333333;
    --border-light: rgba(0, 0, 0, 0.2);
    --border-medium: rgba(0, 0, 0, 0.3);
  }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 深色模式支持（预留） */
@media (prefers-color-scheme: dark) {
  :root {
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-tertiary: #999999;
    --bg-primary: #1a1a1a;
    --bg-secondary: rgba(0, 0, 0, 0.8);
    --bg-tertiary: rgba(255, 255, 255, 0.1);
    --border-light: rgba(255, 255, 255, 0.1);
    --border-medium: rgba(255, 255, 255, 0.2);
  }
}
