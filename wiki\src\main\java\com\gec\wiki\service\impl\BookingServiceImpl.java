package com.gec.wiki.service.impl;

import com.gec.wiki.mapper.BookingMapper;
import com.gec.wiki.mapper.ServiceMapper;
import com.gec.wiki.pojo.Booking;
import com.gec.wiki.pojo.Service;
import com.gec.wiki.pojo.req.BookingCreateReq;
import com.gec.wiki.pojo.resp.CommonResp;
import com.gec.wiki.service.BookingService;
import com.gec.wiki.utils.SnowFlake;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 预约服务实现类
 */
@Component
public class BookingServiceImpl implements BookingService {
    
    private static final Logger LOG = LoggerFactory.getLogger(BookingServiceImpl.class);
    
    @Resource
    private BookingMapper bookingMapper;
    
    @Resource
    private ServiceMapper serviceMapper;
    
    @Resource
    private SnowFlake snowFlake;
    
    @Override
    public CommonResp<Object> createBooking(BookingCreateReq req, Long userId) {
        CommonResp<Object> resp = new CommonResp<>();
        
        try {
            // 参数校验
            if (req.getServiceId() == null || req.getVehicleId() == null || 
                !StringUtils.hasText(req.getContactName()) || !StringUtils.hasText(req.getContactPhone()) ||
                req.getBookingDate() == null || req.getBookingTime() == null ||
                !StringUtils.hasText(req.getProblemDescription())) {
                resp.setSuccess(false);
                resp.setMessage("必填信息不能为空");
                return resp;
            }
            
            // 检查预约日期是否为未来日期
            if (req.getBookingDate().isBefore(LocalDate.now())) {
                resp.setSuccess(false);
                resp.setMessage("预约日期不能是过去日期");
                return resp;
            }
            
            // 获取服务信息
            Service service = serviceMapper.selectById(req.getServiceId());
            if (service == null) {
                resp.setSuccess(false);
                resp.setMessage("服务不存在");
                return resp;
            }
            
            // 检查时间段是否可用（如果指定了技师）
            if (req.getTechnicianId() != null) {
                int count = bookingMapper.checkTimeAvailable(req.getBookingDate(), 
                                                            req.getBookingTime(), 
                                                            req.getTechnicianId());
                if (count > 0) {
                    resp.setSuccess(false);
                    resp.setMessage("该时间段已被预约，请选择其他时间");
                    return resp;
                }
            }
            
            // 创建预约记录
            Booking booking = new Booking();
            booking.setBookingNo(generateBookingNo());
            booking.setUserId(userId);
            booking.setVehicleId(req.getVehicleId());
            booking.setServiceId(req.getServiceId());
            booking.setTechnicianId(req.getTechnicianId());
            booking.setServiceName(service.getName());
            booking.setServicePrice(service.getPrice());
            booking.setContactName(req.getContactName());
            booking.setContactPhone(req.getContactPhone());
            booking.setBookingDate(req.getBookingDate());
            booking.setBookingTime(req.getBookingTime());
            booking.setEstimatedDuration(service.getDuration());
            booking.setProblemDescription(req.getProblemDescription());
            booking.setRemark(req.getRemark());
            booking.setStatus(1); // 待确认
            booking.setCreateTime(LocalDateTime.now());
            booking.setUpdateTime(LocalDateTime.now());
            
            // 保存预约
            int result = bookingMapper.insert(booking);
            if (result > 0) {
                resp.setSuccess(true);
                resp.setMessage("预约成功");
                
                Map<String, Object> data = new HashMap<>();
                data.put("bookingNo", booking.getBookingNo());
                data.put("id", booking.getId());
                resp.setContent(data);
                
                LOG.info("用户{}创建预约成功，预约编号：{}", userId, booking.getBookingNo());
            } else {
                resp.setSuccess(false);
                resp.setMessage("预约失败");
            }
            
        } catch (Exception e) {
            LOG.error("创建预约失败", e);
            resp.setSuccess(false);
            resp.setMessage("预约失败，系统异常");
        }
        
        return resp;
    }
    
    @Override
    public CommonResp<Object> getBookingList(Long userId, Integer page, Integer size) {
        CommonResp<Object> resp = new CommonResp<>();
        
        try {
            // TODO: 实现分页查询
            List<Booking> bookings = bookingMapper.findByUserId(userId);
            
            resp.setSuccess(true);
            resp.setContent(bookings);
            
        } catch (Exception e) {
            LOG.error("获取预约列表失败", e);
            resp.setSuccess(false);
            resp.setMessage("获取预约列表失败");
        }
        
        return resp;
    }
    
    @Override
    public CommonResp<Object> getBookingDetail(Long id, Long userId) {
        CommonResp<Object> resp = new CommonResp<>();
        
        try {
            Booking booking = bookingMapper.selectById(id);
            if (booking == null || !booking.getUserId().equals(userId)) {
                resp.setSuccess(false);
                resp.setMessage("预约不存在");
                return resp;
            }
            
            resp.setSuccess(true);
            resp.setContent(booking);
            
        } catch (Exception e) {
            LOG.error("获取预约详情失败", e);
            resp.setSuccess(false);
            resp.setMessage("获取预约详情失败");
        }
        
        return resp;
    }
    
    @Override
    public CommonResp<Object> cancelBooking(Long id, Long userId) {
        CommonResp<Object> resp = new CommonResp<>();
        
        try {
            Booking booking = bookingMapper.selectById(id);
            if (booking == null || !booking.getUserId().equals(userId)) {
                resp.setSuccess(false);
                resp.setMessage("预约不存在");
                return resp;
            }
            
            // 检查预约状态
            if (booking.getStatus() == 5) {
                resp.setSuccess(false);
                resp.setMessage("预约已取消");
                return resp;
            }
            
            if (booking.getStatus() == 3 || booking.getStatus() == 4) {
                resp.setSuccess(false);
                resp.setMessage("预约已开始或已完成，无法取消");
                return resp;
            }
            
            // 更新状态为已取消
            booking.setStatus(5);
            booking.setUpdateTime(LocalDateTime.now());
            
            int result = bookingMapper.updateById(booking);
            if (result > 0) {
                resp.setSuccess(true);
                resp.setMessage("预约已取消");
                LOG.info("用户{}取消预约：{}", userId, booking.getBookingNo());
            } else {
                resp.setSuccess(false);
                resp.setMessage("取消预约失败");
            }
            
        } catch (Exception e) {
            LOG.error("取消预约失败", e);
            resp.setSuccess(false);
            resp.setMessage("取消预约失败");
        }
        
        return resp;
    }
    
    @Override
    public CommonResp<Object> getAvailableTimes(LocalDate date, Long serviceId) {
        CommonResp<Object> resp = new CommonResp<>();
        
        try {
            // 生成基础时间段（9:00-17:00，每小时一个时间段）
            List<Map<String, Object>> timeSlots = new ArrayList<>();
            for (int hour = 9; hour < 17; hour++) {
                LocalTime time = LocalTime.of(hour, 0);
                Map<String, Object> slot = new HashMap<>();
                slot.put("value", time.format(DateTimeFormatter.ofPattern("HH:mm")));
                slot.put("label", String.format("%02d:00-%02d:00", hour, hour + 1));
                slot.put("available", true);
                timeSlots.add(slot);
            }
            
            // 获取已预约的时间段
            List<LocalTime> bookedTimes = bookingMapper.findBookedTimesByDate(date);
            
            // 标记已预约的时间段为不可用
            for (LocalTime bookedTime : bookedTimes) {
                String timeStr = bookedTime.format(DateTimeFormatter.ofPattern("HH:mm"));
                for (Map<String, Object> slot : timeSlots) {
                    if (timeStr.equals(slot.get("value"))) {
                        slot.put("available", false);
                        break;
                    }
                }
            }
            
            resp.setSuccess(true);
            resp.setContent(timeSlots);
            
        } catch (Exception e) {
            LOG.error("获取可用时间失败", e);
            resp.setSuccess(false);
            resp.setMessage("获取可用时间失败");
        }
        
        return resp;
    }
    
    @Override
    public CommonResp<Object> getTodayBookingsForShop(Long shopId) {
        CommonResp<Object> resp = new CommonResp<>();
        
        try {
            LocalDate today = LocalDate.now();
            List<Booking> bookings = bookingMapper.findTodayBookingsByShopId(shopId, today);
            
            // 转换为前端需要的格式
            List<Map<String, Object>> result = new ArrayList<>();
            for (Booking booking : bookings) {
                Map<String, Object> item = new HashMap<>();
                item.put("id", booking.getId());
                item.put("serviceName", booking.getServiceName());
                item.put("contactName", booking.getContactName());
                item.put("contactPhone", booking.getContactPhone());
                item.put("bookingTime", booking.getBookingTime().toString());
                item.put("status", booking.getStatus());
                item.put("problemDescription", booking.getProblemDescription());
                item.put("servicePrice", booking.getServicePrice());
                result.add(item);
            }
            
            resp.setSuccess(true);
            resp.setContent(result);
            LOG.info("获取维修店{}今日预约列表成功，共{}条", shopId, bookings.size());
            
        } catch (Exception e) {
            LOG.error("获取维修店今日预约列表失败", e);
            resp.setSuccess(false);
            resp.setMessage("获取今日预约失败");
        }
        
        return resp;
    }
    
    @Override
    public CommonResp<Object> updateBookingStatus(Long bookingId, Integer status, Long shopId) {
        CommonResp<Object> resp = new CommonResp<>();
        
        try {
            Booking booking = bookingMapper.selectById(bookingId);
            if (booking == null) {
                resp.setSuccess(false);
                resp.setMessage("预约不存在");
                return resp;
            }
            
            // TODO: 这里应该检查预约的服务是否属于该维修店
            // 暂时跳过权限验证
            
            booking.setStatus(status);
            booking.setUpdateTime(LocalDateTime.now());
            
            int result = bookingMapper.updateById(booking);
            if (result > 0) {
                resp.setSuccess(true);
                resp.setMessage("预约状态更新成功");
                LOG.info("维修店{}更新预约{}状态为{}", shopId, bookingId, status);
            } else {
                resp.setSuccess(false);
                resp.setMessage("更新预约状态失败");
            }
            
        } catch (Exception e) {
            LOG.error("更新预约状态失败", e);
            resp.setSuccess(false);
            resp.setMessage("更新预约状态失败");
        }
        
        return resp;
    }
    
    @Override
    public CommonResp<Object> getBookingStatsForShop(Long shopId) {
        CommonResp<Object> resp = new CommonResp<>();
        
        try {
            Map<String, Object> stats = bookingMapper.getBookingStatsByShopId(shopId);
            if (stats == null) {
                // 如果没有统计数据，返回默认值
                stats = new HashMap<>();
                stats.put("pendingOrders", 0);
                stats.put("processingOrders", 0);
                stats.put("completedOrders", 0);
                stats.put("monthlyRevenue", 0.0);
            }
            
            resp.setSuccess(true);
            resp.setContent(stats);
            LOG.info("获取维修店{}预约统计成功", shopId);
            
        } catch (Exception e) {
            LOG.error("获取维修店预约统计失败", e);
            resp.setSuccess(false);
            resp.setMessage("获取统计数据失败");
        }
        
        return resp;
    }
    
    @Override
    public CommonResp<Object> getBookingsForShop(Long shopId, Integer page, Integer size, Integer status) {
        CommonResp<Object> resp = new CommonResp<>();
        
        try {
            // 计算偏移量
            int offset = (page - 1) * size;
            
            // 查询预约列表
            List<Map<String, Object>> bookingMaps = bookingMapper.findBookingsForShop(shopId, offset, size, status);
            
            // 查询总数
            int total = bookingMapper.countBookingsForShop(shopId, status);
            
            // 转换为前端需要的格式
            List<Map<String, Object>> result = new ArrayList<>();
            for (Map<String, Object> bookingMap : bookingMaps) {
                Map<String, Object> item = new HashMap<>();
                item.put("id", bookingMap.get("id"));
                item.put("orderNumber", bookingMap.get("booking_no"));
                item.put("customerName", bookingMap.get("contact_name"));
                item.put("customerPhone", bookingMap.get("contact_phone"));
                item.put("serviceName", bookingMap.get("service_name"));
                item.put("appointmentTime", bookingMap.get("booking_date") + " " + bookingMap.get("booking_time"));
                item.put("status", bookingMap.get("status"));
                item.put("amount", bookingMap.get("service_price"));
                item.put("requirements", bookingMap.get("problem_description"));
                
                // 组装车辆信息
                String vehicleInfo = buildVehicleInfo(bookingMap);
                item.put("vehicleInfo", vehicleInfo);
                
                result.add(item);
            }
            
            // 构造分页结果
            Map<String, Object> pageResp = new HashMap<>();
            pageResp.put("list", result);
            pageResp.put("total", total);
            
            resp.setSuccess(true);
            resp.setContent(pageResp);
            LOG.info("获取维修店{}预约列表成功，共{}条", shopId, total);
            
        } catch (Exception e) {
            LOG.error("获取维修店预约列表失败", e);
            resp.setSuccess(false);
            resp.setMessage("获取预约列表失败");
        }
        
        return resp;
    }
    
    /**
     * 生成预约编号
     */
    private String generateBookingNo() {
        String date = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        return "B" + date + snowFlake.nextId();
    }
    
    /**
     * 构建车辆信息字符串
     */
    private String buildVehicleInfo(Map<String, Object> bookingMap) {
        String licensePlate = (String) bookingMap.get("license_plate");
        String brand = (String) bookingMap.get("brand");
        String model = (String) bookingMap.get("model");
        String color = (String) bookingMap.get("color");
        Object yearObj = bookingMap.get("year");
        
        StringBuilder vehicleInfo = new StringBuilder();
        
        // 车牌号（必需）
        if (licensePlate != null && !licensePlate.trim().isEmpty()) {
            vehicleInfo.append(licensePlate);
        }
        
        // 品牌和型号
        if (brand != null && !brand.trim().isEmpty()) {
            if (vehicleInfo.length() > 0) {
                vehicleInfo.append(" - ");
            }
            vehicleInfo.append(brand);
            
            if (model != null && !model.trim().isEmpty()) {
                vehicleInfo.append(" ").append(model);
            }
        }
        
        // 颜色和年份
        List<String> extraInfo = new ArrayList<>();
        if (color != null && !color.trim().isEmpty()) {
            extraInfo.add(color);
        }
        if (yearObj != null) {
            extraInfo.add(yearObj.toString() + "年");
        }
        
        if (!extraInfo.isEmpty()) {
            if (vehicleInfo.length() > 0) {
                vehicleInfo.append(" (").append(String.join("，", extraInfo)).append(")");
            } else {
                vehicleInfo.append(String.join("，", extraInfo));
            }
        }
        
        return vehicleInfo.toString().isEmpty() ? "未知车辆" : vehicleInfo.toString();
    }
}
