package com.gec.wiki.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 简单的Token管理器
 * 用于存储和查找token对应的用户信息
 */
@Component
public class TokenManager {
    
    private static final Logger LOG = LoggerFactory.getLogger(TokenManager.class);
    
    // 存储token -> userId的映射
    private final ConcurrentMap<String, Long> tokenUserMap = new ConcurrentHashMap<>();
    
    // 存储userId -> userInfo的映射
    private final ConcurrentMap<Long, UserInfo> userInfoMap = new ConcurrentHashMap<>();
    
    /**
     * 存储token和用户信息
     */
    public void storeToken(String token, Long userId, String username, String realName, Integer userType) {
        if (token != null && userId != null) {
            tokenUserMap.put(token, userId);
            userInfoMap.put(userId, new UserInfo(userId, username, realName, userType));
            LOG.info("存储token成功: userId={}, username={}", userId, username);
        }
    }
    
    /**
     * 根据token获取用户ID
     */
    public Long getUserIdByToken(String token) {
        if (token == null || token.trim().isEmpty()) {
            return null;
        }
        
        // 移除Bearer前缀（如果有）
        if (token.startsWith("Bearer ")) {
            token = token.substring(7);
        }
        
        Long userId = tokenUserMap.get(token);
        LOG.debug("根据token查找用户ID: token={}, userId={}", token, userId);
        return userId;
    }
    
    /**
     * 根据用户ID获取用户信息
     */
    public UserInfo getUserInfo(Long userId) {
        if (userId == null) {
            return null;
        }
        return userInfoMap.get(userId);
    }
    
    /**
     * 根据token获取用户信息
     */
    public UserInfo getUserInfoByToken(String token) {
        Long userId = getUserIdByToken(token);
        if (userId == null) {
            return null;
        }
        return getUserInfo(userId);
    }
    
    /**
     * 移除token（登出时调用）
     */
    public void removeToken(String token) {
        if (token != null) {
            if (token.startsWith("Bearer ")) {
                token = token.substring(7);
            }
            Long userId = tokenUserMap.remove(token);
            if (userId != null) {
                // 检查是否还有其他token指向同一用户，如果没有则移除用户信息
                boolean hasOtherTokens = tokenUserMap.containsValue(userId);
                if (!hasOtherTokens) {
                    userInfoMap.remove(userId);
                }
                LOG.info("移除token成功: userId={}", userId);
            }
        }
    }
    
    /**
     * 清空所有token（系统重启或清理时调用）
     */
    public void clearAllTokens() {
        tokenUserMap.clear();
        userInfoMap.clear();
        LOG.info("清空所有token");
    }
    
    /**
     * 用户信息类
     */
    public static class UserInfo {
        private final Long userId;
        private final String username;
        private final String realName;
        private final Integer userType;
        
        public UserInfo(Long userId, String username, String realName, Integer userType) {
            this.userId = userId;
            this.username = username;
            this.realName = realName;
            this.userType = userType;
        }
        
        public Long getUserId() { return userId; }
        public String getUsername() { return username; }
        public String getRealName() { return realName; }
        public Integer getUserType() { return userType; }
        
        @Override
        public String toString() {
            return "UserInfo{" +
                    "userId=" + userId +
                    ", username='" + username + '\'' +
                    ", realName='" + realName + '\'' +
                    ", userType=" + userType +
                    '}';
        }
    }
}
