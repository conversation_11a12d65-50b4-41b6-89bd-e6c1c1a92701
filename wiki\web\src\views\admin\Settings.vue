<template>
  <div class="admin-settings">
    <div class="page-header">
      <h1>系统设置</h1>
      <p>平台参数和系统配置管理</p>
    </div>

    <!-- 平台配置 -->
    <a-card title="平台配置" style="margin-bottom: 24px;">
      <a-form :model="settings" layout="vertical">
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="平台名称">
              <a-input v-model:value="settings.platformName" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="客服电话">
              <a-input v-model:value="settings.servicePhone" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="平台抽成比例(%)">
              <a-input-number v-model:value="settings.commissionRate" :min="0" :max="100" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="服务半径(公里)">
              <a-input-number v-model:value="settings.serviceRadius" :min="1" :max="100" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item>
          <a-button type="primary" @click="saveSettings">保存平台设置</a-button>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 安全配置 -->
    <a-card title="安全配置">
      <a-form :model="securitySettings" layout="vertical">
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item
              label="登录错误次数限制"
              help="用户连续登录失败达到此次数后将被锁定账号"
            >
              <a-input-number
                v-model:value="securitySettings.maxLoginFailCount"
                :min="3"
                :max="10"
                :step="1"
                style="width: 100%"
              />
              <span style="margin-left: 8px; color: #666;">次</span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="账号锁定时间"
              help="账号被锁定后的解锁等待时间"
            >
              <a-input-number
                v-model:value="securitySettings.lockDurationMinutes"
                :min="5"
                :max="1440"
                :step="5"
                style="width: 100%"
              />
              <span style="margin-left: 8px; color: #666;">分钟</span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="密码最小长度"
              help="用户注册和修改密码时的最小长度要求"
            >
              <a-input-number
                v-model:value="securitySettings.minPasswordLength"
                :min="6"
                :max="20"
                :step="1"
                style="width: 100%"
              />
              <span style="margin-left: 8px; color: #666;">位</span>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item
              label="密码复杂度要求"
              help="是否要求密码包含数字、字母和特殊字符"
            >
              <a-switch
                v-model:checked="securitySettings.requireComplexPassword"
                checked-children="开启"
                un-checked-children="关闭"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-form-item>
          <a-button type="primary" @click="saveSecuritySettings">保存安全设置</a-button>
          <a-button style="margin-left: 8px;" @click="resetSecuritySettings">重置默认</a-button>
        </a-form-item>
      </a-form>
    </a-card>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import axios from 'axios';

export default defineComponent({
  name: 'AdminSettings',
  setup() {
    const settings = ref({
      platformName: '汽车维修服务平台',
      servicePhone: '************',
      commissionRate: 5,
      serviceRadius: 50
    });

    const securitySettings = ref({
      maxLoginFailCount: 5,
      lockDurationMinutes: 30,
      minPasswordLength: 6,
      requireComplexPassword: false
    });

    const loading = ref(false);

    // 加载当前安全设置
    const loadSecuritySettings = async () => {
      try {
        const response = await axios.get('/api/admin/security-settings');
        if (response.data.success) {
          securitySettings.value = { ...securitySettings.value, ...response.data.content };
        }
      } catch (error) {
        console.error('加载安全设置失败:', error);
      }
    };

    const saveSettings = () => {
      // TODO: 实现保存平台设置API
      message.success('平台设置保存成功');
    };

    const saveSecuritySettings = async () => {
      try {
        loading.value = true;
        const response = await axios.post('/api/admin/security-settings', securitySettings.value);

        if (response.data.success) {
          message.success('安全设置保存成功');
        } else {
          message.error(response.data.message || '保存失败');
        }
      } catch (error) {
        console.error('保存安全设置失败:', error);
        message.error('保存安全设置失败');
      } finally {
        loading.value = false;
      }
    };

    const resetSecuritySettings = () => {
      securitySettings.value = {
        maxLoginFailCount: 5,
        lockDurationMinutes: 30,
        minPasswordLength: 6,
        requireComplexPassword: false
      };
      message.info('已重置为默认设置');
    };

    onMounted(() => {
      loadSecuritySettings();
    });

    return {
      settings,
      securitySettings,
      loading,
      saveSettings,
      saveSecuritySettings,
      resetSecuritySettings
    };
  }
});
</script>

<style scoped>
.admin-settings {
  padding: 24px;
  flex: 1;
  background: #f0f2f5;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #333;
}

.page-header p {
  margin: 0;
  color: #666;
}
</style>
