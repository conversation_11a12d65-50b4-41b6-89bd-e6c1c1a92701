package com.gec.wiki.mapper;

import com.gec.wiki.domain.SecuritySettings;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Update;

/**
 * 安全设置数据访问层
 */
@Mapper
public interface SecuritySettingsMapper {
    
    /**
     * 获取安全设置（只有一条记录）
     */
    @Select("SELECT * FROM security_settings LIMIT 1")
    SecuritySettings getSecuritySettings();
    
    /**
     * 插入安全设置
     */
    @Insert("INSERT INTO security_settings (max_login_fail_count, lock_duration_minutes, " +
            "min_password_length, require_complex_password, create_time, update_time) " +
            "VALUES (#{maxLoginFailCount}, #{lockDurationMinutes}, #{minPasswordLength}, " +
            "#{requireComplexPassword}, NOW(), NOW())")
    int insertSecuritySettings(SecuritySettings securitySettings);
    
    /**
     * 更新安全设置
     */
    @Update("UPDATE security_settings SET " +
            "max_login_fail_count = #{maxLoginFailCount}, " +
            "lock_duration_minutes = #{lockDurationMinutes}, " +
            "min_password_length = #{minPasswordLength}, " +
            "require_complex_password = #{requireComplexPassword}, " +
            "update_time = NOW() " +
            "WHERE id = #{id}")
    int updateSecuritySettings(SecuritySettings securitySettings);
    
    /**
     * 检查是否存在设置记录
     */
    @Select("SELECT COUNT(*) FROM security_settings")
    int countSettings();
}
