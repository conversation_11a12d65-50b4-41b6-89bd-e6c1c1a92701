<template>
  <div class="home">
    <!-- 轮播图 -->
    <div class="hero-section">
      <a-carousel autoplay effect="fade" :dots="true" dot-position="bottom" style="height: 700px;">
        <div v-for="(banner, index) in banners" :key="index" class="carousel-slide">
          <div class="carousel-background">
            <div class="bg-overlay"></div>
            <div class="bg-pattern"></div>
          </div>
          <div class="banner-content">
            <div class="banner-text">
              <div class="text-badge">{{ index + 1 }}/{{ banners.length }}</div>
              <h1>{{ banner.title }}</h1>
              <p>{{ banner.subtitle }}</p>
              <div class="banner-actions">
                <a-button type="primary" size="large" @click="scrollToServices" class="primary-btn">
                  <span>立即预约</span>
                  <i class="arrow-icon">→</i>
                </a-button>
                <a-button size="large" ghost class="secondary-btn">
                  了解更多
                </a-button>
              </div>
            </div>
            <div class="banner-image">
              <div class="image-wrapper">
                <img :src="banner.image" :alt="banner.title" />
                <div class="image-glow"></div>
              </div>
            </div>
          </div>
        </div>
      </a-carousel>
      <div class="scroll-indicator">
        <div class="scroll-arrow"></div>
      </div>
    </div>

    <!-- 服务特色 -->
    <div class="features-section">
      <div class="features-bg">
        <div class="floating-shapes">
          <div class="shape shape-1"></div>
          <div class="shape shape-2"></div>
          <div class="shape shape-3"></div>
        </div>
      </div>
      <div class="container">
        <div class="section-header">
          <div class="section-subtitle">WHY CHOOSE US</div>
          <h2>为什么选择我们</h2>
          <p class="section-description">专业、可靠、高效的汽车维修服务，让您的爱车焕然一新</p>
        </div>
        <a-row :gutter="32">
          <a-col :span="8" v-for="(feature, index) in features" :key="feature.id">
            <div class="feature-card" :class="`feature-${index + 1}`">
              <div class="feature-background"></div>
              <div class="feature-number">{{ String(index + 1).padStart(2, '0') }}</div>
              <div class="feature-icon">
                <div class="icon-bg"></div>
                <i class="anticon" :class="feature.icon"></i>
              </div>
              <h3>{{ feature.title }}</h3>
              <p>{{ feature.description }}</p>
              <div class="feature-arrow">→</div>
            </div>
          </a-col>
        </a-row>
      </div>
    </div>

    <!-- 服务项目 -->
    <div class="services-section" id="services">
      <div class="container">
        <div class="section-header">
          <div class="section-subtitle">HOT SERVICES</div>
          <h2>热门服务</h2>
          <p class="section-description">精选高品质汽车维修服务，专业技师为您的爱车保驾护航</p>
        </div>
        <a-row :gutter="24">
          <a-col :span="8" v-for="service in hotServices" :key="service.id">
            <div class="service-card" @click="goToServiceDetail(service.id)">
              <div class="service-image">
                <img :src="service.cover || '/image/default-service.png'" :alt="service.name" />
                <div class="service-overlay">
                  <div class="service-quick-info">
                    <span>{{ service.duration }}分钟</span>
                  </div>
                </div>
                <div class="service-tag" v-if="service.isRecommend">
                  <i class="star-icon">★</i>
                  推荐
                </div>
              </div>
              <div class="service-info">
                <div class="service-header">
                  <h3>{{ service.name }}</h3>
                  <div class="service-rating">
                    <span class="rating-stars">★★★★★</span>
                    <span class="rating-score">{{ Math.round(service.ratingScore * 20) }}%</span>
                  </div>
                </div>
                <p>{{ service.description }}</p>
                <div class="service-meta">
                  <div class="price-section">
                    <span class="price">
                      ¥{{ service.price }}
                    </span>
                    <span class="original-price" v-if="service.originalPrice > service.price">
                      ¥{{ service.originalPrice }}
                    </span>
                  </div>
                  <div class="service-stats">
                    <span class="booking-count">{{ service.bookingCount }}+ 预约</span>
                  </div>
                </div>
              </div>
              <div class="service-actions">
                <a-button type="primary" @click.stop="bookService(service.id)" class="book-btn">
                  立即预约
                  <i class="btn-icon">→</i>
                </a-button>
              </div>
            </div>
          </a-col>
        </a-row>
        <div class="more-services">
          <a-button size="large" @click="goToServices" class="more-btn">
            查看更多服务
            <i class="more-icon">↓</i>
          </a-button>
        </div>
      </div>
    </div>

    <!-- 数据统计 -->
    <div class="stats-section">
      <div class="stats-bg">
        <div class="stats-pattern"></div>
      </div>
      <div class="container">
        <div class="stats-header">
          <h2>数据见证实力</h2>
          <p>专业服务赢得信赖，数字展现我们的专业实力</p>
        </div>
        <a-row :gutter="32">
          <a-col :span="6">
            <div class="stat-card stat-1">
              <div class="stat-icon">🔧</div>
              <a-statistic 
                title="服务项目" 
                :value="stats.serviceCount" 
                suffix="+" 
                :value-style="{ color: '#1890ff', fontSize: '48px', fontWeight: 'bold' }"
              />
              <div class="stat-description">涵盖全方位汽车维修</div>
            </div>
          </a-col>
          <a-col :span="6">
            <div class="stat-card stat-2">
              <div class="stat-icon">📊</div>
              <a-statistic 
                title="服务次数" 
                :value="stats.totalBookings" 
                suffix="+" 
                :value-style="{ color: '#52c41a', fontSize: '48px', fontWeight: 'bold' }"
              />
              <div class="stat-description">累计服务经验丰富</div>
            </div>
          </a-col>
          <a-col :span="6">
            <div class="stat-card stat-3">
              <div class="stat-icon">😊</div>
              <a-statistic 
                title="满意客户" 
                :value="stats.satisfiedCustomers" 
                suffix="+" 
                :value-style="{ color: '#722ed1', fontSize: '48px', fontWeight: 'bold' }"
              />
              <div class="stat-description">客户满意是我们的目标</div>
            </div>
          </a-col>
          <a-col :span="6">
            <div class="stat-card stat-4">
              <div class="stat-icon">👨‍🔧</div>
              <a-statistic 
                title="专业技师" 
                :value="stats.technicianCount" 
                suffix="+" 
                :value-style="{ color: '#fa8c16', fontSize: '48px', fontWeight: 'bold' }"
              />
              <div class="stat-description">经验丰富的技师团队</div>
            </div>
          </a-col>
        </a-row>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import axios from 'axios';
import { message } from 'ant-design-vue';

export default defineComponent({
  name: 'Home',
  setup() {
    const router = useRouter();
    const banners = ref([
      {
        title: '专业汽车维修服务',
        subtitle: '20年行业经验，专业技师团队为您的爱车保驾护航',
        image: '/image/封面.jpg'
      },
      {
        title: '快捷便民服务',
        subtitle: '在线预约，上门服务，让您的汽车维修更加便捷',
        image: '/image/封面1.jpg'
      },
      {
        title: '品质保障',
        subtitle: '使用原厂配件，提供质量保证，让您用车无忧',
        image: '/image/封面3.jpg'
      }
    ]);

    const features = ref([
      {
        id: 1,
        icon: 'anticon-tool',
        title: '专业设备',
        description: '引进先进的汽车检测和维修设备，确保服务质量'
      },
      {
        id: 2,
        icon: 'anticon-team',
        title: '专业团队',
        description: '经验丰富的技师团队，持续技能培训，专业可靠'
      },
      {
        id: 3,
        icon: 'anticon-safety-certificate',
        title: '品质保障',
        description: '使用原厂配件，提供服务保修，让您放心托付'
      }
    ]);

    const hotServices = ref([]);
    const stats = ref({
      serviceCount: 0,
      totalBookings: 0,
      satisfiedCustomers: 0,
      technicianCount: 0
    });

    const scrollToServices = () => {
      // 先尝试平滑滚动到服务区域
      const element = document.getElementById('services');
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    };

    const goToServiceDetail = (serviceId: number) => {
      // TODO: 跳转到服务详情页
      console.log('跳转到服务详情:', serviceId);
    };

    const bookService = (serviceId: number) => {
      router.push({ path: '/booking', query: { serviceId } });
    };

    const goToServices = () => {
      // TODO: 跳转到服务列表页
      console.log('跳转到服务列表页');
    };

    const getHotServices = () => {
      axios.get("/service/getServiceListByPage", {
        params: {
          page: 1,
          size: 6
        }
      }).then((response) => {
        const data = response.data;
        if (data.success) {
          hotServices.value = data.content.list;
        } else {
          message.error(data.message);
        }
      });
    };

    const getStats = () => {
      // 获取服务统计
      axios.get("/service/getServiceListByPage", {
        params: {
          page: 1,
          size: 1000
        }
      }).then((response) => {
        const data = response.data;
        if (data.success) {
          stats.value.serviceCount = data.content.total;
          const services = data.content.list;
          let totalBookings = 0;
          let totalCompleted = 0;
          services.forEach((service: any) => {
            totalBookings += service.bookingCount || 0;
            totalCompleted += service.completeCount || 0;
          });
          stats.value.totalBookings = totalBookings;
          stats.value.satisfiedCustomers = totalCompleted;
        }
      });

      // 技师数量
      stats.value.technicianCount = 15;
    };

    onMounted(() => {
      getHotServices();
      getStats();
    });

    return {
      banners,
      features,
      hotServices,
      stats,
      scrollToServices,
      goToServiceDetail,
      bookService,
      goToServices
    };
  }
});
</script>

<style scoped>
.home {
  background: #f8fafc;
  overflow-x: hidden;
}

/* 轮播图样式 - 全新设计 */
.hero-section {
  position: relative;
  height: 700px;
  overflow: hidden;
}

.carousel-slide {
  height: 700px;
  position: relative;
  overflow: hidden;
}

.carousel-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #3b82f6 100%);
  z-index: 1;
}

.bg-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 70% 80%, rgba(59, 130, 246, 0.3) 0%, transparent 50%);
  z-index: 2;
}

.bg-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.05) 2px, transparent 2px),
    radial-gradient(circle at 80% 50%, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  background-size: 60px 60px, 30px 30px;
  animation: patternMove 20s linear infinite;
  z-index: 2;
}

@keyframes patternMove {
  0% { transform: translateX(0) translateY(0); }
  100% { transform: translateX(60px) translateY(60px); }
}

.banner-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
  position: relative;
  z-index: 3;
}

.banner-text {
  flex: 1;
  color: white;
  max-width: 600px;
}

.text-badge {
  display: inline-block;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 24px;
  animation: fadeInUp 1s ease-out;
}

.banner-text h1 {
  font-size: 64px;
  font-weight: 800;
  margin-bottom: 24px;
  color: white;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  line-height: 1.1;
  background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: slideInLeft 1s ease-out 0.2s both;
}

.banner-text p {
  font-size: 22px;
  margin-bottom: 40px;
  opacity: 0.9;
  line-height: 1.6;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  animation: slideInLeft 1s ease-out 0.4s both;
}

.banner-actions {
  display: flex;
  gap: 16px;
  animation: slideInUp 1s ease-out 0.6s both;
}

.primary-btn {
  height: 56px;
  padding: 0 32px;
  font-size: 16px;
  font-weight: 600;
  border: none;
  border-radius: 28px;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.primary-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(59, 130, 246, 0.5);
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
}

.arrow-icon {
  transition: transform 0.3s ease;
}

.primary-btn:hover .arrow-icon {
  transform: translateX(4px);
}

.secondary-btn {
  height: 56px;
  padding: 0 32px;
  font-size: 16px;
  font-weight: 600;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 28px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  color: white;
  transition: all 0.3s ease;
}

.secondary-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
}

.banner-image {
  flex: 1;
  text-align: center;
  max-width: 600px;
}

.image-wrapper {
  position: relative;
  display: inline-block;
}

.banner-image img {
  max-width: 100%;
  max-height: 500px;
  border-radius: 20px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
  animation: slideInRight 1s ease-out 0.3s both;
  transition: transform 0.5s ease;
  position: relative;
  z-index: 2;
}

.image-glow {
  position: absolute;
  top: 20px;
  left: 20px;
  right: -20px;
  bottom: -20px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.3), rgba(147, 51, 234, 0.3));
  border-radius: 20px;
  filter: blur(20px);
  z-index: 1;
  animation: glow 3s ease-in-out infinite alternate;
}

@keyframes glow {
  0% { opacity: 0.5; transform: scale(1); }
  100% { opacity: 0.8; transform: scale(1.05); }
}

.banner-image:hover img {
  transform: scale(1.05) rotateY(5deg);
}

.scroll-indicator {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 3;
}

.scroll-arrow {
  width: 30px;
  height: 30px;
  border: 2px solid rgba(255, 255, 255, 0.6);
  border-top: none;
  border-left: none;
  transform: rotate(45deg);
  animation: scrollBounce 2s infinite;
}

@keyframes scrollBounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0) rotate(45deg); }
  40% { transform: translateY(-10px) rotate(45deg); }
  60% { transform: translateY(-5px) rotate(45deg); }
}

/* 动画定义 */
@keyframes slideInLeft {
  0% {
    opacity: 0;
    transform: translateX(-60px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  0% {
    opacity: 0;
    transform: translateX(60px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInUp {
  0% {
    opacity: 0;
    transform: translateY(40px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* 通用容器 */
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 40px;
}

/* 通用章节头部 */
.section-header {
  text-align: center;
  margin-bottom: 80px;
}

.section-subtitle {
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 2px;
  color: #3b82f6;
  margin-bottom: 16px;
  text-transform: uppercase;
}

.section-header h2 {
  font-size: 48px;
  font-weight: 800;
  color: #1e293b;
  margin-bottom: 20px;
  position: relative;
  background: linear-gradient(135deg, #1e293b 0%, #3b82f6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-description {
  font-size: 18px;
  color: #64748b;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

/* 特色服务区域 */
.features-section {
  padding: 120px 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
  overflow: hidden;
}

.features-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 30%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 80% 70%, rgba(147, 51, 234, 0.1) 0%, transparent 50%);
}

.floating-shapes {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.shape {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(147, 51, 234, 0.2));
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 100px;
  height: 100px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 150px;
  height: 150px;
  top: 60%;
  right: 10%;
  animation-delay: 2s;
}

.shape-3 {
  width: 80px;
  height: 80px;
  bottom: 20%;
  left: 70%;
  animation-delay: 4s;
}

.feature-card {
  text-align: center;
  padding: 60px 40px;
  border-radius: 24px;
  transition: all 0.5s ease;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  height: 100%;
}

.feature-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  transition: opacity 0.5s ease;
}

.feature-1 .feature-background {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05), rgba(37, 99, 235, 0.05));
}

.feature-2 .feature-background {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.05), rgba(5, 150, 105, 0.05));
}

.feature-3 .feature-background {
  background: linear-gradient(135deg, rgba(147, 51, 234, 0.05), rgba(126, 34, 206, 0.05));
}

.feature-card:hover .feature-background {
  opacity: 1;
}

.feature-number {
  position: absolute;
  top: 24px;
  right: 24px;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  font-weight: 700;
  color: white;
}

.feature-card:hover {
  transform: translateY(-20px) scale(1.02);
  box-shadow: 0 25px 60px rgba(0, 0, 0, 0.15);
}

.feature-icon {
  position: relative;
  margin-bottom: 32px;
  display: inline-block;
}

.icon-bg {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120px;
  height: 120px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
  border-radius: 50%;
  z-index: 1;
}

.feature-icon i {
  font-size: 48px;
  color: #3b82f6;
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
}

.feature-card:hover .feature-icon i {
  color: #1d4ed8;
  transform: scale(1.1);
}

.feature-card h3 {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 16px;
  color: #1e293b;
}

.feature-card p {
  color: #64748b;
  line-height: 1.7;
  font-size: 16px;
  margin-bottom: 20px;
}

.feature-arrow {
  opacity: 0;
  transform: translateX(-10px);
  transition: all 0.3s ease;
  color: #3b82f6;
  font-size: 20px;
  font-weight: bold;
}

.feature-card:hover .feature-arrow {
  opacity: 1;
  transform: translateX(0);
}

/* 服务项目区域 */
.services-section {
  padding: 120px 0;
  background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
  position: relative;
}

.service-card {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
  transition: all 0.4s ease;
  cursor: pointer;
  margin-bottom: 32px;
  border: 1px solid rgba(226, 232, 240, 0.8);
  position: relative;
}

.service-card:hover {
  transform: translateY(-12px) scale(1.02);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  border-color: rgba(59, 130, 246, 0.2);
}

.service-image {
  position: relative;
  height: 240px;
  overflow: hidden;
}

.service-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.service-card:hover .service-image img {
  transform: scale(1.1);
}

.service-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.7) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  display: flex;
  align-items: flex-end;
  padding: 20px;
}

.service-card:hover .service-overlay {
  opacity: 1;
}

.service-quick-info {
  color: white;
  font-size: 14px;
  font-weight: 500;
}

.service-tag {
  position: absolute;
  top: 16px;
  right: 16px;
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.4);
}

.star-icon {
  color: #fbbf24;
}

.service-info {
  padding: 24px;
}

.service-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.service-info h3 {
  font-size: 22px;
  font-weight: 700;
  color: #1e293b;
  margin: 0;
}

.service-rating {
  text-align: right;
}

.rating-stars {
  color: #fbbf24;
  font-size: 14px;
  display: block;
  margin-bottom: 4px;
}

.rating-score {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
}

.service-info p {
  color: #64748b;
  margin-bottom: 20px;
  line-height: 1.6;
  font-size: 15px;
}

.service-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;
}

.price-section {
  display: flex;
  align-items: baseline;
  gap: 8px;
}

.price {
  font-size: 28px;
  font-weight: 800;
  color: #ef4444;
  line-height: 1;
}

.original-price {
  font-size: 16px;
  color: #94a3b8;
  text-decoration: line-through;
  font-weight: 500;
}

.service-stats {
  text-align: right;
}

.booking-count {
  font-size: 13px;
  color: #10b981;
  font-weight: 600;
  background: rgba(16, 185, 129, 0.1);
  padding: 4px 12px;
  border-radius: 12px;
}

.service-actions {
  padding: 0 24px 24px;
}

.book-btn {
  width: 100%;
  height: 48px;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.book-btn:hover {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

.btn-icon {
  transition: transform 0.3s ease;
}

.book-btn:hover .btn-icon {
  transform: translateX(4px);
}

.more-services {
  text-align: center;
  margin-top: 60px;
}

.more-btn {
  height: 56px;
  padding: 0 40px;
  border-radius: 28px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
  border: 2px solid #3b82f6;
  color: #3b82f6;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
}

.more-btn:hover {
  background: #3b82f6;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.more-icon {
  transition: transform 0.3s ease;
}

.more-btn:hover .more-icon {
  transform: translateY(4px);
}

/* 统计数据区域 */
.stats-section {
  padding: 120px 0;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  position: relative;
  overflow: hidden;
}

.stats-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.stats-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 2px, transparent 2px),
    radial-gradient(circle at 75% 75%, rgba(147, 51, 234, 0.1) 1px, transparent 1px);
  background-size: 50px 50px, 25px 25px;
  animation: patternMove 30s linear infinite;
}

.stats-header {
  text-align: center;
  margin-bottom: 80px;
}

.stats-header h2 {
  font-size: 48px;
  font-weight: 800;
  color: white;
  margin-bottom: 20px;
  background: linear-gradient(135deg, #ffffff 0%, #94a3b8 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stats-header p {
  font-size: 18px;
  color: #94a3b8;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

.stat-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  padding: 40px;
  text-align: center;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
  height: 280px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stat-card:hover::before {
  opacity: 1;
}

.stat-card:hover {
  transform: translateY(-10px) scale(1.02);
  border-color: rgba(255, 255, 255, 0.2);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.stat-icon {
  font-size: 48px;
  margin-bottom: 20px;
  animation: pulse 2s infinite;
  position: relative;
  z-index: 2;
}

.stat-1 .stat-icon { animation-delay: 0s; }
.stat-2 .stat-icon { animation-delay: 0.5s; }
.stat-3 .stat-icon { animation-delay: 1s; }
.stat-4 .stat-icon { animation-delay: 1.5s; }

.stat-card .ant-statistic {
  position: relative;
  z-index: 2;
}

.stat-card .ant-statistic-title {
  font-size: 18px;
  color: #cbd5e1;
  font-weight: 600;
  margin-bottom: 16px;
}

.stat-card .ant-statistic-content {
  margin-bottom: 16px;
}

.stat-description {
  font-size: 14px;
  color: #94a3b8;
  line-height: 1.5;
  position: relative;
  z-index: 2;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .container {
    max-width: 1200px;
    padding: 0 40px;
  }
}

@media (max-width: 1200px) {
  .banner-content {
    max-width: 100%;
    padding: 0 40px;
  }
  
  .container {
    padding: 0 40px;
  }
  
  .banner-text h1 {
    font-size: 52px;
  }
  
  .section-header h2 {
    font-size: 42px;
  }
}

@media (max-width: 1024px) {
  .hero-section {
    height: 600px;
  }
  
  .carousel-slide {
    height: 600px;
  }
  
  .banner-text h1 {
    font-size: 48px;
  }
  
  .primary-btn, .secondary-btn {
    height: 50px;
    font-size: 15px;
  }
}

@media (max-width: 768px) {
  .hero-section {
    height: 550px;
  }
  
  .carousel-slide {
    height: 550px;
  }
  
  .banner-content {
    flex-direction: column;
    text-align: center;
    padding: 40px 20px;
    gap: 40px;
  }
  
  .banner-text {
    max-width: 100%;
  }
  
  .banner-text h1 {
    font-size: 36px;
    margin-bottom: 20px;
  }
  
  .banner-text p {
    font-size: 18px;
    margin-bottom: 32px;
  }
  
  .banner-actions {
    flex-direction: column;
    gap: 12px;
  }
  
  .primary-btn, .secondary-btn {
    height: 48px;
    font-size: 14px;
    width: 100%;
    max-width: 300px;
  }
  
  .banner-image img {
    max-height: 300px;
  }
  
  .features-section, .services-section {
    padding: 80px 0;
  }
  
  .stats-section {
    padding: 80px 0;
  }
  
  .section-header h2 {
    font-size: 36px;
  }
  
  .feature-card {
    padding: 40px 24px;
    margin-bottom: 24px;
  }
  
  .feature-icon i {
    font-size: 40px;
  }
  
  .feature-card h3 {
    font-size: 20px;
  }
  
  .service-card {
    margin-bottom: 24px;
  }
  
  .service-info {
    padding: 20px;
  }
  
  .service-info h3 {
    font-size: 20px;
  }
  
  .stat-card {
    height: 240px;
    padding: 32px 24px;
  }
  
  .stat-icon {
    font-size: 40px;
    margin-bottom: 16px;
  }
  
  .container {
    padding: 0 20px;
  }
}

@media (max-width: 576px) {
  .hero-section {
    height: 500px;
  }
  
  .carousel-slide {
    height: 500px;
  }
  
  .banner-text h1 {
    font-size: 28px;
  }
  
  .banner-text p {
    font-size: 16px;
  }
  
  .section-header h2 {
    font-size: 28px;
  }
  
  .section-description {
    font-size: 16px;
  }
  
  .features-section, .services-section, .stats-section {
    padding: 60px 0;
  }
  
  .feature-card {
    padding: 32px 20px;
  }
  
  .feature-card h3 {
    font-size: 18px;
  }
  
  .stat-card {
    height: 200px;
    padding: 24px 16px;
  }
  
  .stat-icon {
    font-size: 32px;
    margin-bottom: 12px;
  }
  
  .stats-header h2 {
    font-size: 32px;
  }
  
  .service-info h3 {
    font-size: 18px;
  }
  
  .price {
    font-size: 24px;
  }
  
  .container {
    padding: 0 16px;
  }
}
</style>