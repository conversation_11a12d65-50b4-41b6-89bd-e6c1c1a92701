<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.gec.wiki.mapper.UserMapper">

    <resultMap id="BaseResultMap" type="com.gec.wiki.pojo.User">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="username" jdbcType="VARCHAR" property="username" />
        <result column="password" jdbcType="VARCHAR" property="password" />
        <result column="real_name" jdbcType="VARCHAR" property="realName" />
        <result column="phone" jdbcType="VARCHAR" property="phone" />
        <result column="email" jdbcType="VARCHAR" property="email" />
        <result column="avatar" jdbcType="VARCHAR" property="avatar" />
        <result column="gender" jdbcType="INTEGER" property="gender" />
        <result column="birthday" jdbcType="DATE" property="birthday" />
        <result column="address" jdbcType="VARCHAR" property="address" />
        <result column="status" jdbcType="INTEGER" property="status" />
        <result column="user_type" jdbcType="INTEGER" property="userType" />
        <result column="register_time" jdbcType="TIMESTAMP" property="registerTime" />
        <result column="last_login_time" jdbcType="TIMESTAMP" property="lastLoginTime" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        id, username, password, real_name, phone, email, avatar, gender, birthday, 
        address, status, user_type, register_time, last_login_time, create_time, update_time
    </sql>

    <select id="findByUsername" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM users
        WHERE username = #{username} AND status = 1
    </select>

    <select id="findByPhone" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM users
        WHERE phone = #{phone} AND status = 1
    </select>

    <select id="findByEmail" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM users
        WHERE email = #{email} AND status = 1
    </select>

    <update id="updateLastLoginTime">
        UPDATE users
        SET last_login_time = NOW(), update_time = NOW()
        WHERE id = #{id}
    </update>

    <select id="findByRealNameAndPhoneAndEmail" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM users
        WHERE real_name = #{realName} 
        AND phone = #{phone} 
        AND email = #{email} 
        AND status = 1
    </select>

    <update id="updatePassword">
        UPDATE users
        SET password = #{password}, update_time = NOW()
        WHERE id = #{id}
    </update>

</mapper>
