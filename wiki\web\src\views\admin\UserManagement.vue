<template>
  <div class="user-management">
    <div class="page-header">
      <h2>用户管理</h2>
      <div class="header-actions">
        <a-button @click="exportUsers">
          <DownloadOutlined />
          导出用户
        </a-button>
        <a-button type="primary" @click="refreshUsers">
          <ReloadOutlined />
          刷新
        </a-button>
      </div>
    </div>

    <!-- 用户统计 -->
    <a-row :gutter="16" class="user-stats">
      <a-col :span="6">
        <a-card class="stat-card">
          <a-statistic 
            title="总用户数" 
            :value="userStats.total" 
            prefix="👥"
            :value-style="{ color: '#1890ff' }"
          />
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card class="stat-card">
          <a-statistic 
            title="车主用户" 
            :value="userStats.owners" 
            prefix="🚗"
            :value-style="{ color: '#52c41a' }"
          />
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card class="stat-card">
          <a-statistic 
            title="维修店" 
            :value="userStats.shops" 
            prefix="🔧"
            :value-style="{ color: '#fa8c16' }"
          />
        </a-card>
      </a-col>
      <a-col :span="6">
        <a-card class="stat-card">
          <a-statistic 
            title="今日新增" 
            :value="userStats.todayNew" 
            prefix="⭐"
            :value-style="{ color: '#722ed1' }"
          />
        </a-card>
      </a-col>
    </a-row>

    <!-- 用户列表 -->
    <a-card class="user-table-card">
      <template #title>
        <div class="table-header">
          <span>用户列表</span>
          <div class="table-actions">
            <a-select 
              v-model:value="userTypeFilter" 
              style="width: 120px; margin-right: 16px;"
              @change="handleFilterChange"
            >
              <a-select-option value="">全部类型</a-select-option>
              <a-select-option value="1">车主</a-select-option>
              <a-select-option value="2">维修店</a-select-option>
              <a-select-option value="3">管理员</a-select-option>
            </a-select>
            <a-input-search
              v-model:value="searchKeyword"
              placeholder="搜索用户名或手机号"
              style="width: 250px;"
              @search="handleSearch"
            />
          </div>
        </div>
      </template>

      <a-table 
        :dataSource="users" 
        :columns="columns" 
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        rowKey="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'userType'">
            <a-tag :color="getUserTypeColor(record.userType)">
              {{ getUserTypeText(record.userType) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'status'">
            <a-tag :color="record.status === 1 ? 'green' : 'red'">
              {{ record.status === 1 ? '正常' : '禁用' }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button size="small" @click="viewUserDetail(record)">
                查看
              </a-button>
              <a-button 
                size="small" 
                :type="record.status === 1 ? 'default' : 'primary'"
                @click="toggleUserStatus(record)"
              >
                {{ record.status === 1 ? '禁用' : '启用' }}
              </a-button>
              <a-button size="small" @click="editUser(record)">
                编辑
              </a-button>
              <a-popconfirm
                title="确定删除这个用户吗？"
                @confirm="deleteUser(record.id)"
              >
                <a-button size="small" danger>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 用户详情模态框 -->
    <a-modal
      v-model:visible="detailVisible"
      title="用户详情"
      width="600px"
      :footer="null"
    >
      <div v-if="selectedUser" class="user-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="用户ID">{{ selectedUser.id }}</a-descriptions-item>
          <a-descriptions-item label="用户名">{{ selectedUser.username }}</a-descriptions-item>
          <a-descriptions-item label="用户类型">
            <a-tag :color="getUserTypeColor(selectedUser.userType)">
              {{ getUserTypeText(selectedUser.userType) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="手机号">{{ selectedUser.phone || '未设置' }}</a-descriptions-item>
          <a-descriptions-item label="邮箱">{{ selectedUser.email || '未设置' }}</a-descriptions-item>
          <a-descriptions-item label="注册时间">{{ formatDate(selectedUser.createTime) }}</a-descriptions-item>
          <a-descriptions-item label="最后登录">{{ formatDate(selectedUser.lastLoginTime) }}</a-descriptions-item>
          <a-descriptions-item label="登录次数">{{ selectedUser.loginCount || 0 }}</a-descriptions-item>
          <a-descriptions-item label="账户状态">
            <a-tag :color="selectedUser.status === 1 ? 'green' : 'red'">
              {{ selectedUser.status === 1 ? '正常' : '禁用' }}
            </a-tag>
          </a-descriptions-item>
        </a-descriptions>

        <!-- 如果是维修店用户，显示额外信息 -->
        <div v-if="selectedUser.userType === 2" class="shop-info">
          <a-divider>维修店信息</a-divider>
          <a-descriptions :column="2" bordered>
            <a-descriptions-item label="店铺名称">{{ selectedUser.shopName || '未设置' }}</a-descriptions-item>
            <a-descriptions-item label="营业执照">{{ selectedUser.businessLicense || '未上传' }}</a-descriptions-item>
            <a-descriptions-item label="联系地址">{{ selectedUser.address || '未设置' }}</a-descriptions-item>
            <a-descriptions-item label="审核状态">
              <a-tag :color="getShopStatusColor(selectedUser.shopStatus)">
                {{ getShopStatusText(selectedUser.shopStatus) }}
              </a-tag>
            </a-descriptions-item>
          </a-descriptions>
        </div>
      </div>
    </a-modal>

    <!-- 编辑用户模态框 -->
    <a-modal
      v-model:visible="editVisible"
      title="编辑用户"
      @ok="saveUser"
      @cancel="cancelEdit"
    >
      <a-form
        ref="editFormRef"
        :model="editForm"
        :rules="editRules"
        layout="vertical"
      >
        <a-form-item label="用户名" name="username">
          <a-input v-model:value="editForm.username" placeholder="请输入用户名" />
        </a-form-item>
        <a-form-item label="手机号" name="phone">
          <a-input v-model:value="editForm.phone" placeholder="请输入手机号" />
        </a-form-item>
        <a-form-item label="邮箱" name="email">
          <a-input v-model:value="editForm.email" placeholder="请输入邮箱" />
        </a-form-item>
        <a-form-item label="用户状态" name="status">
          <a-radio-group v-model:value="editForm.status">
            <a-radio :value="1">正常</a-radio>
            <a-radio :value="0">禁用</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { DownloadOutlined, ReloadOutlined } from '@ant-design/icons-vue';
import axios from 'axios';

// 响应式数据
const loading = ref(false);
const users = ref([]);
const userTypeFilter = ref('');
const searchKeyword = ref('');
const detailVisible = ref(false);
const editVisible = ref(false);
const selectedUser = ref(null);

// 用户统计数据
const userStats = ref({
  total: 156,
  owners: 89,
  shops: 45,
  todayNew: 8
});

// 编辑表单
const editForm = reactive({
  id: null,
  username: '',
  phone: '',
  email: '',
  status: 1
});

const editRules = {
  username: [{ required: true, message: '请输入用户名' }],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址' }
  ]
};

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条数据`
});

// 表格列定义
const columns = [
  {
    title: '用户ID',
    dataIndex: 'id',
    key: 'id',
    width: 80
  },
  {
    title: '用户名',
    dataIndex: 'username',
    key: 'username',
    width: 120
  },
  {
    title: '用户类型',
    key: 'userType',
    width: 100
  },
  {
    title: '手机号',
    dataIndex: 'phone',
    key: 'phone',
    width: 130
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    key: 'email',
    width: 180
  },
  {
    title: '注册时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 160,
    customRender: ({ text }: { text: string }) => formatDate(text)
  },
  {
    title: '状态',
    key: 'status',
    width: 80
  },
  {
    title: '操作',
    key: 'action',
    width: 200
  }
];

// 加载用户数据
const loadUsers = async () => {
  try {
    loading.value = true;
    // 模拟数据
    users.value = [
      {
        id: 1,
        username: 'admin',
        userType: 3,
        phone: '13800138000',
        email: '<EMAIL>',
        createTime: '2024-01-01T00:00:00',
        lastLoginTime: '2025-01-04T10:30:00',
        loginCount: 156,
        status: 1
      },
      {
        id: 2,
        username: 'zhangsan',
        userType: 1,
        phone: '***********',
        email: '<EMAIL>',
        createTime: '2024-06-15T14:22:00',
        lastLoginTime: '2025-01-04T09:15:00',
        loginCount: 23,
        status: 1
      },
      {
        id: 3,
        username: 'lisi_shop',
        userType: 2,
        phone: '***********',
        email: '<EMAIL>',
        createTime: '2024-08-20T16:45:00',
        lastLoginTime: '2025-01-03T18:20:00',
        loginCount: 45,
        status: 1,
        shopName: '李师傅汽修店',
        businessLicense: 'BL123456789',
        address: '北京市朝阳区建国路88号',
        shopStatus: 1
      },
      {
        id: 4,
        username: 'wangwu',
        userType: 1,
        phone: '***********',
        email: '<EMAIL>',
        createTime: '2024-12-01T10:00:00',
        lastLoginTime: '2025-01-02T20:30:00',
        loginCount: 8,
        status: 0
      }
    ];
    pagination.total = users.value.length;
  } catch (error) {
    message.error('加载用户数据失败');
  } finally {
    loading.value = false;
  }
};

// 刷新用户数据
const refreshUsers = () => {
  loadUsers();
};

// 筛选变化处理
const handleFilterChange = () => {
  pagination.current = 1;
  loadUsers();
};

// 搜索处理
const handleSearch = () => {
  pagination.current = 1;
  loadUsers();
};

// 表格变化处理
const handleTableChange = (pag: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  loadUsers();
};

// 查看用户详情
const viewUserDetail = (user: any) => {
  selectedUser.value = user;
  detailVisible.value = true;
};

// 编辑用户
const editUser = (user: any) => {
  Object.assign(editForm, {
    id: user.id,
    username: user.username,
    phone: user.phone,
    email: user.email,
    status: user.status
  });
  editVisible.value = true;
};

// 保存用户编辑
const saveUser = async () => {
  try {
    // 这里应该调用后端API
    message.success('用户信息更新成功');
    editVisible.value = false;
    loadUsers();
  } catch (error) {
    message.error('更新失败');
  }
};

// 取消编辑
const cancelEdit = () => {
  editVisible.value = false;
};

// 切换用户状态
const toggleUserStatus = async (user: any) => {
  try {
    // 这里应该调用后端API
    const newStatus = user.status === 1 ? 0 : 1;
    message.success(`用户已${newStatus === 1 ? '启用' : '禁用'}`);
    loadUsers();
  } catch (error) {
    message.error('操作失败');
  }
};

// 删除用户
const deleteUser = async (userId: number) => {
  try {
    // 这里应该调用后端API
    message.success('用户删除成功');
    loadUsers();
  } catch (error) {
    message.error('删除失败');
  }
};

// 导出用户数据
const exportUsers = () => {
  message.info('导出功能开发中...');
};

// 获取用户类型颜色
const getUserTypeColor = (userType: number): string => {
  const colors: Record<number, string> = {
    1: 'blue',      // 车主
    2: 'orange',    // 维修店
    3: 'red'        // 管理员
  };
  return colors[userType] || 'default';
};

// 获取用户类型文本
const getUserTypeText = (userType: number): string => {
  const texts: Record<number, string> = {
    1: '车主',
    2: '维修店',
    3: '管理员'
  };
  return texts[userType] || '未知';
};

// 获取店铺状态颜色
const getShopStatusColor = (status: number): string => {
  const colors: Record<number, string> = {
    0: 'orange',    // 待审核
    1: 'green',     // 已通过
    2: 'red'        // 已拒绝
  };
  return colors[status] || 'default';
};

// 获取店铺状态文本
const getShopStatusText = (status: number): string => {
  const texts: Record<number, string> = {
    0: '待审核',
    1: '已通过',
    2: '已拒绝'
  };
  return texts[status] || '未知';
};

// 格式化日期
const formatDate = (dateStr: string): string => {
  if (!dateStr) return '--';
  return new Date(dateStr).toLocaleString();
};

// 组件挂载时加载数据
onMounted(() => {
  loadUsers();
});
</script>

<style scoped>
.user-management {
  padding: 24px;
  background: #f0f2f5;
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0;
  color: #1890ff;
  font-size: 24px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.user-stats {
  margin-bottom: 24px;
}

.stat-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.stat-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  transform: translateY(-2px);
}

.user-table-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-header span {
  font-weight: 600;
  color: #1890ff;
}

.table-actions {
  display: flex;
  align-items: center;
}

.user-detail {
  margin-top: 16px;
}

.shop-info {
  margin-top: 24px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .user-stats :deep(.ant-col) {
    margin-bottom: 16px;
  }
}

@media (max-width: 768px) {
  .user-management {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .header-actions {
    justify-content: stretch;
  }
  
  .table-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .table-actions {
    flex-direction: column;
    gap: 12px;
  }
}
</style>
