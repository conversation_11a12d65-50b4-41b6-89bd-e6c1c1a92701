package com.gec.wiki.controller;

import com.gec.wiki.pojo.resp.CommonResp;
import com.gec.wiki.service.BookingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 维修店预约管理控制器
 */
@RestController
@RequestMapping("/shop/booking")
public class ShopBookingController {

    private static final Logger LOG = LoggerFactory.getLogger(ShopBookingController.class);

    @Resource
    private BookingService bookingService;

    /**
     * 获取维修店今日预约列表
     */
    @GetMapping("/today")
    public CommonResp<Object> getTodayBookings() {
        CommonResp<Object> resp = new CommonResp<>();
        try {
            LOG.info("🏪 获取维修店今日预约列表");
            
            // 获取当前维修店ID - 这里使用固定值，实际项目应从认证信息获取
            Long shopId = getCurrentShopId();
            if (shopId == null) {
                resp.setSuccess(false);
                resp.setMessage("用户未登录或非维修店用户");
                return resp;
            }
            
            return bookingService.getTodayBookingsForShop(shopId);
            
        } catch (Exception e) {
            LOG.error("❌ 获取维修店今日预约列表失败", e);
            resp.setSuccess(false);
            resp.setMessage("获取今日预约失败：" + e.getMessage());
        }
        return resp;
    }

    /**
     * 更新预约状态
     */
    @PutMapping("/{id}/status")
    public CommonResp<Object> updateBookingStatus(@PathVariable Long id, @RequestParam Integer status) {
        CommonResp<Object> resp = new CommonResp<>();
        try {
            LOG.info("🔄 更新预约状态，预约ID：{}，状态：{}", id, status);
            
            // 获取当前维修店ID - 这里使用固定值，实际项目应从认证信息获取
            Long shopId = getCurrentShopId();
            if (shopId == null) {
                resp.setSuccess(false);
                resp.setMessage("用户未登录或非维修店用户");
                return resp;
            }
            
            // 验证状态值
            if (status < 1 || status > 5) {
                resp.setSuccess(false);
                resp.setMessage("无效的状态值");
                return resp;
            }
            
            return bookingService.updateBookingStatus(id, status, shopId);
            
        } catch (Exception e) {
            LOG.error("❌ 更新预约状态失败", e);
            resp.setSuccess(false);
            resp.setMessage("更新预约状态失败：" + e.getMessage());
        }
        return resp;
    }

    /**
     * 获取维修店预约统计
     */
    @GetMapping("/stats")
    public CommonResp<Object> getBookingStats() {
        CommonResp<Object> resp = new CommonResp<>();
        try {
            LOG.info("📊 获取维修店预约统计");
            
            // 获取当前维修店ID - 这里使用固定值，实际项目应从认证信息获取
            Long shopId = getCurrentShopId();
            if (shopId == null) {
                resp.setSuccess(false);
                resp.setMessage("用户未登录或非维修店用户");
                return resp;
            }
            
            return bookingService.getBookingStatsForShop(shopId);
            
        } catch (Exception e) {
            LOG.error("❌ 获取维修店预约统计失败", e);
            resp.setSuccess(false);
            resp.setMessage("获取统计数据失败：" + e.getMessage());
        }
        return resp;
    }

    /**
     * 确认预约
     */
    @PutMapping("/{id}/confirm")
    public CommonResp<Object> confirmBooking(@PathVariable Long id) {
        return updateBookingStatus(id, 2); // 状态2：已确认
    }

    /**
     * 开始服务
     */
    @PutMapping("/{id}/start")
    public CommonResp<Object> startService(@PathVariable Long id) {
        return updateBookingStatus(id, 3); // 状态3：服务中
    }

    /**
     * 完成服务
     */
    @PutMapping("/{id}/complete")
    public CommonResp<Object> completeService(@PathVariable Long id) {
        return updateBookingStatus(id, 4); // 状态4：已完成
    }

    /**
     * 婉拒预约
     */
    @PutMapping("/{id}/reject")
    public CommonResp<Object> rejectBooking(@PathVariable Long id, @RequestParam(required = false) String reason) {
        CommonResp<Object> resp = new CommonResp<>();
        try {
            LOG.info("🚫 婉拒预约，预约ID：{}，原因：{}", id, reason);
            
            // 获取当前维修店ID
            Long shopId = getCurrentShopId();
            if (shopId == null) {
                resp.setSuccess(false);
                resp.setMessage("用户未登录或非维修店用户");
                return resp;
            }
            
            // 更新预约状态为已取消，并记录婉拒原因
            CommonResp<Object> updateResult = bookingService.updateBookingStatus(id, 5, shopId);
            
            if (updateResult.isSuccess()) {
                // TODO: 这里可以发送短信或邮件通知客户
                LOG.info("✅ 预约{}婉拒成功，原因：{}", id, reason);
                resp.setSuccess(true);
                resp.setMessage("预约已婉拒" + (reason != null ? "，原因：" + reason : ""));
            } else {
                resp.setSuccess(false);
                resp.setMessage("婉拒失败：" + updateResult.getMessage());
            }
            
        } catch (Exception e) {
            LOG.error("❌ 婉拒预约失败", e);
            resp.setSuccess(false);
            resp.setMessage("婉拒失败：" + e.getMessage());
        }
        return resp;
    }

    /**
     * 获取当前登录的维修店ID
     */
    private Long getCurrentShopId() {
        try {
            // TODO: 集成用户认证系统，从session或token中获取当前用户信息
            return 1L; // 临时返回1，实际应该从RequestContext或Security Context获取
        } catch (Exception e) {
            LOG.error("获取当前维修店ID失败", e);
            return null;
        }
    }
}
