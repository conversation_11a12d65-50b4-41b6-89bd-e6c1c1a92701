<template>
  <div class="admin-order-management">
    <div class="page-header">
      <h1>订单管理</h1>
      <p>管理平台所有维修订单</p>
    </div>

    <a-card>
      <div class="search-section">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-input
              v-model:value="searchForm.keyword"
              placeholder="搜索订单号、客户姓名"
              @press-enter="handleSearch"
            >
              <template #prefix><SearchOutlined /></template>
            </a-input>
          </a-col>
          <a-col :span="4">
            <a-select
              v-model:value="searchForm.status"
              placeholder="订单状态"
              allowClear
            >
              <a-select-option :value="1">待确认</a-select-option>
              <a-select-option :value="2">已确认</a-select-option>
              <a-select-option :value="3">服务中</a-select-option>
              <a-select-option :value="4">已完成</a-select-option>
              <a-select-option :value="5">已取消</a-select-option>
            </a-select>
          </a-col>
          <a-col :span="4">
            <a-button type="primary" @click="handleSearch">
              <SearchOutlined />
              搜索
            </a-button>
          </a-col>
        </a-row>
      </div>

      <a-table
        :columns="columns"
        :data-source="orders"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          <template v-if="column.key === 'action'">
            <a-space>
              <a-button size="small" @click="viewOrder(record.id)">查看</a-button>
              <a-button size="small" type="primary" v-if="record.status === 1" 
                @click="confirmOrder(record.id)">确认</a-button>
              <a-button size="small" danger v-if="[1,2].includes(record.status)" 
                @click="cancelOrder(record.id)">取消</a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue';
import { SearchOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';

export default defineComponent({
  name: 'AdminOrderManagement',
  components: {
    SearchOutlined
  },
  setup() {
    const loading = ref(false);
    const orders = ref<any[]>([]);
    const searchForm = ref({
      keyword: '',
      status: undefined
    });

    const pagination = ref({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total: number) => `共 ${total} 条记录`
    });

    const columns = [
      { title: '订单号', dataIndex: 'orderNo', key: 'orderNo' },
      { title: '客户姓名', dataIndex: 'customerName', key: 'customerName' },
      { title: '维修店', dataIndex: 'shopName', key: 'shopName' },
      { title: '服务项目', dataIndex: 'serviceName', key: 'serviceName' },
      { title: '金额', dataIndex: 'amount', key: 'amount' },
      { title: '状态', dataIndex: 'status', key: 'status' },
      { title: '创建时间', dataIndex: 'createTime', key: 'createTime' },
      { title: '操作', key: 'action', width: 200 }
    ];

    const getStatusColor = (status: number) => {
      const colors: Record<number, string> = {
        1: 'orange', 2: 'blue', 3: 'cyan', 4: 'green', 5: 'red'
      };
      return colors[status] || 'default';
    };

    const getStatusText = (status: number) => {
      const texts: Record<number, string> = {
        1: '待确认', 2: '已确认', 3: '服务中', 4: '已完成', 5: '已取消'
      };
      return texts[status] || '未知';
    };

    const handleSearch = () => {
      pagination.value.current = 1;
      loadOrders();
    };

    const handleTableChange = (pag: any) => {
      pagination.value = { ...pagination.value, ...pag };
      loadOrders();
    };

    const loadOrders = async () => {
      loading.value = true;
      try {
        // TODO: 实现API调用
        // 模拟数据
        orders.value = [
          {
            id: 1,
            orderNo: 'ORD20240115001',
            customerName: '张先生',
            shopName: '汽车之家维修店',
            serviceName: '机油更换',
            amount: '¥150',
            status: 1,
            createTime: '2024-01-15 10:30:00'
          }
        ] as any;
        pagination.value.total = 1;
      } catch (error) {
        message.error('加载订单数据失败');
      } finally {
        loading.value = false;
      }
    };

    const viewOrder = (id: number) => {
      message.info(`查看订单 ${id}`);
    };

    const confirmOrder = (id: number) => {
      message.success(`订单 ${id} 已确认`);
      loadOrders();
    };

    const cancelOrder = (id: number) => {
      message.success(`订单 ${id} 已取消`);
      loadOrders();
    };

    onMounted(() => {
      loadOrders();
    });

    return {
      loading,
      orders,
      searchForm,
      pagination,
      columns,
      getStatusColor,
      getStatusText,
      handleSearch,
      handleTableChange,
      viewOrder,
      confirmOrder,
      cancelOrder
    };
  }
});
</script>

<style scoped>
.admin-order-management {
  padding: 24px;
  flex: 1;
  background: #f0f2f5;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #333;
}

.page-header p {
  margin: 0;
  color: #666;
}

.search-section {
  margin-bottom: 24px;
}
</style>
