<template>
  <div class="shop-dashboard">
    <div class="dashboard-header">
      <div class="welcome-section">
        <h1>维修店管理中心</h1>
        <p>{{ userInfo.realName || userInfo.username }} - 专业维修服务管理平台</p>
      </div>
      <div class="user-actions">
        <a-button type="primary" @click="goToServices">服务管理</a-button>
        <a-button @click="logout">退出登录</a-button>
      </div>
    </div>

    <div class="dashboard-content">
      <!-- 统计概览 -->
      <a-row :gutter="24" class="stats-overview">
        <a-col :span="6">
          <a-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon pending">
                <ClockCircleOutlined />
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.pendingOrders }}</div>
                <div class="stat-label">待处理订单</div>
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon processing">
                <ToolOutlined />
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.processingOrders }}</div>
                <div class="stat-label">进行中订单</div>
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon completed">
                <CheckCircleOutlined />
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ stats.completedOrders }}</div>
                <div class="stat-label">已完成订单</div>
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon revenue">
                <DollarOutlined />
              </div>
              <div class="stat-info">
                <div class="stat-number">¥{{ stats.monthlyRevenue }}</div>
                <div class="stat-label">本月收入</div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <a-row :gutter="24" style="margin-top: 24px;">
        <!-- 今日预约 -->
        <a-col :span="12">
          <a-card>
            <template #title>
              <div class="booking-header">
                <CalendarOutlined style="margin-right: 8px;" />
                今日预约
                <a-badge :count="todayBookings.length" style="margin-left: 12px;" />
              </div>
            </template>
            <template #extra>
              <a-space>
                <a-button size="small" @click="refreshBookings">
                  <ReloadOutlined />
                  刷新
                </a-button>
                <a @click="goToOrders" style="color: #1890ff;">查看全部</a>
              </a-space>
            </template>
            
            <div v-if="loadingBookings" class="booking-loading">
              <a-spin size="large">
                <template #indicator>
                  <LoadingOutlined style="font-size: 24px;" spin />
                </template>
              </a-spin>
              <p style="margin-top: 12px; color: #666;">加载预约信息中...</p>
            </div>

            <div v-else-if="todayBookings.length === 0" class="no-bookings">
              <a-empty description="今日暂无预约">
                <template #image>
                  <CalendarOutlined style="font-size: 48px; color: #d9d9d9;" />
                </template>
              </a-empty>
            </div>

            <div v-else class="booking-list">
              <div 
                v-for="item in todayBookings" 
                :key="item.id"
                class="booking-item"
                :class="`booking-status-${item.status}`"
              >
                <div class="booking-header-info">
                  <div class="booking-avatar">
                    <a-avatar :style="{ backgroundColor: getAvatarColor(item.status) }">
                      <UserOutlined />
                      </a-avatar>
                  </div>
                  <div class="booking-details">
                    <div class="booking-title">
                      <span class="service-name">{{ item.serviceName }}</span>
                      <a-tag :color="getStatusColor(item.status)" size="small">
                      {{ getStatusText(item.status) }}
                    </a-tag>
                  </div>
                    <div class="booking-info">
                      <span class="customer-name">
                        <UserOutlined style="margin-right: 4px;" />
                        {{ item.contactName }}
                      </span>
                      <span class="booking-time">
                        <ClockCircleOutlined style="margin-right: 4px;" />
                        {{ item.bookingTime }}
                      </span>
                      <span class="booking-price">
                        <DollarOutlined style="margin-right: 4px;" />
                        ¥{{ item.servicePrice }}
                      </span>
                    </div>
                    <div class="booking-contact" v-if="item.contactPhone">
                      <PhoneOutlined style="margin-right: 4px;" />
                      {{ item.contactPhone }}
                    </div>
                    <div class="booking-description" v-if="item.problemDescription">
                      <FileTextOutlined style="margin-right: 4px;" />
                      {{ item.problemDescription }}
                    </div>
                  </div>
                </div>
                
                <div class="booking-actions">
                  <a-space size="small">
                    <!-- 查看详情 -->
                    <a-tooltip title="查看详情">
                      <a-button size="small" @click="viewBookingDetail(item)">
                        <EyeOutlined />
                      </a-button>
                    </a-tooltip>
                    
                    <!-- 拨打电话 -->
                    <a-tooltip title="联系客户">
                      <a-button size="small" @click="callCustomer(item.contactPhone)">
                        <PhoneOutlined />
                      </a-button>
                    </a-tooltip>
                    
                    <!-- 根据状态显示不同的操作按钮 -->
                    <template v-if="item.status === 1">
                      <a-button 
                        type="primary" 
                        size="small" 
                        @click="confirmBooking(item.id)"
                      >
                        确认
                      </a-button>
                      <a-button 
                        danger 
                        size="small" 
                        @click="rejectBooking(item)"
                      >
                        婉拒
                      </a-button>
              </template>
                    
                    <template v-else-if="item.status === 2">
                      <a-button 
                        type="primary" 
                        size="small" 
                        @click="startService(item.id)"
                      >
                        开始
                      </a-button>
                    </template>
                    
                    <template v-else-if="item.status === 3">
                      <a-button 
                        type="primary" 
                        size="small" 
                        @click="completeService(item.id)"
                      >
                        完成
                      </a-button>
                    </template>
                    
                    <template v-else-if="item.status === 4">
                      <a-tag color="green">已完成</a-tag>
                    </template>
                  </a-space>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>

        <!-- 快速操作 -->
        <a-col :span="12">
          <a-card title="快速操作">
            <a-row :gutter="16">
              <a-col :span="12">
                <div class="quick-action-card" @click="goToOrders">
                  <FileTextOutlined class="action-icon" />
                  <h3>订单管理</h3>
                  <p>查看和处理所有维修订单</p>
                </div>
              </a-col>
              <a-col :span="12">
                <div class="quick-action-card" @click="goToServices">
                  <SettingOutlined class="action-icon" />
                  <h3>服务管理</h3>
                  <p>管理维修服务项目和价格</p>
                </div>
              </a-col>
              <a-col :span="12">
                <div class="quick-action-card" @click="goToTechnicians">
                  <TeamOutlined class="action-icon" />
                  <h3>技师管理</h3>
                  <p>管理技师信息和工作安排</p>
                </div>
              </a-col>
              <a-col :span="12">
                <div class="quick-action-card" @click="goToReports">
                  <BarChartOutlined class="action-icon" />
                  <h3>数据报告</h3>
                  <p>查看营收和服务统计数据</p>
                </div>
              </a-col>
            </a-row>
          </a-card>
        </a-col>
      </a-row>

      <!-- 热门服务统计 -->
      <a-row :gutter="24" style="margin-top: 24px;">
        <a-col :span="24">
          <a-card title="服务统计">
            <a-row :gutter="16">
              <a-col :span="6" v-for="service in serviceStats" :key="service.id">
                <div class="service-stat-card">
                  <div class="service-name">{{ service.name }}</div>
                  <div class="service-booking-count">{{ service.bookingCount }} 次预约</div>
                  <div class="service-revenue">收入: ¥{{ service.revenue }}</div>
                  <div class="service-rating">
                    <StarFilled style="color: #faad14;" />
                    {{ service.rating }} 分
                  </div>
                </div>
              </a-col>
            </a-row>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import axios from 'axios';
import { clearUserSession, safeNavigate } from '../../utils/auth';
import {
  ClockCircleOutlined,
  ToolOutlined,
  CheckCircleOutlined,
  DollarOutlined,
  UserOutlined,
  FileTextOutlined,
  SettingOutlined,
  TeamOutlined,
  BarChartOutlined,
  StarFilled,
  CalendarOutlined,
  ReloadOutlined,
  LoadingOutlined,
  EyeOutlined,
  PhoneOutlined
} from '@ant-design/icons-vue';

export default defineComponent({
  name: 'ShopDashboard',
  components: {
    ClockCircleOutlined,
    ToolOutlined,
    CheckCircleOutlined,
    DollarOutlined,
    UserOutlined,
    FileTextOutlined,
    SettingOutlined,
    TeamOutlined,
    BarChartOutlined,
    StarFilled,
    CalendarOutlined,
    ReloadOutlined,
    LoadingOutlined,
    EyeOutlined,
    PhoneOutlined
  },
  setup() {
    const router = useRouter();
    const loadingBookings = ref(false);
    
    const userInfo = ref<any>({});
    const todayBookings = ref<any[]>([]);
    const serviceStats = ref<any[]>([]);
    const stats = ref({
      pendingOrders: 0,
      processingOrders: 0,
      completedOrders: 0,
      monthlyRevenue: 0
    });

    const goToOrders = () => {
      safeNavigate(router, '/shop/orders');
    };

    const goToServices = () => {
      safeNavigate(router, '/shop/services');
    };

    const goToTechnicians = () => {
      safeNavigate(router, '/shop/technicians');
    };

    const goToReports = () => {
      safeNavigate(router, '/shop/reports');
    };

    const logout = () => {
      clearUserSession();
      message.success('退出登录成功');
      // 强制刷新到首页确保状态完全清除
      window.location.href = '/';
    };

    const handleBooking = async (bookingId: number, currentStatus: number) => {
      try {
        let nextStatus = currentStatus + 1;
        let actionText = '';
        
        switch (currentStatus) {
          case 1:
            nextStatus = 2;
            actionText = '确认预约';
            break;
          case 2:
            nextStatus = 3;
            actionText = '开始服务';
            break;
          case 3:
            nextStatus = 4;
            actionText = '完成服务';
            break;
          default:
            message.info('该预约无法进行下一步操作');
            return;
        }
        
        const response = await axios.put(`/shop/booking/${bookingId}/status?status=${nextStatus}`);
        if (response.data.success) {
          message.success(`${actionText}成功`);
          // 重新加载今日预约列表
          loadTodayBookings();
        } else {
          message.error('操作失败: ' + response.data.message);
        }
      } catch (error) {
        console.error('Handle booking error:', error);
        message.error('操作失败');
      }
    };

    // 刷新预约列表
    const refreshBookings = () => {
      loadTodayBookings();
      loadStats();
      message.success('数据已刷新');
    };

    // 获取头像颜色
    const getAvatarColor = (status: number) => {
      const colors: Record<number, string> = {
        1: '#fa8c16', // 橙色 - 待确认
        2: '#1890ff', // 蓝色 - 已确认
        3: '#52c41a', // 绿色 - 服务中
        4: '#722ed1', // 紫色 - 已完成
        5: '#f5222d'  // 红色 - 已取消
      };
      return colors[status] || '#d9d9d9';
    };

    // 查看预约详情
    const viewBookingDetail = (booking: any) => {
      import('ant-design-vue').then(antd => {
        const htmlContent = `
          <div style="max-height: 70vh; overflow-y: auto;">
            <!-- 头部客户信息 -->
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 20px; border-radius: 8px; margin-bottom: 20px; color: white; display: flex; align-items: center;">
              <div style="width: 64px; height: 64px; background: rgba(255,255,255,0.3); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 16px; border: 2px solid rgba(255,255,255,0.5); font-size: 24px;">
                👤
              </div>
              <div>
                <h3 style="color: white; margin: 0; font-size: 20px;">${booking.contactName}</h3>
                <p style="color: rgba(255,255,255,0.9); margin: 4px 0 0 0;">
                  📞 ${booking.contactPhone}
                </p>
              </div>
            </div>

            <!-- 服务信息卡片 -->
            <div style="background: #f8f9fa; padding: 16px; border-radius: 8px; margin-bottom: 16px; border-left: 4px solid #1890ff;">
              <h4 style="margin: 0 0 8px 0; color: #1890ff; display: flex; align-items: center;">
                🛠️ ${booking.serviceName}
              </h4>
              <div style="display: flex; align-items: center; gap: 16px; flex-wrap: wrap;">
                <span style="color: #666; display: flex; align-items: center;">
                  ⏰ ${booking.bookingTime}
                </span>
                <span style="color: #666; display: flex; align-items: center;">
                  💰 ¥${booking.servicePrice}
                </span>
                <span style="padding: 2px 8px; border-radius: 4px; font-size: 12px; color: white; background: ${getStatusColor(booking.status)};">
                  ${getStatusText(booking.status)}
                </span>
              </div>
            </div>

            <!-- 预约信息表格 -->
            <div style="margin-bottom: 16px;">
              <h4 style="color: #333; margin-bottom: 12px; display: flex; align-items: center;">
                📋 预约信息
              </h4>
              <table style="width: 100%; border-collapse: collapse; border: 1px solid #f0f0f0;">
                <tr>
                  <td style="padding: 8px 12px; border: 1px solid #f0f0f0; background: #fafafa; font-weight: 500; width: 120px;">预约编号</td>
                  <td style="padding: 8px 12px; border: 1px solid #f0f0f0;" colspan="3">
                    <code style="background: #f5f5f5; padding: 2px 8px; border-radius: 4px; color: #722ed1;">
                      ${booking.bookingNo || '未生成'}
                    </code>
                  </td>
                </tr>
                <tr>
                  <td style="padding: 8px 12px; border: 1px solid #f0f0f0; background: #fafafa; font-weight: 500;">预约日期</td>
                  <td style="padding: 8px 12px; border: 1px solid #f0f0f0; color: #1890ff;">📅 今日</td>
                  <td style="padding: 8px 12px; border: 1px solid #f0f0f0; background: #fafafa; font-weight: 500;">预约时间</td>
                  <td style="padding: 8px 12px; border: 1px solid #f0f0f0; color: #52c41a; font-weight: 500;">${booking.bookingTime}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 12px; border: 1px solid #f0f0f0; background: #fafafa; font-weight: 500;">服务时长</td>
                  <td style="padding: 8px 12px; border: 1px solid #f0f0f0;">${booking.estimatedDuration ? booking.estimatedDuration + ' 分钟' : '60 分钟'}</td>
                  <td style="padding: 8px 12px; border: 1px solid #f0f0f0; background: #fafafa; font-weight: 500;">服务价格</td>
                  <td style="padding: 8px 12px; border: 1px solid #f0f0f0;">
                    <span style="color: #f5222d; font-weight: 600; font-size: 16px;">¥${booking.servicePrice}</span>
                  </td>
                </tr>
              </table>
            </div>

            ${booking.problemDescription ? `
              <!-- 问题描述 -->
              <div style="margin-bottom: 16px;">
                <h4 style="color: #333; margin-bottom: 12px; display: flex; align-items: center;">
                  💬 问题描述
                </h4>
                <div style="background: #fffbe6; border: 1px solid #ffe58f; border-radius: 6px; padding: 12px;">
                  <p style="margin: 0; line-height: 1.6; color: #666;">
                    ${booking.problemDescription}
                  </p>
                </div>
              </div>
            ` : ''}

              <!-- 操作按钮 -->
              <div style="border-top: 1px solid #f0f0f0; padding-top: 16px; text-align: center;">
                <div style="display: flex; gap: 8px; justify-content: center; flex-wrap: wrap; margin-bottom: 16px;">
                  <button 
                    onclick="window.callCustomer('${booking.contactPhone}')"
                    style="background: #1890ff; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer; display: inline-flex; align-items: center; gap: 4px;"
                  >
                    📞 联系客户
                  </button>
                  ${booking.status === 1 ? `
                    <button 
                      onclick="window.confirmBooking(${booking.id}); window.closeModal();"
                      style="background: #52c41a; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer; display: inline-flex; align-items: center; gap: 4px;"
                    >
                      ✅ 确认预约
                    </button>
                    <button 
                      onclick="window.rejectBookingFromModal(${booking.id}); window.closeModal();"
                      style="background: #f5222d; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer; display: inline-flex; align-items: center; gap: 4px;"
                    >
                      ❌ 婉拒预约
                    </button>
                  ` : ''}
                  ${booking.status === 2 ? `
                    <button 
                      onclick="window.startService(${booking.id}); window.closeModal();"
                      style="background: #1890ff; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer; display: inline-flex; align-items: center; gap: 4px;"
                    >
                      🚀 开始服务
                    </button>
                  ` : ''}
                  ${booking.status === 3 ? `
                    <button 
                      onclick="window.completeService(${booking.id}); window.closeModal();"
                      style="background: #52c41a; color: white; border: none; padding: 8px 16px; border-radius: 6px; cursor: pointer; display: inline-flex; align-items: center; gap: 4px;"
                    >
                      🎯 完成服务
                    </button>
                  ` : ''}
                </div>
                <!-- 关闭按钮 -->
                <div style="text-align: center;">
                  <button 
                    onclick="window.closeModal()"
                    style="background: #f0f0f0; color: #666; border: 1px solid #d9d9d9; padding: 8px 24px; border-radius: 6px; cursor: pointer; display: inline-flex; align-items: center; gap: 4px; font-weight: 500;"
                  >
                    ❌ 关闭
                  </button>
                </div>
              </div>
          </div>
        `;

        // 暴露函数到window对象供按钮调用
        (window as any).callCustomer = callCustomer;
        (window as any).confirmBooking = confirmBooking;
        (window as any).startService = startService;
        (window as any).completeService = completeService;
        (window as any).rejectBookingFromModal = rejectBooking;
        (window as any).closeModal = () => antd.Modal.destroyAll();

        // 使用Modal.info，但先用临时内容，然后更新
        const modal = antd.Modal.info({
          title: '👁️ 预约详情',
          width: 700,
          content: '加载中...',
          okText: '关闭',
          icon: null,
          wrapClassName: 'booking-detail-modal',
          maskClosable: true,  // 点击遮罩层可关闭
          keyboard: true       // 按ESC键可关闭
        });

        // 延迟设置HTML内容
        setTimeout(() => {
          const modalBody = document.querySelector('.booking-detail-modal .ant-modal-body');
          if (modalBody) {
            modalBody.innerHTML = htmlContent;
          }
        }, 50);
      });
    };

    // 联系客户
    const callCustomer = (phone: string) => {
      if (phone) {
        // 尝试调用系统拨号功能
        window.open(`tel:${phone}`);
        message.info(`正在拨打 ${phone}`);
      } else {
        message.error('客户电话号码不可用');
      }
    };

    // 确认预约
    const confirmBooking = async (bookingId: number) => {
      try {
        const response = await axios.put(`/shop/booking/${bookingId}/status?status=2`);
        if (response.data.success) {
          message.success('预约确认成功');
          loadTodayBookings();
        } else {
          message.error('确认失败: ' + response.data.message);
        }
      } catch (error) {
        console.error('Confirm booking error:', error);
        message.error('确认失败');
      }
    };

    // 开始服务
    const startService = async (bookingId: number) => {
      try {
        const response = await axios.put(`/shop/booking/${bookingId}/status?status=3`);
        if (response.data.success) {
          message.success('服务已开始');
          loadTodayBookings();
        } else {
          message.error('操作失败: ' + response.data.message);
        }
      } catch (error) {
        console.error('Start service error:', error);
        message.error('操作失败');
      }
    };

    // 完成服务
    const completeService = async (bookingId: number) => {
      try {
        const response = await axios.put(`/shop/booking/${bookingId}/status?status=4`);
        if (response.data.success) {
          message.success('服务已完成');
          loadTodayBookings();
          loadStats(); // 刷新统计数据
        } else {
          message.error('操作失败: ' + response.data.message);
        }
      } catch (error) {
        console.error('Complete service error:', error);
        message.error('操作失败');
      }
    };

    // 婉拒预约
    const rejectBooking = (booking: any) => {
      import('ant-design-vue').then(antd => {
        // 创建一个输入框让用户输入婉拒原因
        let rejectReason = '';
        
        const modal = antd.Modal.confirm({
          title: '婉拒预约',
          width: 500,
          content: `
            <div style="padding: 16px 0;">
              <p style="margin-bottom: 16px;">
                确定要婉拒客户 <strong>"${booking.contactName}"</strong> 的 <strong>"${booking.serviceName}"</strong> 预约吗？
              </p>
              <div style="margin-bottom: 12px;">
                <label style="display: block; margin-bottom: 8px; font-weight: 500;">婉拒原因（可选）：</label>
                <textarea 
                  id="rejectReasonInput"
                  placeholder="请输入婉拒原因，将会发送给客户..."
                  rows="3"
                  maxlength="200"
                  style="width: 100%; padding: 8px; border: 1px solid #d9d9d9; border-radius: 4px; resize: vertical;"
                ></textarea>
              </div>
              <p style="color: #666; font-size: 12px; margin: 0;">
                * 婉拒后预约状态将变更为"已取消"，建议您提供婉拒原因以便客户理解。
              </p>
            </div>
          `,
          okText: '确认婉拒',
          okType: 'danger',
          cancelText: '取消',
          onOk: async () => {
            try {
              // 从输入框获取婉拒原因
              const textarea = document.getElementById('rejectReasonInput') as HTMLTextAreaElement;
              const reason = textarea ? textarea.value.trim() : '';
              
              const params = reason ? `?reason=${encodeURIComponent(reason)}` : '';
              const response = await axios.put(`/shop/booking/${booking.id}/reject${params}`);
              
              if (response.data.success) {
                message.success('已婉拒预约');
                loadTodayBookings();
                loadStats(); // 刷新统计数据
              } else {
                message.error('操作失败: ' + response.data.message);
              }
            } catch (error) {
              console.error('Reject booking error:', error);
              message.error('操作失败');
            }
          }
        });
      });
    };

    const getStatusColor = (status: number) => {
      const colors: Record<number, string> = {
        1: 'orange',
        2: 'blue', 
        3: 'cyan',
        4: 'green',
        5: 'red'
      };
      return colors[status] || 'default';
    };

    const getStatusText = (status: number) => {
      const texts: Record<number, string> = {
        1: '待确认',
        2: '已确认',
        3: '服务中', 
        4: '已完成',
        5: '已取消'
      };
      return texts[status] || '未知';
    };

    const getNextActionText = (status: number) => {
      const actions: Record<number, string> = {
        1: '确认',
        2: '开始',
        3: '完成'
      };
      return actions[status] || '处理';
    };

    const loadUserInfo = () => {
      const userInfoStr = localStorage.getItem('userInfo');
      if (userInfoStr) {
        try {
          userInfo.value = JSON.parse(userInfoStr);
        } catch (error) {
          console.error('Parse user info error:', error);
        }
      }
    };

    const loadTodayBookings = async () => {
      loadingBookings.value = true;
      try {
        const response = await axios.get('/shop/booking/today');
        if (response.data.success) {
          todayBookings.value = response.data.content || [];
        } else {
          message.error('获取今日预约失败: ' + response.data.message);
          todayBookings.value = [];
        }
      } catch (error) {
        console.error('Load bookings error:', error);
        message.error('获取今日预约失败');
        todayBookings.value = [];
      } finally {
        loadingBookings.value = false;
      }
    };

    const loadServiceStats = () => {
      // TODO: 实现服务统计API
      serviceStats.value = [
        { id: 1, name: '机油更换', bookingCount: 25, revenue: 3750, rating: 4.8 },
        { id: 2, name: '轮胎更换', bookingCount: 18, revenue: 7200, rating: 4.7 },
        { id: 3, name: '刹车维修', bookingCount: 12, revenue: 3600, rating: 4.9 },
        { id: 4, name: '空调清洗', bookingCount: 20, revenue: 3600, rating: 4.6 }
      ];
    };

    const loadStats = async () => {
      try {
        const response = await axios.get('/shop/booking/stats');
        if (response.data.success) {
          stats.value = response.data.content;
        } else {
          console.error('获取统计数据失败:', response.data.message);
      stats.value = {
            pendingOrders: 0,
            processingOrders: 0,
            completedOrders: 0,
            monthlyRevenue: 0
          };
        }
      } catch (error) {
        console.error('Load stats error:', error);
        stats.value = {
          pendingOrders: 0,
          processingOrders: 0,
          completedOrders: 0,
          monthlyRevenue: 0
        };
      }
    };

    onMounted(() => {
      loadUserInfo();
      loadTodayBookings();
      loadServiceStats();
      loadStats();
    });

    return {
      userInfo,
      todayBookings,
      serviceStats,
      stats,
      loadingBookings,
      goToOrders,
      goToServices,
      goToTechnicians,
      goToReports,
      logout,
      handleBooking,
      getStatusColor,
      getStatusText,
      getNextActionText,
      refreshBookings,
      getAvatarColor,
      viewBookingDetail,
      callCustomer,
      confirmBooking,
      startService,
      completeService,
      rejectBooking
    };
  }
});
</script>

<style scoped>
.shop-dashboard {
  min-height: 100vh;
  background: #f0f2f5;
  padding: 24px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.welcome-section h1 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 28px;
}

.welcome-section p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.user-actions .ant-btn {
  margin-left: 12px;
}

.stats-overview .stat-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stat-icon.pending {
  background: #fa8c16;
}

.stat-icon.processing {
  background: #1890ff;
}

.stat-icon.completed {
  background: #52c41a;
}

.stat-icon.revenue {
  background: #722ed1;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.stat-label {
  color: #666;
  font-size: 14px;
}

.booking-actions {
  text-align: right;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.quick-action-card {
  padding: 24px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  height: 140px;
  margin-bottom: 16px;
}

.quick-action-card:hover {
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
  transform: translateY(-2px);
}

.quick-action-card .action-icon {
  font-size: 32px;
  color: #1890ff;
  margin-bottom: 12px;
}

.quick-action-card h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #333;
}

.quick-action-card p {
  margin: 0;
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.service-stat-card {
  padding: 20px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  text-align: center;
}

.service-name {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.service-booking-count {
  color: #1890ff;
  font-size: 14px;
  margin-bottom: 4px;
}

.service-revenue {
  color: #52c41a;
  font-size: 14px;
  margin-bottom: 4px;
}

.service-rating {
  color: #666;
  font-size: 14px;
}

/* 新增的今日预约样式 */
.booking-header {
  display: flex;
  align-items: center;
  font-weight: 600;
}

.booking-loading {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.no-bookings {
  text-align: center;
  padding: 40px 20px;
}

.booking-list {
  max-height: 600px;
  overflow-y: auto;
}

.booking-item {
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  background: white;
  transition: all 0.3s ease;
  position: relative;
}

.booking-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #d9d9d9;
}

.booking-item:last-child {
  margin-bottom: 0;
}

/* 根据预约状态添加不同的左边框 */
.booking-status-1 {
  border-left: 4px solid #fa8c16;
}

.booking-status-2 {
  border-left: 4px solid #1890ff;
}

.booking-status-3 {
  border-left: 4px solid #52c41a;
}

.booking-status-4 {
  border-left: 4px solid #722ed1;
}

.booking-status-5 {
  border-left: 4px solid #f5222d;
}

.booking-header-info {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
}

.booking-avatar {
  margin-right: 12px;
  flex-shrink: 0;
}

.booking-details {
  flex: 1;
  min-width: 0;
}

.booking-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.service-name {
  font-weight: 600;
  font-size: 16px;
  color: #333;
  margin-right: 8px;
}

.booking-info {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 8px;
}

.booking-info > span {
  display: flex;
  align-items: center;
  font-size: 13px;
  color: #666;
}

.customer-name {
  color: #333;
  font-weight: 500;
}

.booking-time {
  color: #1890ff;
  font-weight: 500;
}

.booking-price {
  color: #f5222d;
  font-weight: 600;
}

.booking-contact, .booking-description {
  display: flex;
  align-items: flex-start;
  font-size: 13px;
  color: #666;
  margin-bottom: 4px;
  line-height: 1.4;
}

.booking-description {
  margin-bottom: 8px;
}

.booking-actions {
  display: flex;
  justify-content: flex-end;
  padding-top: 8px;
  border-top: 1px solid #f5f5f5;
}

.booking-actions .ant-btn {
  margin-left: 8px;
}

.booking-actions .ant-btn:first-child {
  margin-left: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .booking-item {
    padding: 12px;
  }
  
  .booking-info {
    flex-direction: column;
    gap: 8px;
  }
  
  .booking-actions {
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .booking-actions .ant-btn {
    margin-left: 0;
    flex: 1;
    min-width: 80px;
  }
}

/* 动画效果 */
.booking-item {
  animation: fadeInUp 0.3s ease-in-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态优化 */
.booking-loading .ant-spin {
  color: #1890ff;
}

/* 预约详情模态框样式 */
:deep(.booking-detail-modal) {
  .ant-modal-content {
    border-radius: 12px;
    overflow: hidden;
  }
  
  .ant-modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-bottom: none;
    padding: 20px 24px;
  }
  
  .ant-modal-title {
    color: white;
    font-size: 18px;
    font-weight: 600;
  }
  
  .ant-modal-close {
    color: white;
    opacity: 0.8;
  }
  
  .ant-modal-close:hover {
    color: white;
    opacity: 1;
  }
  
  .ant-modal-body {
    padding: 0;
    max-height: 70vh;
    overflow-y: auto;
  }
  
  .ant-modal-footer {
    border-top: 1px solid #f0f0f0;
    padding: 16px 24px;
    text-align: center;
  }
  
  .ant-descriptions-bordered .ant-descriptions-item-label {
    background: #fafafa;
    font-weight: 500;
    color: #333;
  }
  
  .ant-descriptions-bordered .ant-descriptions-item-content {
    background: white;
  }
  
  .ant-btn {
    border-radius: 6px;
    height: 36px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
  }
}

/* 响应式处理 */
@media (max-width: 768px) {
  :deep(.booking-detail-modal) {
    .ant-modal {
      width: 95% !important;
      max-width: none;
      margin: 10px auto;
    }
    
    .ant-modal-content {
      margin: 0;
    }
    
    .ant-descriptions {
      font-size: 13px;
    }
    
    .ant-space {
      flex-direction: column;
      width: 100%;
    }
    
    .ant-space .ant-btn {
      width: 100%;
    }
  }
}
</style>
