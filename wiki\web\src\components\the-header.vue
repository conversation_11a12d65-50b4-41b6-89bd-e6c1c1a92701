<template>
  <div class="header">
    <div class="logo">
      <router-link to="/">
        <span class="logo-text">汽车维修服务平台</span>
      </router-link>
    </div>
    
    <a-menu
        v-model:selectedKeys="selectedKeys1"
        theme="dark"
        mode="horizontal"
        class="main-menu"
    >
      <a-menu-item v-for="item in navigationMenu" :key="item.key" @click="handleMenuClick(item)">
        {{ item.title }}
      </a-menu-item>
    </a-menu>
    
    <!-- 用户菜单 -->
    <div class="user-menu">
      <template v-if="!isLoggedIn">
        <a-button type="link" style="color: #fff" @click="goToLogin">
          登录
        </a-button>
        <a-button type="primary" @click="goToRegister">
          注册
        </a-button>
      </template>
      <template v-else>
        <a-dropdown>
          <a class="ant-dropdown-link" @click.prevent style="color: #fff">
            <a-avatar :src="userInfo.avatar" style="margin-right: 8px">
              {{ getUserInitial() }}
            </a-avatar>
            {{ userInfo.realName || userInfo.username }}
            <DownOutlined />
          </a>
          <template #overlay>
            <a-menu>
              <a-menu-item v-for="item in userDropdownMenu" :key="item.key" @click="item.handler">
                <component :is="item.icon" v-if="item.icon" />
                {{ item.title }}
              </a-menu-item>
              <a-menu-divider />
              <a-menu-item key="logout" @click="handleLogout">
                <LogoutOutlined />
                退出登录
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </template>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import { 
  DownOutlined, 
  UserOutlined, 
  ShoppingCartOutlined, 
  CarOutlined, 
  LogoutOutlined,
  TeamOutlined,
  SettingOutlined,
  FileTextOutlined,
  BarChartOutlined,
  ToolOutlined,
  HomeOutlined,
  InfoCircleOutlined
} from '@ant-design/icons-vue';
import { getCurrentUser, safeNavigate, clearUserSession, USER_TYPES } from '../utils/auth';
import { eventBus, AUTH_EVENTS } from '../utils/eventBus';

export default defineComponent({
  name: 'TheHeader',
  components: {
    DownOutlined,
    UserOutlined,
    ShoppingCartOutlined,
    CarOutlined,
    LogoutOutlined,
    TeamOutlined,
    SettingOutlined,
    FileTextOutlined,
    BarChartOutlined,
    ToolOutlined,
    HomeOutlined,
    InfoCircleOutlined
  },
  setup() {
    const router = useRouter();
    const route = useRoute();
    const selectedKeys1 = ref<string[]>([]);
    
    // 使用响应式状态管理登录状态，因为computed无法监听localStorage变化
    const loginState = ref(0); // 用于强制更新的状态
    
    const currentUser = computed(() => {
      // 访问loginState.value来建立响应式依赖
      loginState.value;
      return getCurrentUser();
    });
    
    const isLoggedIn = computed(() => {
      return !!currentUser.value;
    });
    
    const userInfo = computed(() => {
      return currentUser.value || {};
    });
    
    // 强制更新登录状态
    const forceUpdateLoginState = () => {
      loginState.value++;
    };

    // 更新导航菜单选中状态
    const updateSelectedKeys = () => {
      const currentPath = route.path;
      const menu = navigationMenu.value;
      
      console.log(`Updating navigation selection - Current path: ${currentPath}, Menu:`, menu);
      
      // 查找当前路径对应的菜单项
      const currentMenuItem = menu.find(item => item.path === currentPath);
      if (currentMenuItem) {
        selectedKeys1.value = [currentMenuItem.key];
        console.log(`Selected menu item: ${currentMenuItem.title} (key: ${currentMenuItem.key})`);
      } else {
        // 如果没有找到精确匹配，尝试匹配父路径
        const matchingItem = menu.find(item => currentPath.startsWith(item.path) && item.path !== '/');
        if (matchingItem) {
          selectedKeys1.value = [matchingItem.key];
          console.log(`Selected parent menu item: ${matchingItem.title} (key: ${matchingItem.key})`);
        } else {
          // 默认不选中任何项
          selectedKeys1.value = [];
          console.log('No matching menu item found, clearing selection');
        }
      }
    };

    // 导航菜单根据用户角色动态生成
    const navigationMenu = computed(() => {
      const user = currentUser.value;
      
      if (!user) {
        // 未登录用户显示公共菜单
        return [
          { key: '1', title: '首页', path: '/' },
          { key: '2', title: '关于我们', path: '/about' }
        ];
      }
      
      switch (user.userType) {
        case USER_TYPES.ADMIN: // 管理员
          return [
            { key: '1', title: '首页', path: '/admin/dashboard' },
            { key: '2', title: '用户管理', path: '/admin/users' },
            { key: '3', title: '订单管理', path: '/admin/orders' },
            { key: '4', title: '服务管理', path: '/admin/services' },
            { key: '5', title: '系统设置', path: '/admin/settings' }
          ];
          
        case USER_TYPES.SHOP: // 维修店
          return [
            { key: '1', title: '首页', path: '/shop/dashboard' },
            { key: '2', title: '订单管理', path: '/shop/orders' },
            { key: '3', title: '服务管理', path: '/shop/services' },
            { key: '4', title: '技师管理', path: '/shop/technicians' },
            { key: '5', title: '数据报告', path: '/shop/reports' }
          ];
          
        case USER_TYPES.OWNER: // 车主
          return [
            { key: '1', title: '首页', path: '/owner/dashboard' },
            { key: '2', title: '服务预约', path: '/booking' },
            { key: '3', title: '我的车辆', path: '/owner/vehicles' },
            { key: '4', title: '维修记录', path: '/owner/orders' },
            { key: '5', title: '关于我们', path: '/about' }
          ];
          
        default:
          return [
            { key: '1', title: '首页', path: '/' },
            { key: '2', title: '关于我们', path: '/about' }
          ];
      }
    });

    // 用户下拉菜单根据角色动态生成
    const userDropdownMenu = computed(() => {
      const user = currentUser.value;
      if (!user) return [];
      
      switch (user.userType) {
        case USER_TYPES.ADMIN: // 管理员
          return [
            { key: 'dashboard', title: '管理中心', icon: 'SettingOutlined', handler: () => safeNavigate(router, '/admin/dashboard') },
            { key: 'users', title: '用户管理', icon: 'TeamOutlined', handler: () => safeNavigate(router, '/admin/users') },
            { key: 'reports', title: '数据报告', icon: 'BarChartOutlined', handler: () => safeNavigate(router, '/admin/reports') }
          ];
          
        case USER_TYPES.SHOP: // 维修店
          return [
            { key: 'dashboard', title: '管理中心', icon: 'SettingOutlined', handler: () => safeNavigate(router, '/shop/dashboard') },
            { key: 'orders', title: '订单管理', icon: 'FileTextOutlined', handler: () => safeNavigate(router, '/shop/orders') },
            { key: 'services', title: '服务管理', icon: 'ToolOutlined', handler: () => safeNavigate(router, '/shop/services') }
          ];
          
        case USER_TYPES.OWNER: // 车主
          return [
            { key: 'dashboard', title: '个人中心', icon: 'UserOutlined', handler: () => safeNavigate(router, '/owner/dashboard') },
            { key: 'vehicles', title: '我的车辆', icon: 'CarOutlined', handler: () => safeNavigate(router, '/owner/vehicles') },
            { key: 'orders', title: '维修记录', icon: 'FileTextOutlined', handler: () => safeNavigate(router, '/owner/orders') }
          ];
          
        default:
          return [
            { key: 'profile', title: '个人中心', icon: 'UserOutlined', handler: () => message.info('个人中心页面开发中...') }
          ];
      }
    });
    
    const goToLogin = () => {
      console.log('Navigating to login page');
      router.push('/login');
    };
    
    const goToRegister = () => {
      console.log('Navigating to register page');
      router.push('/register');
    };
    
    const handleMenuClick = (menuItem: any) => {
      if (!menuItem.path) return;
      
      const user = currentUser.value;
      
      // 如果用户未登录，直接导航到公共页面
      if (!user) {
        const publicPages = ['/', '/about'];
        if (publicPages.includes(menuItem.path)) {
          console.log(`Navigating to public page: ${menuItem.path}`);
          router.push(menuItem.path);
        } else {
          console.log(`Non-public page requires login: ${menuItem.path}`);
          router.push('/login');
        }
        return;
      }
      
      // 已登录用户使用安全导航
      safeNavigate(router, menuItem.path);
    };

    const handleLogout = () => {
      clearUserSession();
      message.success('退出登录成功');
      // 跳转到首页，不需要强制刷新了
      router.push('/');
    };
    
    const getUserInitial = () => {
      const user = userInfo.value as any;
      if (user && user.realName && user.realName.length > 0) {
        return user.realName.charAt(0);
      }
      if (user && user.username && user.username.length > 0) {
        return user.username.charAt(0);
      }
      return '用';
    };
    
    // 监听认证状态变化事件
    const handleAuthStateChange = () => {
      forceUpdateLoginState();
      // 用户状态变化后也要更新选中状态
      setTimeout(updateSelectedKeys, 0);
    };
    
    // 监听路由变化
    watch(() => route.path, () => {
      updateSelectedKeys();
    }, { immediate: true });
    
    // 监听导航菜单变化
    watch(navigationMenu, () => {
      updateSelectedKeys();
    }, { immediate: true });
    
    // 组件挂载时设置事件监听
    onMounted(() => {
      forceUpdateLoginState();
      updateSelectedKeys();
      eventBus.on(AUTH_EVENTS.AUTH_STATE_CHANGED, handleAuthStateChange);
    });
    
    // 组件卸载时清除事件监听
    onUnmounted(() => {
      eventBus.off(AUTH_EVENTS.AUTH_STATE_CHANGED, handleAuthStateChange);
    });
    
    return {
      selectedKeys1,
      isLoggedIn,
      userInfo,
      navigationMenu,
      userDropdownMenu,
      goToLogin,
      goToRegister,
      handleMenuClick,
      handleLogout,
      getUserInitial,
      forceUpdateLoginState
    };
  },
});
</script>

<style scoped>
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  background: #001529;
  color: #fff;
  height: 64px;
  flex-shrink: 0;
}

.logo {
  height: 32px;
  margin: 16px 24px 16px 0;
}

.logo-text {
  color: rgba(36, 132, 124, 0.95);
  font-size: 25px;
  font-weight: bold;
  text-decoration: none;
}

.logo a {
  text-decoration: none;
}

.main-menu {
  flex: 1;
  border-bottom: none;
}

.user-menu {
  display: flex;
  align-items: center;
  gap: 12px;
}

.ant-dropdown-link {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.3s;
  text-decoration: none;
}

.ant-dropdown-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header {
    padding: 0 16px;
  }
  
  .logo {
    margin: 16px 16px 16px 0;
  }
}

@media (max-width: 768px) {
  .header {
    flex-wrap: wrap;
    padding: 0 12px;
  }
  
  .logo {
    margin: 8px 16px 8px 0;
  }
  
  .logo-text {
    font-size: 16px;
  }
  
  .main-menu {
    display: none; /* 在移动端隐藏菜单，可以考虑添加折叠菜单 */
  }
  
  .user-menu {
    gap: 8px;
  }
  
  .ant-btn {
    font-size: 12px;
    height: 32px;
    padding: 0 12px;
  }
}

@media (max-width: 576px) {
  .logo-text {
    font-size: 14px;
  }
  
  .user-menu .ant-btn {
    font-size: 11px;
    height: 28px;
    padding: 0 8px;
  }
}
</style>