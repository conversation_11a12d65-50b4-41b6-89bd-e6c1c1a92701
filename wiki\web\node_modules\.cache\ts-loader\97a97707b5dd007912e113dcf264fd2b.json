{"remainingRequest": "D:\\JavaCar\\wiki\\wiki\\web\\node_modules\\ts-loader\\index.js??ref--13-1!D:\\JavaCar\\wiki\\wiki\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\JavaCar\\wiki\\wiki\\web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\JavaCar\\wiki\\wiki\\web\\src\\views\\Register.vue?vue&type=script&lang=ts", "dependencies": [{"path": "D:\\JavaCar\\wiki\\wiki\\web\\src\\views\\Register.vue", "mtime": 1757594543302}, {"path": "D:\\JavaCar\\wiki\\wiki\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750678170000}, {"path": "D:\\JavaCar\\wiki\\wiki\\web\\node_modules\\ts-loader\\index.js", "mtime": 1750678172000}, {"path": "D:\\JavaCar\\wiki\\wiki\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750678170000}, {"path": "D:\\JavaCar\\wiki\\wiki\\web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1750678172000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "file": "D:\\JavaCar\\wiki\\wiki\\web\\src\\views\\Register.vue.ts", "sourceRoot": "", "sources": ["D:\\JavaCar\\wiki\\wiki\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\JavaCar\\wiki\\wiki\\web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\JavaCar\\wiki\\wiki\\web\\src\\views\\Register.vue?vue&type=script&lang=ts"], "names": [], "mappings": "AACA,OAAO,EAAE,eAAe,EAAE,GAAG,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,KAAK,CAAC;AAC7D,OAAO,EACL,YAAY,EACZ,YAAY,EACZ,aAAa,EACb,YAAY,EACZ,cAAc,EACd,WAAW,EACX,YAAY,EACZ,mBAAmB,EACpB,MAAM,uBAAuB,CAAC;AAC/B,OAAO,EAAE,OAAO,EAAE,MAAM,gBAAgB,CAAC;AACzC,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,EAAE,SAAS,EAAE,MAAM,YAAY,CAAC;AAEvC,eAAe,eAAe,CAAC;IAC7B,IAAI,EAAE,UAAU;IAChB,UAAU,EAAE;QACV,YAAY;QACZ,YAAY;QACZ,aAAa;QACb,YAAY;QACZ,cAAc;QACd,WAAW;QACX,YAAY;QACZ,mBAAmB;KACpB;IACD,KAAK;QACH,MAAM,MAAM,GAAG,SAAS,EAAE,CAAC;QAC3B,MAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;QAC3B,MAAM,UAAU,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;QAC9B,MAAM,oBAAoB,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;QACxC,MAAM,oBAAoB,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;QACxC,MAAM,eAAe,GAAG,GAAG,EAAE,CAAC;QAE9B,MAAM,YAAY,GAAG,GAAG,CAAC;YACvB,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,EAAE;YACZ,QAAQ,EAAE,EAAE;YACZ,KAAK,EAAE,EAAE;YACT,KAAK,EAAE,EAAE;YACT,WAAW,EAAE,EAAE;YACf,QAAQ,EAAE,EAAE;YACZ,eAAe,EAAE,EAAE;YACnB,QAAQ,EAAE,CAAC,CAAC,SAAS;SACtB,CAAC,CAAC;QAEH,OAAO;QACP,MAAM,gBAAgB,GAAG,GAAG,CAAC;YAC3B,iBAAiB,EAAE,CAAC;YACpB,sBAAsB,EAAE,KAAK;SAC9B,CAAC,CAAC;QAEH,SAAS;QACT,MAAM,oBAAoB,GAAG,KAAK,IAAI,EAAE;YACtC,IAAI;gBACF,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;gBACjE,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE;oBACzB,gBAAgB,CAAC,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC;iBAChD;aACF;YAAC,OAAO,KAAK,EAAE;gBACd,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;aACnC;QACH,CAAC,CAAC;QAGF,SAAS;QACT,MAAM,qBAAqB,GAAG,GAAG,EAAE,CAAC,CAAC;YACnC,QAAQ,EAAE;gBACR,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE;aACxD;YACD,KAAK,EAAE;gBACL,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE;gBACtD,EAAE,OAAO,EAAE,eAAe,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE;aACpE;YACD,KAAK,EAAE;gBACL,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,OAAO,EAAE,MAAM,EAAE;aAC1D;YACD,QAAQ,EAAE;gBACR,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE;aAC1D;YACD,QAAQ,EAAE;gBACR,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE;gBACrD,EAAE,GAAG,EAAE,CAAC,EAAE,OAAO,EAAE,YAAY,EAAE,OAAO,EAAE,MAAM,EAAE;aACnD;YACC,eAAe,EAAE;gBACf,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE;gBACrD;oBACE,SAAS,EAAE,CAAC,KAAU,EAAE,KAAa,EAAE,EAAE;wBACvC,IAAI,CAAC,KAAK;4BAAE,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;wBACrC,IAAI,KAAK,KAAK,YAAY,CAAC,KAAK,CAAC,QAAQ,EAAE;4BACzC,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;yBAChD;wBACD,OAAO,OAAO,CAAC,OAAO,EAAE,CAAC;oBAC3B,CAAC;oBACD,OAAO,EAAE,MAAM;iBAChB;aACF;YACH,QAAQ,EAAE;gBACR,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE;gBACtD,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,OAAO,EAAE,eAAe,EAAE,OAAO,EAAE,MAAM,EAAE;aAC/D;YACD,QAAQ,EAAE;gBACR,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE;gBACvD,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,EAAE,OAAO,EAAE,gBAAgB,EAAE,OAAO,EAAE,MAAM,EAAE;aAChE;YACD,WAAW,EAAE;gBACX,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE;gBACvD,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,OAAO,EAAE,eAAe,EAAE,OAAO,EAAE,MAAM,EAAE;aAChE;SACF,CAAC,CAAC;QAEH,MAAM,aAAa,GAAG,GAAG,CAAC,qBAAqB,EAAE,CAAC,CAAC;QAEnD,MAAM,cAAc,GAAG,KAAK,IAAI,EAAE;YAChC,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE;gBACrB,OAAO,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;gBACjC,OAAO;aACR;YAED,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;YACrB,IAAI;gBACF,gBAAgB;gBAChB,MAAM,WAAW,GAAQ;oBACvB,QAAQ,EAAE,YAAY,CAAC,KAAK,CAAC,QAAQ;oBACrC,KAAK,EAAE,YAAY,CAAC,KAAK,CAAC,KAAK;oBAC/B,KAAK,EAAE,YAAY,CAAC,KAAK,CAAC,KAAK;oBAC/B,QAAQ,EAAE,YAAY,CAAC,KAAK,CAAC,QAAQ;oBACrC,QAAQ,EAAE,YAAY,CAAC,KAAK,CAAC,QAAQ;iBACtC,CAAC;gBAEF,IAAI,YAAY,CAAC,KAAK,CAAC,QAAQ,KAAK,CAAC,EAAE;oBACrC,OAAO;oBACP,WAAW,CAAC,QAAQ,GAAG,YAAY,CAAC,KAAK,CAAC,QAAQ,CAAC;iBACpD;qBAAM,IAAI,YAAY,CAAC,KAAK,CAAC,QAAQ,KAAK,CAAC,EAAE;oBAC5C,QAAQ;oBACR,WAAW,CAAC,QAAQ,GAAG,YAAY,CAAC,KAAK,CAAC,QAAQ,CAAC;oBACnD,WAAW,CAAC,WAAW,GAAG,YAAY,CAAC,KAAK,CAAC,WAAW,CAAC;oBACzD,WAAW,CAAC,QAAQ,GAAG,YAAY,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,YAAY;iBACjE;gBAED,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,IAAI,CAAC,gBAAgB,EAAE,WAAW,CAAC,CAAC;gBACjE,MAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;gBAE3B,IAAI,IAAI,CAAC,OAAO,EAAE;oBAChB,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;oBAC5B,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;iBACvB;qBAAM;oBACL,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,IAAI,MAAM,CAAC,CAAC;iBACvC;aACF;YAAC,OAAO,KAAK,EAAE;gBACd,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;gBAC9B,OAAO,CAAC,KAAK,CAAC,iBAAiB,EAAE,KAAK,CAAC,CAAC;aACzC;oBAAS;gBACR,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;aACvB;QACH,CAAC,CAAC;QAEF,MAAM,SAAS,GAAG,GAAG,EAAE;YACrB,oBAAoB,CAAC,KAAK,GAAG,IAAI,CAAC;QACpC,CAAC,CAAC;QAEF,MAAM,WAAW,GAAG,GAAG,EAAE;YACvB,oBAAoB,CAAC,KAAK,GAAG,IAAI,CAAC;QACpC,CAAC,CAAC;QAEF,kBAAkB;QAClB,KAAK,CACH,GAAG,EAAE,CAAC,YAAY,CAAC,KAAK,CAAC,QAAQ,EACjC,GAAG,EAAE;YACH,2BAA2B;YAC3B,IAAI,YAAY,CAAC,KAAK,CAAC,eAAe,IAAI,eAAe,CAAC,KAAK,EAAE;gBAC/D,eAAe,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC;aAC1D;QACH,CAAC,CACF,CAAC;QAEF,cAAc;QACd,SAAS,CAAC,GAAG,EAAE;YACb,oBAAoB,EAAE,CAAC;QACzB,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,YAAY;YACZ,eAAe;YACf,aAAa;YACb,OAAO;YACP,UAAU;YACV,gBAAgB;YAChB,oBAAoB;YACpB,oBAAoB;YACpB,cAAc;YACd,SAAS;YACT,WAAW;SACZ,CAAC;IACJ,CAAC;CACF,CAAC,CAAC", "sourcesContent": ["\nimport { defineComponent, ref, onMounted, watch } from 'vue';\nimport { \n  UserOutlined, \n  LockOutlined, \n  PhoneOutlined, \n  MailOutlined,\n  IdcardOutlined,\n  CarOutlined,\n  ShopOutlined,\n  EnvironmentOutlined\n} from '@ant-design/icons-vue';\nimport { message } from 'ant-design-vue';\nimport axios from 'axios';\nimport { useRouter } from 'vue-router';\n\nexport default defineComponent({\n  name: 'Register',\n  components: {\n    UserOutlined,\n    LockOutlined,\n    PhoneOutlined,\n    MailOutlined,\n    IdcardOutlined,\n    CarOutlined,\n    ShopOutlined,\n    EnvironmentOutlined\n  },\n  setup() {\n    const router = useRouter();\n    const loading = ref(false);\n    const agreeTerms = ref(false);\n    const userAgreementVisible = ref(false);\n    const privacyPolicyVisible = ref(false);\n    const registerFormRef = ref();\n\n    const registerForm = ref({\n      username: '',\n      shopName: '',\n      realName: '',\n      phone: '',\n      email: '',\n      shopAddress: '',\n      password: '',\n      confirmPassword: '',\n      userType: 1 // 默认选择车主\n    });\n\n    // 安全设置\n    const securitySettings = ref({\n      minPasswordLength: 6,\n      requireComplexPassword: false\n    });\n    \n    // 加载安全设置\n    const loadSecuritySettings = async () => {\n      try {\n        const response = await axios.get('/api/admin/security-settings');\n        if (response.data.success) {\n          securitySettings.value = response.data.content;\n        }\n      } catch (error) {\n        console.error('加载安全设置失败:', error);\n      }\n    };\n\n    \n    // 创建验证规则\n    const createValidationRules = () => ({\n      realName: [\n        { required: true, message: '请输入真实姓名', trigger: 'blur' }\n      ],\n      phone: [\n        { required: true, message: '请输入手机号', trigger: 'blur' },\n        { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }\n      ],\n      email: [\n        { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }\n      ],\n      userType: [\n        { required: true, message: '请选择用户类型', trigger: 'change' }\n      ],\n      password: [\n        { required: true, message: '请输入密码', trigger: 'blur' },\n        { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }\n      ],\n        confirmPassword: [\n          { required: true, message: '请确认密码', trigger: 'blur' },\n          {\n            validator: (_rule: any, value: string) => {\n              if (!value) return Promise.resolve();\n              if (value !== registerForm.value.password) {\n                return Promise.reject(new Error('两次输入的密码不一致'));\n              }\n              return Promise.resolve();\n            },\n            trigger: 'blur'\n          }\n        ],\n      username: [\n        { required: true, message: '请输入用户名', trigger: 'blur' },\n        { min: 3, max: 20, message: '用户名长度为3-20个字符', trigger: 'blur' }\n      ],\n      shopName: [\n        { required: true, message: '请输入维修店名', trigger: 'blur' },\n        { min: 2, max: 50, message: '维修店名长度为2-50个字符', trigger: 'blur' }\n      ],\n      shopAddress: [\n        { required: true, message: '请输入地铺地址', trigger: 'blur' },\n        { min: 5, max: 200, message: '地址长度为5-200个字符', trigger: 'blur' }\n      ]\n    });\n\n    const registerRules = ref(createValidationRules());\n    \n    const handleRegister = async () => {\n      if (!agreeTerms.value) {\n        message.warning('请先同意用户协议和隐私政策');\n        return;\n      }\n      \n      loading.value = true;\n      try {\n        // 根据用户类型准备不同的数据\n        const requestData: any = {\n          realName: registerForm.value.realName,\n          phone: registerForm.value.phone,\n          email: registerForm.value.email,\n          password: registerForm.value.password,\n          userType: registerForm.value.userType\n        };\n\n        if (registerForm.value.userType === 1) {\n          // 车主用户\n          requestData.username = registerForm.value.username;\n        } else if (registerForm.value.userType === 2) {\n          // 维修店用户\n          requestData.shopName = registerForm.value.shopName;\n          requestData.shopAddress = registerForm.value.shopAddress;\n          requestData.username = registerForm.value.shopName; // 使用店名作为用户名\n        }\n\n        const response = await axios.post('/auth/register', requestData);\n        const data = response.data;\n        \n        if (data.success) {\n          message.success('注册成功，请登录');\n          router.push('/login');\n        } else {\n          message.error(data.message || '注册失败');\n        }\n      } catch (error) {\n        message.error('注册失败，请检查网络连接');\n        console.error('Register error:', error);\n      } finally {\n        loading.value = false;\n      }\n    };\n    \n    const showTerms = () => {\n      userAgreementVisible.value = true;\n    };\n    \n    const showPrivacy = () => {\n      privacyPolicyVisible.value = true;\n    };\n\n    // 监听密码变化，重新验证确认密码\n    watch(\n      () => registerForm.value.password,\n      () => {\n        // 当密码改变时，如果确认密码已有值，则清除验证状态\n        if (registerForm.value.confirmPassword && registerFormRef.value) {\n          registerFormRef.value.clearValidate(['confirmPassword']);\n        }\n      }\n    );\n\n    // 组件挂载时加载安全设置\n    onMounted(() => {\n      loadSecuritySettings();\n    });\n\n    return {\n      registerForm,\n      registerFormRef,\n      registerRules,\n      loading,\n      agreeTerms,\n      securitySettings,\n      userAgreementVisible,\n      privacyPolicyVisible,\n      handleRegister,\n      showTerms,\n      showPrivacy\n    };\n  }\n});\n"]}]}