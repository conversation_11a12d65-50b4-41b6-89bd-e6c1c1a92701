<template>
  <div class="admin-dashboard">
    <div class="dashboard-header">
      <div class="welcome-section">
        <h1>系统管理中心</h1>
        <p>{{ userInfo.realName || userInfo.username }} - 汽车维修服务平台管理员</p>
      </div>
      <div class="user-actions">
        <a-button type="primary" @click="goToSystemSettings">系统设置</a-button>
        <a-button @click="logout">退出登录</a-button>
      </div>
    </div>

    <div class="dashboard-content">
      <!-- 平台统计概览 -->
      <a-row :gutter="24" class="stats-overview">
        <a-col :span="6">
          <a-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon users">
                <UserOutlined />
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ platformStats.totalUsers }}</div>
                <div class="stat-label">注册用户</div>
                <div class="stat-detail">
                  车主: {{ platformStats.ownerCount }} | 维修店: {{ platformStats.shopCount }}
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon orders">
                <FileTextOutlined />
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ platformStats.totalOrders }}</div>
                <div class="stat-label">总订单数</div>
                <div class="stat-detail">今日: {{ platformStats.todayOrders }}</div>
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon revenue">
                <DollarOutlined />
              </div>
              <div class="stat-info">
                <div class="stat-number">¥{{ platformStats.totalRevenue }}</div>
                <div class="stat-label">平台收入</div>
                <div class="stat-detail">本月: ¥{{ platformStats.monthlyRevenue }}</div>
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card">
            <div class="stat-content">
              <div class="stat-icon services">
                <ToolOutlined />
              </div>
              <div class="stat-info">
                <div class="stat-number">{{ platformStats.totalServices }}</div>
                <div class="stat-label">服务项目</div>
                <div class="stat-detail">活跃: {{ platformStats.activeServices }}</div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <a-row :gutter="24" style="margin-top: 24px;">
        <!-- 管理功能 -->
        <a-col :span="16">
          <a-card title="管理功能">
            <a-row :gutter="16">
              <a-col :span="8">
                <div class="admin-function-card" @click="goToUserManagement">
                  <TeamOutlined class="function-icon" />
                  <h3>用户管理</h3>
                  <p>管理车主和维修店用户账户</p>
                  <div class="function-stats">
                    <span>总用户: {{ platformStats.totalUsers }}</span>
                  </div>
                </div>
              </a-col>
              <a-col :span="8">
                <div class="admin-function-card" @click="goToOrderManagement">
                  <ShoppingCartOutlined class="function-icon" />
                  <h3>订单管理</h3>
                  <p>查看和管理所有维修订单</p>
                  <div class="function-stats">
                    <span>待处理: {{ platformStats.pendingOrders }}</span>
                  </div>
                </div>
              </a-col>
              <a-col :span="8">
                <div class="admin-function-card" @click="goToServiceManagement">
                  <SettingOutlined class="function-icon" />
                  <h3>服务管理</h3>
                  <p>管理平台服务项目</p>
                  <div class="function-stats">
                    <span>服务项目: {{ platformStats.totalServices }}</span>
                  </div>
                </div>
              </a-col>
              <a-col :span="8">
                <div class="admin-function-card" @click="goToCategoryManagement">
                  <AppstoreOutlined class="function-icon" />
                  <h3>分类管理</h3>
                  <p>管理服务分类，支持多级分类结构</p>
                  <div class="function-stats">
                    <span>分类管理</span>
                  </div>
                </div>
              </a-col>
              <a-col :span="8">
                <div class="admin-function-card" @click="goToReports">
                  <BarChartOutlined class="function-icon" />
                  <h3>数据报告</h3>
                  <p>查看平台运营数据和统计</p>
                  <div class="function-stats">
                    <span>数据分析</span>
                  </div>
                </div>
              </a-col>
              <a-col :span="8">
                <div class="admin-function-card" @click="goToSystemSettings">
                  <ControlOutlined class="function-icon" />
                  <h3>系统设置</h3>
                  <p>平台参数和系统配置管理</p>
                  <div class="function-stats">
                    <span>系统配置</span>
                  </div>
                </div>
              </a-col>
              <a-col :span="8">
                <div class="admin-function-card" @click="goToLogs">
                  <FileSearchOutlined class="function-icon" />
                  <h3>系统日志</h3>
                  <p>查看系统操作和错误日志</p>
                  <div class="function-stats">
                    <span>日志监控</span>
                  </div>
                </div>
              </a-col>
            </a-row>
          </a-card>
        </a-col>

        <!-- 最新动态 -->
        <a-col :span="8">
          <a-card title="最新动态" extra="查看全部">
            <a-timeline>
              <a-timeline-item v-for="activity in recentActivities" :key="activity.id">
                <div class="activity-item">
                  <div class="activity-title">{{ activity.title }}</div>
                  <div class="activity-time">{{ activity.time }}</div>
                  <div class="activity-desc">{{ activity.description }}</div>
                </div>
              </a-timeline-item>
            </a-timeline>
          </a-card>
        </a-col>
      </a-row>

      <!-- 平台数据趋势 -->
      <a-row :gutter="24" style="margin-top: 24px;">
        <a-col :span="12">
          <a-card title="用户增长趋势">
            <div class="trend-chart">
              <div class="trend-item" v-for="item in userTrend" :key="item.date">
                <div class="trend-date">{{ item.date }}</div>
                <div class="trend-bar">
                  <div class="trend-bar-fill" :style="{ width: (item.count / 50) * 100 + '%' }"></div>
                </div>
                <div class="trend-count">{{ item.count }}</div>
              </div>
            </div>
          </a-card>
        </a-col>
        <a-col :span="12">
          <a-card title="订单处理情况">
            <div class="order-status-chart">
              <div class="status-item">
                <div class="status-color pending"></div>
                <span>待处理: {{ orderStatusStats.pending }}</span>
              </div>
              <div class="status-item">
                <div class="status-color processing"></div>
                <span>处理中: {{ orderStatusStats.processing }}</span>
              </div>
              <div class="status-item">
                <div class="status-color completed"></div>
                <span>已完成: {{ orderStatusStats.completed }}</span>
              </div>
              <div class="status-item">
                <div class="status-color cancelled"></div>
                <span>已取消: {{ orderStatusStats.cancelled }}</span>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { clearUserSession, safeNavigate } from '../../utils/auth';
import {
  UserOutlined,
  FileTextOutlined,
  DollarOutlined,
  ToolOutlined,
  TeamOutlined,
  ShoppingCartOutlined,
  SettingOutlined,
  BarChartOutlined,
  ControlOutlined,
  FileSearchOutlined,
  AppstoreOutlined
} from '@ant-design/icons-vue';

export default defineComponent({
  name: 'AdminDashboard',
  components: {
    UserOutlined,
    FileTextOutlined,
    DollarOutlined,
    ToolOutlined,
    TeamOutlined,
    ShoppingCartOutlined,
    SettingOutlined,
    BarChartOutlined,
    ControlOutlined,
    FileSearchOutlined,
    AppstoreOutlined
  },
  setup() {
    const router = useRouter();
    
    const userInfo = ref<any>({});
    const platformStats = ref({
      totalUsers: 0,
      ownerCount: 0,
      shopCount: 0,
      totalOrders: 0,
      todayOrders: 0,
      totalRevenue: 0,
      monthlyRevenue: 0,
      totalServices: 0,
      activeServices: 0,
      pendingOrders: 0
    });

    const recentActivities = ref<any[]>([]);
    const userTrend = ref<any[]>([]);
    const orderStatusStats = ref({
      pending: 0,
      processing: 0,
      completed: 0,
      cancelled: 0
    });

    const goToUserManagement = () => {
      safeNavigate(router, '/admin/users');
    };

    const goToOrderManagement = () => {
      safeNavigate(router, '/admin/orders');
    };

    const goToServiceManagement = () => {
      safeNavigate(router, '/admin/services');
    };

    const goToCategoryManagement = () => {
      safeNavigate(router, '/admin/categories');
    };

    const goToReports = () => {
      safeNavigate(router, '/admin/reports');
    };

    const goToSystemSettings = () => {
      safeNavigate(router, '/admin/settings');
    };

    const goToLogs = () => {
      safeNavigate(router, '/admin/logs');
    };

    const logout = () => {
      clearUserSession();
      message.success('退出登录成功');
      // 强制刷新到首页确保状态完全清除
      window.location.href = '/';
    };

    const loadUserInfo = () => {
      const userInfoStr = localStorage.getItem('userInfo');
      if (userInfoStr) {
        try {
          userInfo.value = JSON.parse(userInfoStr);
        } catch (error) {
          console.error('Parse user info error:', error);
        }
      }
    };

    const loadPlatformStats = () => {
      // TODO: 实现平台统计数据API
      platformStats.value = {
        totalUsers: 1248,
        ownerCount: 956,
        shopCount: 292,
        totalOrders: 3456,
        todayOrders: 23,
        totalRevenue: 856300,
        monthlyRevenue: 125600,
        totalServices: 127,
        activeServices: 98,
        pendingOrders: 18
      };
    };

    const loadRecentActivities = () => {
      // TODO: 实现最新动态API
      recentActivities.value = [
        {
          id: 1,
          title: '新用户注册',
          time: '5分钟前',
          description: '车主张先生注册了账户'
        },
        {
          id: 2,
          title: '订单完成',
          time: '15分钟前',
          description: '维修订单 #12345 已完成'
        },
        {
          id: 3,
          title: '新维修店加入',
          time: '1小时前',
          description: '汽车之家维修店通过审核'
        },
        {
          id: 4,
          title: '服务更新',
          time: '2小时前',
          description: '更新了机油更换服务价格'
        }
      ];
    };

    const loadUserTrend = () => {
      // TODO: 实现用户增长趋势API
      userTrend.value = [
        { date: '今日', count: 23 },
        { date: '昨日', count: 45 },
        { date: '3日前', count: 32 },
        { date: '4日前', count: 28 },
        { date: '5日前', count: 41 },
        { date: '6日前', count: 35 },
        { date: '7日前', count: 38 }
      ];
    };

    const loadOrderStatusStats = () => {
      // TODO: 实现订单状态统计API
      orderStatusStats.value = {
        pending: 18,
        processing: 25,
        completed: 156,
        cancelled: 8
      };
    };

    onMounted(() => {
      loadUserInfo();
      loadPlatformStats();
      loadRecentActivities();
      loadUserTrend();
      loadOrderStatusStats();
    });

    return {
      userInfo,
      platformStats,
      recentActivities,
      userTrend,
      orderStatusStats,
      goToUserManagement,
      goToOrderManagement,
      goToServiceManagement,
      goToCategoryManagement,
      goToReports,
      goToSystemSettings,
      goToLogs,
      logout
    };
  }
});
</script>

<style scoped>
.admin-dashboard {
  background: #f0f2f5;
  padding: 24px;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.welcome-section h1 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 28px;
}

.welcome-section p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.user-actions .ant-btn {
  margin-left: 12px;
}

.stats-overview .stat-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 8px 0;
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stat-icon.users { background: #1890ff; }
.stat-icon.orders { background: #52c41a; }
.stat-icon.revenue { background: #722ed1; }
.stat-icon.services { background: #fa8c16; }

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.stat-label {
  color: #666;
  font-size: 14px;
  margin-bottom: 2px;
}

.stat-detail {
  color: #999;
  font-size: 12px;
}

.admin-function-card {
  padding: 24px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  height: 160px;
  margin-bottom: 16px;
}

.admin-function-card:hover {
  border-color: #1890ff;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
  transform: translateY(-2px);
}

.admin-function-card .function-icon {
  font-size: 36px;
  color: #1890ff;
  margin-bottom: 12px;
}

.admin-function-card h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  color: #333;
}

.admin-function-card p {
  margin: 0 0 12px 0;
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.function-stats {
  font-size: 12px;
  color: #1890ff;
  font-weight: 500;
}

.activity-item {
  margin-bottom: 8px;
}

.activity-title {
  font-weight: 500;
  color: #333;
  margin-bottom: 2px;
}

.activity-time {
  font-size: 12px;
  color: #999;
  margin-bottom: 4px;
}

.activity-desc {
  font-size: 13px;
  color: #666;
}

.trend-chart {
  padding: 16px 0;
}

.trend-item {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.trend-date {
  width: 60px;
  font-size: 12px;
  color: #666;
}

.trend-bar {
  flex: 1;
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  margin: 0 12px;
  position: relative;
}

.trend-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #1890ff, #52c41a);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.trend-count {
  width: 40px;
  text-align: right;
  font-size: 12px;
  color: #333;
  font-weight: 500;
}

.order-status-chart {
  padding: 16px 0;
}

.status-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  font-size: 14px;
  color: #333;
}

.status-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 12px;
}

.status-color.pending { background: #fa8c16; }
.status-color.processing { background: #1890ff; }
.status-color.completed { background: #52c41a; }
.status-color.cancelled { background: #ff4d4f; }
</style>
