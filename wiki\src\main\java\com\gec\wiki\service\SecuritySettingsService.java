package com.gec.wiki.service;

import com.gec.wiki.domain.SecuritySettings;

/**
 * 安全设置服务接口
 */
public interface SecuritySettingsService {
    
    /**
     * 获取安全设置
     * @return 安全设置对象，如果不存在则返回默认设置
     */
    SecuritySettings getSecuritySettings();
    
    /**
     * 保存或更新安全设置
     * @param securitySettings 安全设置对象
     * @return 是否保存成功
     */
    boolean saveSecuritySettings(SecuritySettings securitySettings);
    
    /**
     * 获取最大登录失败次数
     * @return 最大登录失败次数
     */
    int getMaxLoginFailCount();
    
    /**
     * 获取锁定持续时间（分钟）
     * @return 锁定持续时间
     */
    int getLockDurationMinutes();
    
    /**
     * 获取密码最小长度
     * @return 密码最小长度
     */
    int getMinPasswordLength();
    
    /**
     * 是否要求复杂密码
     * @return 是否要求复杂密码
     */
    boolean isRequireComplexPassword();
    
    /**
     * 验证密码是否符合要求
     * @param password 密码
     * @return 验证结果消息，null表示验证通过
     */
    String validatePassword(String password);
}
