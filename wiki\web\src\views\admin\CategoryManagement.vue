<template>
  <div class="category-management">
    <div class="page-header">
      <div class="header-content">
        <h1>分类管理</h1>
        <p>管理汽车维修服务分类，支持多级分类层级结构</p>
      </div>
      <div class="header-actions">
        <a-button type="primary" @click="showAddCategoryModal(null)">
          <PlusOutlined />
          添加一级分类
        </a-button>
        <a-button @click="refreshCategoryTree">
          <ReloadOutlined />
          刷新
        </a-button>
      </div>
    </div>

    <div class="page-content">
      <a-card>
        <div class="search-section">
          <a-input-search
            v-model:value="searchKeyword"
            placeholder="搜索分类名称"
            style="width: 300px"
            @search="onSearch"
            @change="onSearchChange"
          />
        </div>

        <div class="tree-section">
          <a-spin :spinning="loading">
            <a-tree
              v-if="filteredTreeData.length > 0"
              :tree-data="filteredTreeData"
              :expanded-keys="expandedKeys"
              :selected-keys="selectedKeys"
              show-line
              :show-icon="false"
              @expand="onExpand"
              @select="onSelect"
            >
              <template #title="nodeData">
                <div class="tree-node">
                  <div class="node-content">
                    <span class="node-title">{{ nodeData.name }}</span>
                    <a-tag v-if="nodeData.parent === 0 || nodeData.parent === null" color="blue" size="small">一级</a-tag>
                    <a-tag v-else color="green" size="small">二级</a-tag>
                    <span class="node-sort">排序: {{ nodeData.sort }}</span>
                  </div>
                  <div class="node-actions">
                    <a-button
                      type="text"
                      size="small"
                      @click.stop="showAddCategoryModal(nodeData.id)"
                      title="添加子分类"
                    >
                      <PlusOutlined />
                    </a-button>
                    <a-button
                      type="text"
                      size="small"
                      @click.stop="showEditCategoryModal({ id: nodeData.id, name: nodeData.name, parent: nodeData.parent, sort: nodeData.sort })"
                      title="编辑"
                    >
                      <EditOutlined />
                    </a-button>
                    <a-button
                      type="text"
                      size="small"
                      danger
                      @click.stop="handleDeleteCategory(nodeData.id, nodeData.name, nodeData.children && nodeData.children.length > 0)"
                      title="删除"
                    >
                      <DeleteOutlined />
                    </a-button>
                  </div>
                </div>
              </template>
            </a-tree>
            <a-empty v-else description="暂无分类数据" />
          </a-spin>
        </div>
      </a-card>
    </div>

    <!-- 添加/编辑分类对话框 -->
    <a-modal
      :visible="categoryModalVisible"
      :title="categoryModalTitle"
      :confirm-loading="categoryModalLoading"
      @ok="handleSaveCategory"
      @cancel="handleCancelCategoryModal"
      width="500px"
    >
      <a-form
        ref="categoryFormRef"
        :model="categoryForm"
        :rules="categoryFormRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="分类名称" name="name">
          <a-input v-model:value="categoryForm.name" placeholder="请输入分类名称" />
        </a-form-item>
        
        <a-form-item label="父级分类" name="parent">
          <a-select
            v-model:value="categoryForm.parent"
            placeholder="选择父级分类（不选择则为一级分类）"
            allow-clear
            :options="parentCategoryOptions"
          />
        </a-form-item>
        
        <a-form-item label="排序" name="sort">
          <a-input-number
            v-model:value="categoryForm.sort"
            :min="1"
            :max="999"
            placeholder="排序号"
            style="width: 100%"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, computed, reactive } from 'vue';
import { message, Modal } from 'ant-design-vue';
import axios from 'axios';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined
} from '@ant-design/icons-vue';

interface CategoryData {
  id: number;
  name: string;
  parent: number;
  sort: number;
  children?: CategoryData[];
  level?: number;
}

interface TreeNodeData {
  key: string;
  title: string;
  id: number;
  name: string;
  parent: number;
  sort: number;
  children?: TreeNodeData[];
}

export default defineComponent({
  name: 'CategoryManagement',
  components: {
    PlusOutlined,
    EditOutlined,
    DeleteOutlined,
    ReloadOutlined
  },
  setup() {
    const loading = ref(false);
    const searchKeyword = ref('');
    const categoryData = ref<CategoryData[]>([]);
    const expandedKeys = ref<string[]>([]);
    const selectedKeys = ref<string[]>([]);

    // 分类对话框相关
    const categoryModalVisible = ref(false);
    const categoryModalLoading = ref(false);
    const categoryModalTitle = ref('');
    const categoryFormRef = ref();
    const categoryForm = reactive({
      id: null as number | null,
      name: '',
      parent: null as number | null,
      sort: 1
    });

    const categoryFormRules = {
      name: [
        { required: true, message: '请输入分类名称', trigger: 'blur' },
        { min: 1, max: 50, message: '分类名称长度在1-50个字符', trigger: 'blur' }
      ]
    };

    // 转换数据为树形结构
    const convertToTreeData = (data: CategoryData[]): TreeNodeData[] => {
      return data.map(item => ({
        key: item.id.toString(),
        title: item.name,
        id: item.id,
        name: item.name,
        parent: item.parent,
        sort: item.sort,
        children: item.children ? convertToTreeData(item.children) : undefined
      }));
    };

    // 树形数据
    const treeData = computed(() => convertToTreeData(categoryData.value));

    // 过滤树形数据（支持搜索）
    const filteredTreeData = computed(() => {
      if (!searchKeyword.value.trim()) {
        return treeData.value;
      }
      return filterTreeData(treeData.value, searchKeyword.value.toLowerCase());
    });

    // 父级分类选项
    const parentCategoryOptions = computed(() => {
      const options: { label: string; value: number }[] = [];
      const flattenCategories = (categories: CategoryData[], level = 0) => {
        categories.forEach(category => {
          // 只显示一级分类作为父级选项（限制为二级分类系统）
          if (level === 0) {
            options.push({
              label: category.name,
              value: category.id
            });
          }
        });
      };
      flattenCategories(categoryData.value);
      return options;
    });

    // 递归过滤树形数据
    const filterTreeData = (data: TreeNodeData[], keyword: string): TreeNodeData[] => {
      return data.filter(item => {
        const matchesKeyword = item.name.toLowerCase().includes(keyword);
        const hasMatchingChildren = item.children && filterTreeData(item.children, keyword).length > 0;
        
        if (matchesKeyword || hasMatchingChildren) {
          return {
            ...item,
            children: item.children ? filterTreeData(item.children, keyword) : undefined
          };
        }
        return false;
      }).map(item => ({
        ...item,
        children: item.children ? filterTreeData(item.children, keyword) : undefined
      }));
    };

    // 加载分类树形数据
    const loadCategoryTree = async () => {
      loading.value = true;
      try {
        const response = await axios.get('/category/tree');
        if (response.data.success) {
          categoryData.value = response.data.content || [];
          // 默认展开所有一级分类
          const firstLevelKeys = categoryData.value.map(item => item.id.toString());
          expandedKeys.value = firstLevelKeys;
        } else {
          message.error(response.data.message || '获取分类数据失败');
        }
      } catch (error) {
        console.error('Load category tree error:', error);
        message.error('获取分类数据失败');
      } finally {
        loading.value = false;
      }
    };

    // 刷新分类树
    const refreshCategoryTree = () => {
      loadCategoryTree();
    };

    // 展开/收起节点
    const onExpand = (keys: string[]) => {
      expandedKeys.value = keys;
    };

    // 选择节点
    const onSelect = (keys: string[]) => {
      selectedKeys.value = keys;
    };

    // 搜索
    const onSearch = () => {
      // 搜索时展开所有匹配的节点
      if (searchKeyword.value.trim()) {
        const allKeys = getAllNodeKeys(treeData.value);
        expandedKeys.value = allKeys;
      }
    };

    // 搜索框内容变化
    const onSearchChange = () => {
      if (!searchKeyword.value.trim()) {
        // 清空搜索时恢复默认展开状态
        const firstLevelKeys = categoryData.value.map(item => item.id.toString());
        expandedKeys.value = firstLevelKeys;
      }
    };

    // 获取所有节点key
    const getAllNodeKeys = (data: TreeNodeData[]): string[] => {
      const keys: string[] = [];
      const traverse = (nodes: TreeNodeData[]) => {
        nodes.forEach(node => {
          keys.push(node.key);
          if (node.children) {
            traverse(node.children);
          }
        });
      };
      traverse(data);
      return keys;
    };

    // 显示添加分类对话框
    const showAddCategoryModal = (parentId: number | null) => {
      categoryModalTitle.value = parentId ? '添加子分类' : '添加一级分类';
      categoryForm.id = null;
      categoryForm.name = '';
      categoryForm.parent = parentId;
      categoryForm.sort = 1;
      categoryModalVisible.value = true;
    };

    // 显示编辑分类对话框
    const showEditCategoryModal = (category: any) => {
      categoryModalTitle.value = '编辑分类';
      categoryForm.id = category.id;
      categoryForm.name = category.name;
      categoryForm.parent = category.parent === 0 ? null : category.parent;
      categoryForm.sort = category.sort;
      categoryModalVisible.value = true;
    };

    // 取消分类对话框
    const handleCancelCategoryModal = () => {
      categoryModalVisible.value = false;
      if (categoryFormRef.value) {
        categoryFormRef.value.resetFields();
      }
    };

    // 保存分类
    const handleSaveCategory = async () => {
      try {
        if (categoryFormRef.value) {
          await categoryFormRef.value.validate();
        }
        categoryModalLoading.value = true;

        const saveData = {
          id: categoryForm.id,
          name: categoryForm.name.trim(),
          parent: categoryForm.parent || 0,
          sort: categoryForm.sort
        };

        const response = await axios.post('/category/save', saveData);
        if (response.data.success) {
          message.success(response.data.message || '保存成功');
          categoryModalVisible.value = false;
          await loadCategoryTree();
        } else {
          message.error(response.data.message || '保存失败');
        }
      } catch (error) {
        console.error('Save category error:', error);
        message.error('保存失败');
      } finally {
        categoryModalLoading.value = false;
      }
    };

    // 删除分类
    const handleDeleteCategory = (id: number, name: string, hasChildren: boolean) => {
      if (hasChildren) {
        message.warning('该分类下还有子分类，请先删除子分类');
        return;
      }

      Modal.confirm({
        title: '确认删除',
        content: `确定要删除分类"${name}"吗？此操作不可恢复。`,
        okText: '确定',
        cancelText: '取消',
        okType: 'danger',
        onOk: async () => {
          try {
            const response = await axios.get(`/category/remove?id=${id}`);
            if (response.data.success) {
              message.success('删除成功');
              await loadCategoryTree();
            } else {
              message.error(response.data.message || '删除失败');
            }
          } catch (error) {
            console.error('Delete category error:', error);
            message.error('删除失败');
          }
        }
      });
    };

    onMounted(() => {
      loadCategoryTree();
    });

    return {
      loading,
      searchKeyword,
      categoryData,
      treeData,
      filteredTreeData,
      expandedKeys,
      selectedKeys,
      categoryModalVisible,
      categoryModalLoading,
      categoryModalTitle,
      categoryFormRef,
      categoryForm,
      categoryFormRules,
      parentCategoryOptions,
      refreshCategoryTree,
      onExpand,
      onSelect,
      onSearch,
      onSearchChange,
      showAddCategoryModal,
      showEditCategoryModal,
      handleCancelCategoryModal,
      handleSaveCategory,
      handleDeleteCategory
    };
  }
});
</script>

<style scoped>
.category-management {
  background: #f0f2f5;
  padding: 24px;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content h1 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 28px;
}

.header-content p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.header-actions .ant-btn {
  margin-left: 12px;
}

.page-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-section {
  padding: 24px 24px 0 24px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 24px;
}

.tree-section {
  padding: 0 24px 24px 24px;
  min-height: 400px;
}

.tree-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
  width: 100%;
}

.node-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.node-title {
  font-size: 14px;
  font-weight: 500;
  margin-right: 8px;
  color: #333;
}

.node-sort {
  font-size: 12px;
  color: #999;
  margin-left: 8px;
}

.node-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.tree-node:hover .node-actions {
  opacity: 1;
}

.node-actions .ant-btn {
  padding: 4px;
  border: none;
  box-shadow: none;
}

/* 树形组件样式覆盖 */
:deep(.ant-tree .ant-tree-node-content-wrapper) {
  width: 100%;
}

:deep(.ant-tree .ant-tree-title) {
  width: 100%;
}

:deep(.ant-tree-switcher) {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 标签样式 */
.ant-tag {
  margin-left: 8px;
  font-size: 11px;
  padding: 1px 6px;
  line-height: 16px;
}

/* 响应式 */
@media (max-width: 768px) {
  .category-management {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .header-actions {
    align-self: stretch;
  }
  
  .header-actions .ant-btn {
    margin-left: 0;
    margin-right: 12px;
  }
  
  .search-section {
    padding: 16px;
  }
  
  .tree-section {
    padding: 0 16px 16px 16px;
  }
}
</style>
