package com.gec.wiki.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.gec.wiki.pojo.Customer;
import com.gec.wiki.pojo.req.CustomerQueryReq;
import com.gec.wiki.pojo.req.CustomerSaveReq;
import com.gec.wiki.pojo.resp.CommonResp;
import com.gec.wiki.pojo.resp.PageResp;
import com.gec.wiki.service.CustomerService;
import com.gec.wiki.utils.CopyUtil;
import com.gec.wiki.utils.SnowFlake;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

@RestController
@RequestMapping("/customer")
public class CustomerController {

    @Autowired
    private CustomerService customerService;

    private static final Logger LOG = LoggerFactory.getLogger(CustomerController.class);

    @GetMapping("/getCustomerListByPage")
    public CommonResp getCustomerListByPage(CustomerQueryReq customerReq){
        Page<Customer> page = new Page<>(customerReq.getPage(),customerReq.getSize());

        QueryWrapper<Customer> wrapper=new QueryWrapper<>();
        if (!ObjectUtils.isEmpty(customerReq.getName())){
            wrapper.like("name",customerReq.getName());
        }
        if (!ObjectUtils.isEmpty(customerReq.getPhone())){
            wrapper.like("phone",customerReq.getPhone());
        }
        if (customerReq.getLevel()!=null){
            wrapper.eq("level",customerReq.getLevel());
        }

        page = customerService.page(page,wrapper);
        List<Customer> list = page.getRecords();

        LOG.info("总行数：{}",page.getTotal()+"");
        LOG.info("总页数：{}",page.getPages()+"");

        List<Customer> customerResps =CopyUtil.copyList(list, Customer.class);

        //分页处理
        PageResp<Customer> pageResp=new PageResp<>();
        pageResp.setTotal(page.getTotal());
        pageResp.setList(customerResps);

        CommonResp< PageResp<Customer> > resp = new CommonResp<>();
        resp.setContent(pageResp);
        return resp;
    }

    @PostMapping("/save")
    public CommonResp save(@RequestBody CustomerSaveReq req){
        CommonResp resp=new CommonResp();
        LOG.info("收到的保存请求：ID = {}, 姓名 = {}", req.getId(), req.getName());
        
        Customer customer=CopyUtil.copy(req,Customer.class);
        
        if (req.getId() == null){
            // 新增
            SnowFlake snowFlake=new SnowFlake();
            customer.setId(snowFlake.nextId());
            customer.setCreateTime(LocalDateTime.now());
            customer.setUpdateTime(LocalDateTime.now());
            if (customer.getLevel() == null) {
                customer.setLevel(1); // 默认普通客户
            }
            customerService.save(customer);
            resp.setMessage("添加成功");
        }else {
            // 修改
            LOG.info("更新ID = {}", customer.getId());
            customer.setUpdateTime(LocalDateTime.now());
            customerService.updateById(customer);
            resp.setMessage("修改成功");
        }
        return resp;
    }
    
    @GetMapping("/remove")
    public CommonResp remove(long id){
        customerService.removeById(id);
        CommonResp<Object> resp = new CommonResp<>();
        resp.setMessage("删除成功");
        return resp;
    }

    @GetMapping("/getAllCustomers")
    public CommonResp getAllCustomers(){
        List<Customer> list = customerService.list();
        CommonResp<List<Customer>> resp = new CommonResp<>();
        resp.setContent(list);
        return resp;
    }
}
