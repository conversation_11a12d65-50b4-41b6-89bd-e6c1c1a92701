-- 修复分类数据脚本
-- 确保分类数据正确插入到数据库中

USE car_service;

-- 清空现有分类数据（如果存在）
DELETE FROM category;

-- 重新插入分类数据
INSERT INTO category (id, parent, name, sort) VALUES 
(100, 0, '常规保养', 1),
(101, 100, '机油保养', 1),
(102, 100, '轮胎保养', 2),
(103, 100, '制动系统', 3),
(200, 0, '发动机维修', 2),
(201, 200, '发动机检修', 1),
(202, 200, '冷却系统', 2),
(300, 0, '电气系统', 3),
(301, 300, '电瓶维护', 1),
(302, 300, '空调系统', 2),
(400, 0, '底盘系统', 4),
(401, 400, '悬挂系统', 1),
(402, 400, '转向系统', 2),
(500, 0, '外观内饰', 5),
(501, 500, '车身美容', 1),
(502, 500, '内饰清洁', 2);

-- 验证数据插入
SELECT * FROM category ORDER BY sort, id;

-- 查看分层结构
SELECT 
    c1.id as category1_id, c1.name as category1_name,
    c2.id as category2_id, c2.name as category2_name
FROM category c1
LEFT JOIN category c2 ON c2.parent = c1.id
WHERE c1.parent = 0
ORDER BY c1.sort, c2.sort;
