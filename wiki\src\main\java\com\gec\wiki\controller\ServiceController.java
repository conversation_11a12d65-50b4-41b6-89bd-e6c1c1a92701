package com.gec.wiki.controller;

import com.gec.wiki.pojo.Service;
import com.gec.wiki.pojo.req.ServiceQueryReq;
import com.gec.wiki.pojo.req.ServiceSaveReq;
import com.gec.wiki.pojo.resp.CommonResp;
import com.gec.wiki.pojo.resp.PageResp;
import com.gec.wiki.pojo.resp.ServiceQueryResp;
import com.gec.wiki.service.ServiceService;
import com.gec.wiki.utils.CopyUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 服务管理控制器
 */
@RestController
@RequestMapping("/service")
public class ServiceController {

    private static final Logger LOG = LoggerFactory.getLogger(ServiceController.class);

    @Autowired
    private ServiceService serviceService;

    /**
     * 分页查询服务列表
     */
    @GetMapping("/getServiceListByPage")
    public CommonResp<PageResp<ServiceQueryResp>> getServiceListByPage(ServiceQueryReq req) {
        CommonResp<PageResp<ServiceQueryResp>> resp = new CommonResp<>();
        try {
            LOG.info("🔍 分页查询服务列表，参数：{}", req);
            
            // 验证分页参数
            if (req.getPage() == null || req.getPage() < 1) {
                req.setPage(1);
            }
            if (req.getSize() == null || req.getSize() < 1) {
                req.setSize(10);
            }
            
            PageResp<ServiceQueryResp> pageResp = serviceService.getServiceListByPage(req);
            resp.setContent(pageResp);
            resp.setSuccess(true);
            resp.setMessage("查询成功");
            LOG.info("📊 查询结果：共 {} 条记录", pageResp.getTotal());
        } catch (Exception e) {
            LOG.error("❌ 分页查询服务列表失败", e);
            resp.setSuccess(false);
            resp.setMessage("查询失败：" + e.getMessage());
        }
        return resp;
    }

    /**
     * 获取所有服务列表
     */
    @GetMapping("/getAllServiceList")
    public CommonResp<List<ServiceQueryResp>> getAllServiceList(ServiceQueryReq req) {
        CommonResp<List<ServiceQueryResp>> resp = new CommonResp<>();
        try {
            LOG.info("查询所有服务列表，参数：{}", req);
            List<ServiceQueryResp> list = serviceService.getAllServiceList(req);
            resp.setContent(list);
            resp.setSuccess(true);
            resp.setMessage("查询成功");
        } catch (Exception e) {
            LOG.error("查询所有服务列表失败", e);
            resp.setSuccess(false);
            resp.setMessage("查询失败：" + e.getMessage());
        }
        return resp;
    }

    /**
     * 根据ID获取服务详情
     */
    @GetMapping("/getServiceById")
    public CommonResp<ServiceQueryResp> getServiceById(@RequestParam Long id) {
        CommonResp<ServiceQueryResp> resp = new CommonResp<>();
        try {
            LOG.info("根据ID获取服务详情，ID：{}", id);
            ServiceQueryResp service = serviceService.getServiceById(id);
            resp.setContent(service);
            resp.setSuccess(true);
            resp.setMessage("查询成功");
        } catch (Exception e) {
            LOG.error("根据ID获取服务详情失败", e);
            resp.setSuccess(false);
            resp.setMessage("查询失败：" + e.getMessage());
        }
        return resp;
    }

    /**
     * 保存或更新服务
     */
    @PostMapping("/save")
    public CommonResp<Void> save(@RequestBody ServiceSaveReq req) {
        CommonResp<Void> resp = new CommonResp<>();
        try {
            LOG.info("💾 保存或更新服务，参数：{}", req);
            
            // 验证必填字段
            if (ObjectUtils.isEmpty(req.getName())) {
                resp.setSuccess(false);
                resp.setMessage("服务名称不能为空");
                LOG.warn("❌ 服务名称为空");
                return resp;
            }
            
            if (ObjectUtils.isEmpty(req.getPrice())) {
                resp.setSuccess(false);
                resp.setMessage("服务价格不能为空");
                LOG.warn("❌ 服务价格为空");
                return resp;
            }
            
            if (ObjectUtils.isEmpty(req.getCategory1Id()) || ObjectUtils.isEmpty(req.getCategory2Id())) {
                resp.setSuccess(false);
                resp.setMessage("请选择服务分类");
                LOG.warn("❌ 服务分类为空：category1Id={}, category2Id={}", req.getCategory1Id(), req.getCategory2Id());
                return resp;
            }
            
            if (ObjectUtils.isEmpty(req.getDuration())) {
                resp.setSuccess(false);
                resp.setMessage("服务时长不能为空");
                LOG.warn("❌ 服务时长为空");
                return resp;
            }
            
            Service service = CopyUtil.copy(req, Service.class);
            LOG.info("🔄 转换后的Service对象：{}", service);
            
            boolean result = serviceService.saveOrUpdateService(service);
            if (result) {
                resp.setSuccess(true);
                resp.setMessage(ObjectUtils.isEmpty(req.getId()) ? "添加成功" : "修改成功");
                LOG.info("✅ 服务保存成功，ID：{}", service.getId());
            } else {
                resp.setSuccess(false);
                resp.setMessage("操作失败");
                LOG.error("❌ 服务保存失败");
            }
        } catch (Exception e) {
            LOG.error("💥 保存或更新服务失败", e);
            resp.setSuccess(false);
            resp.setMessage("操作失败：" + e.getMessage());
        }
        return resp;
    }

    /**
     * 删除服务
     */
    @GetMapping("/remove")
    public CommonResp<Void> remove(@RequestParam Long id) {
        CommonResp<Void> resp = new CommonResp<>();
        try {
            LOG.info("删除服务，ID：{}", id);
            boolean result = serviceService.removeById(id);
            if (result) {
                resp.setSuccess(true);
                resp.setMessage("删除成功");
            } else {
                resp.setSuccess(false);
                resp.setMessage("删除失败");
            }
        } catch (Exception e) {
            LOG.error("删除服务失败", e);
            resp.setSuccess(false);
            resp.setMessage("删除失败：" + e.getMessage());
        }
        return resp;
    }

    /**
     * 批量更新服务状态
     */
    @PostMapping("/batchUpdateStatus")
    public CommonResp<Void> batchUpdateStatus(@RequestBody BatchUpdateStatusReq req) {
        CommonResp<Void> resp = new CommonResp<>();
        try {
            LOG.info("批量更新服务状态，参数：{}", req);
            boolean result = serviceService.batchUpdateStatus(req.getIds(), req.getStatus());
            if (result) {
                resp.setSuccess(true);
                resp.setMessage("批量更新状态成功");
            } else {
                resp.setSuccess(false);
                resp.setMessage("批量更新状态失败");
            }
        } catch (Exception e) {
            LOG.error("批量更新服务状态失败", e);
            resp.setSuccess(false);
            resp.setMessage("批量更新状态失败：" + e.getMessage());
        }
        return resp;
    }

    /**
     * 批量删除服务
     */
    @PostMapping("/batchDelete")
    public CommonResp<Void> batchDelete(@RequestBody BatchDeleteReq req) {
        CommonResp<Void> resp = new CommonResp<>();
        try {
            LOG.info("批量删除服务，参数：{}", req);
            boolean result = serviceService.batchDeleteServices(req.getIds());
            if (result) {
                resp.setSuccess(true);
                resp.setMessage("批量删除成功");
            } else {
                resp.setSuccess(false);
                resp.setMessage("批量删除失败");
            }
        } catch (Exception e) {
            LOG.error("批量删除服务失败", e);
            resp.setSuccess(false);
            resp.setMessage("批量删除失败：" + e.getMessage());
        }
        return resp;
    }

    /**
     * 上传服务图片
     */
    @PostMapping("/uploadImage")
    public CommonResp<String> uploadImage(@RequestParam("file") MultipartFile file) {
        CommonResp<String> resp = new CommonResp<>();
        try {
            LOG.info("上传服务图片，文件名：{}", file.getOriginalFilename());
            String imageUrl = serviceService.uploadServiceImage(file);
            resp.setContent(imageUrl);
            resp.setSuccess(true);
            resp.setMessage("图片上传成功");
        } catch (Exception e) {
            LOG.error("上传服务图片失败", e);
            resp.setSuccess(false);
            resp.setMessage("图片上传失败：" + e.getMessage());
        }
        return resp;
    }

    /**
     * 批量更新状态请求类
     */
    public static class BatchUpdateStatusReq {
        private List<Long> ids;
        private Integer status;

        public List<Long> getIds() {
            return ids;
        }

        public void setIds(List<Long> ids) {
            this.ids = ids;
        }

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }

        @Override
        public String toString() {
            return "BatchUpdateStatusReq{" +
                    "ids=" + ids +
                    ", status=" + status +
                    '}';
        }
    }

    /**
     * 批量删除请求类
     */
    public static class BatchDeleteReq {
        private List<Long> ids;

        public List<Long> getIds() {
            return ids;
        }

        public void setIds(List<Long> ids) {
            this.ids = ids;
        }

        @Override
        public String toString() {
            return "BatchDeleteReq{" +
                    "ids=" + ids +
                    '}';
        }
    }
}
