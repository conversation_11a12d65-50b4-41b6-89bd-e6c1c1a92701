2025-09-09 21:52:09.051 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 21.0.7 on LAPTOP-4VB8OLQM with PID 28940 (D:\JavaCar\wiki\wiki\target\car-service-0.0.1-SNAPSHOT.jar started by fls in D:\JavaCar\wiki\wiki)
2025-09-09 21:52:09.061 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-09 21:52:10.011 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :262  [32m                  [0;39m Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-09 21:52:10.027 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :132  [32m                  [0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-09 21:52:10.059 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :201  [32m                  [0;39m Finished Spring Data repository scanning in 16 ms. Found 0 Redis repository interfaces.
2025-09-09 21:52:11.531 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8880 (http)
2025-09-09 21:52:11.564 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8880"]
2025-09-09 21:52:11.566 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-09 21:52:11.566 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-09 21:52:11.752 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-09 21:52:11.753 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 2603 ms
2025-09-09 21:52:16.541 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-09 21:52:17.280 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8880"]
2025-09-09 21:52:17.297 WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext:591  [32m                  [0;39m Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'; nested exception is org.springframework.boot.web.server.PortInUseException: Port 8880 is already in use
2025-09-09 21:52:17.322 INFO  com.alibaba.druid.pool.DruidDataSource            :2043 [32m                  [0;39m {dataSource-0} closing ...
2025-09-09 21:52:17.324 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Pausing ProtocolHandler ["http-nio-8880"]
2025-09-09 21:52:17.324 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Stopping service [Tomcat]
2025-09-09 21:52:17.540 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Stopping ProtocolHandler ["http-nio-8880"]
2025-09-09 21:52:17.540 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Destroying ProtocolHandler ["http-nio-8880"]
2025-09-09 21:52:17.556 INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener:136  [32m                  [0;39m 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-09-09 21:52:17.618 ERROR o.s.b.diagnostics.LoggingFailureAnalysisReporter  :40   [32m                  [0;39m 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8880 was already in use.

Action:

Identify and stop the process that's listening on port 8880 or configure this application to listen on another port.

2025-09-09 21:53:13.948 INFO  com.gec.wiki.WikiApplication                      :55   [32m                  [0;39m Starting WikiApplication using Java 21.0.7 on LAPTOP-4VB8OLQM with PID 8016 (D:\JavaCar\wiki\wiki\target\car-service-0.0.1-SNAPSHOT.jar started by fls in D:\JavaCar\wiki\wiki)
2025-09-09 21:53:13.953 INFO  com.gec.wiki.WikiApplication                      :631  [32m                  [0;39m No active profile set, falling back to 1 default profile: "default"
2025-09-09 21:53:14.881 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :262  [32m                  [0;39m Multiple Spring Data modules found, entering strict repository configuration mode
2025-09-09 21:53:14.881 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :132  [32m                  [0;39m Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-09-09 21:53:14.929 INFO  o.s.d.r.config.RepositoryConfigurationDelegate    :201  [32m                  [0;39m Finished Spring Data repository scanning in 13 ms. Found 0 Redis repository interfaces.
2025-09-09 21:53:15.959 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :108  [32m                  [0;39m Tomcat initialized with port(s): 8880 (http)
2025-09-09 21:53:15.971 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Initializing ProtocolHandler ["http-nio-8880"]
2025-09-09 21:53:15.971 INFO  org.apache.catalina.core.StandardService          :173  [32m                  [0;39m Starting service [Tomcat]
2025-09-09 21:53:15.971 INFO  org.apache.catalina.core.StandardEngine           :173  [32m                  [0;39m Starting Servlet engine: [Apache Tomcat/9.0.69]
2025-09-09 21:53:16.054 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring embedded WebApplicationContext
2025-09-09 21:53:16.054 INFO  o.s.b.w.s.c.ServletWebServerApplicationContext    :292  [32m                  [0;39m Root WebApplicationContext: initialization completed in 2011 ms
2025-09-09 21:53:18.677 INFO  o.s.b.a.web.servlet.WelcomePageHandlerMapping     :53   [32m                  [0;39m Adding welcome page: class path resource [static/index.html]
2025-09-09 21:53:19.232 INFO  org.apache.coyote.http11.Http11NioProtocol        :173  [32m                  [0;39m Starting ProtocolHandler ["http-nio-8880"]
2025-09-09 21:53:19.264 INFO  o.s.boot.web.embedded.tomcat.TomcatWebServer      :220  [32m                  [0;39m Tomcat started on port(s): 8880 (http) with context path ''
2025-09-09 21:53:19.285 INFO  com.gec.wiki.WikiApplication                      :61   [32m                  [0;39m Started WikiApplication in 6.242 seconds (JVM running for 6.972)
2025-09-09 21:53:19.291 INFO  com.gec.wiki.WikiApplication                      :23   [32m                  [0;39m 汽车维修服务平台启动成功！！
2025-09-09 21:53:19.291 INFO  com.gec.wiki.WikiApplication                      :24   [32m                  [0;39m 地址：	http://127.0.0.1:8880
2025-09-09 21:54:18.987 INFO  o.a.c.core.ContainerBase.[Tomcat].[localhost].[/] :173  [32m                  [0;39m Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-09-09 21:54:18.987 INFO  org.springframework.web.servlet.DispatcherServlet :525  [32m                  [0;39m Initializing Servlet 'dispatcherServlet'
2025-09-09 21:54:18.987 INFO  org.springframework.web.servlet.DispatcherServlet :547  [32m                  [0;39m Completed initialization in 0 ms
2025-09-09 21:54:45.161 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4848578892956704  [0;39m ------------- 开始 -------------
2025-09-09 21:54:45.169 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4848578892956704  [0;39m 请求地址: http://localhost:8880/shop/service/categories GET
2025-09-09 21:54:45.169 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4848578892956704  [0;39m 类名方法: com.gec.wiki.controller.ShopServiceController.getServiceCategories
2025-09-09 21:54:45.171 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4848578892956704  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-09 21:54:45.181 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4848578893612064  [0;39m ------------- 开始 -------------
2025-09-09 21:54:45.181 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4848578893612064  [0;39m 请求地址: http://localhost:8880/shop/service/list GET
2025-09-09 21:54:45.184 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4848578893612064  [0;39m 类名方法: com.gec.wiki.controller.ShopServiceController.getShopServiceList
2025-09-09 21:54:45.184 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4848578893612064  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-09 21:54:45.236 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4848578892956704  [0;39m 请求参数: []
2025-09-09 21:54:45.246 INFO  com.gec.wiki.controller.ShopServiceController     :83   [32m4848578892956704  [0;39m 📋 获取服务分类列表
2025-09-09 21:54:45.268 INFO  com.gec.wiki.service.impl.CategoryServiceImpl     :50   [32m4848578892956704  [0;39m 🌲 构建分类树形结构
2025-09-09 21:54:45.365 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4848578893612064  [0;39m 请求参数: [{"page":1,"size":10}]
2025-09-09 21:54:45.369 INFO  com.gec.wiki.controller.ShopServiceController     :45   [32m4848578893612064  [0;39m 🏪 获取维修店服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-09 21:54:45.421 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :326  [32m4848578893612064  [0;39m 🏪 构建维修店服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}, shopId: 1
2025-09-09 21:54:45.446 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :367  [32m4848578893612064  [0;39m 🔢 执行维修店服务分页查询：页码=1, 页大小=10
2025-09-09 21:54:45.863 INFO  com.alibaba.druid.pool.DruidDataSource            :990  [32m4848578892956704  [0;39m {dataSource-1} inited
2025-09-09 21:54:46.462 INFO  com.gec.wiki.service.impl.CategoryServiceImpl     :67   [32m4848578892956704  [0;39m ✅ 分类树形结构构建完成，根节点数量：4
2025-09-09 21:54:46.462 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :371  [32m4848578893612064  [0;39m 📋 维修店 1 查询结果：共 1 条记录，当前页 1 条
2025-09-09 21:54:46.480 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4848578892956704  [0;39m 返回结果: {"content":[{"children":[{"children":[],"id":101,"level":1,"name":"机油保养","parent":100,"sort":1},{"children":[],"id":****************,"level":1,"name":"空调换新","parent":100,"sort":1},{"children":[],"id":****************,"level":1,"name":"汽车全身清洗","parent":100,"sort":1},{"children":[],"id":102,"level":1,"name":"轮胎保养","parent":100,"sort":2},{"children":[],"id":103,"level":1,"name":"制动系统","parent":100,"sort":3}],"id":100,"level":0,"name":"常规保养","parent":0,"sort":1},{"children":[{"children":[],"id":4848310685074464,"level":1,"name":"窗户换新","parent":4848309795718176,"sort":1}],"id":4848309795718176,"level":0,"name":"车窗维修","parent":0,"sort":1},{"children":[{"children":[],"id":201,"level":1,"name":"发动机检修","parent":200,"sort":1},{"children":[],"id":202,"level":1,"name":"冷却系统","parent":200,"sort":2}],"id":200,"level":0,"name":"发动机维修","parent":0,"sort":2},{"children":[{"children":[],"id":301,"level":1,"name":"电瓶维护","parent":300,"sort":1}],"id":300,"level":0,"name":"电气系统","parent":0,"sort":3}],"message":"查询成功","success":true}
2025-09-09 21:54:46.482 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4848578892956704  [0;39m ------------- 结束 耗时：1321 ms -------------
2025-09-09 21:54:46.496 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :387  [32m4848578893612064  [0;39m ✅ 维修店 1 服务查询完成，返回 1 条记录
2025-09-09 21:54:46.496 INFO  com.gec.wiki.controller.ShopServiceController     :67   [32m4848578893612064  [0;39m ✅ 维修店 1 查询到 1 条服务记录
2025-09-09 21:54:46.508 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4848578893612064  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"content":"123456","cover":"/image/afe19c37-3922-4802-af23-5dd03921bba6_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-09T20:25:19","description":"123456","duration":60,"id":4834096586359847,"isRecommend":1,"name":"汽车维修","originalPrice":40.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T21:42:26"}],"total":1},"message":"查询成功","success":true}
2025-09-09 21:54:46.508 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4848578893612064  [0;39m ------------- 结束 耗时：1327 ms -------------
2025-09-09 21:55:16.524 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4848579920659488  [0;39m ------------- 开始 -------------
2025-09-09 21:55:16.524 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4848579920659489  [0;39m ------------- 开始 -------------
2025-09-09 21:55:16.525 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4848579920659488  [0;39m 请求地址: http://localhost:8880/shop/service/categories GET
2025-09-09 21:55:16.526 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4848579920659488  [0;39m 类名方法: com.gec.wiki.controller.ShopServiceController.getServiceCategories
2025-09-09 21:55:16.525 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4848579920659489  [0;39m 请求地址: http://localhost:8880/shop/service/list GET
2025-09-09 21:55:16.528 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4848579920659488  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-09 21:55:16.528 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4848579920659489  [0;39m 类名方法: com.gec.wiki.controller.ShopServiceController.getShopServiceList
2025-09-09 21:55:16.529 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4848579920659489  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-09 21:55:16.529 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4848579920659488  [0;39m 请求参数: []
2025-09-09 21:55:16.530 INFO  com.gec.wiki.controller.ShopServiceController     :83   [32m4848579920659488  [0;39m 📋 获取服务分类列表
2025-09-09 21:55:16.530 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4848579920659489  [0;39m 请求参数: [{"page":1,"size":10}]
2025-09-09 21:55:16.531 INFO  com.gec.wiki.service.impl.CategoryServiceImpl     :50   [32m4848579920659488  [0;39m 🌲 构建分类树形结构
2025-09-09 21:55:16.531 INFO  com.gec.wiki.controller.ShopServiceController     :45   [32m4848579920659489  [0;39m 🏪 获取维修店服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-09 21:55:16.536 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :326  [32m4848579920659489  [0;39m 🏪 构建维修店服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}, shopId: 1
2025-09-09 21:55:16.538 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :367  [32m4848579920659489  [0;39m 🔢 执行维修店服务分页查询：页码=1, 页大小=10
2025-09-09 21:55:16.558 INFO  com.gec.wiki.service.impl.CategoryServiceImpl     :67   [32m4848579920659488  [0;39m ✅ 分类树形结构构建完成，根节点数量：4
2025-09-09 21:55:16.561 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4848579920659488  [0;39m 返回结果: {"content":[{"children":[{"children":[],"id":101,"level":1,"name":"机油保养","parent":100,"sort":1},{"children":[],"id":****************,"level":1,"name":"空调换新","parent":100,"sort":1},{"children":[],"id":****************,"level":1,"name":"汽车全身清洗","parent":100,"sort":1},{"children":[],"id":102,"level":1,"name":"轮胎保养","parent":100,"sort":2},{"children":[],"id":103,"level":1,"name":"制动系统","parent":100,"sort":3}],"id":100,"level":0,"name":"常规保养","parent":0,"sort":1},{"children":[{"children":[],"id":4848310685074464,"level":1,"name":"窗户换新","parent":4848309795718176,"sort":1}],"id":4848309795718176,"level":0,"name":"车窗维修","parent":0,"sort":1},{"children":[{"children":[],"id":201,"level":1,"name":"发动机检修","parent":200,"sort":1},{"children":[],"id":202,"level":1,"name":"冷却系统","parent":200,"sort":2}],"id":200,"level":0,"name":"发动机维修","parent":0,"sort":2},{"children":[{"children":[],"id":301,"level":1,"name":"电瓶维护","parent":300,"sort":1}],"id":300,"level":0,"name":"电气系统","parent":0,"sort":3}],"message":"查询成功","success":true}
2025-09-09 21:55:16.563 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4848579920659488  [0;39m ------------- 结束 耗时：39 ms -------------
2025-09-09 21:55:16.564 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :371  [32m4848579920659489  [0;39m 📋 维修店 1 查询结果：共 1 条记录，当前页 1 条
2025-09-09 21:55:16.575 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :387  [32m4848579920659489  [0;39m ✅ 维修店 1 服务查询完成，返回 1 条记录
2025-09-09 21:55:16.578 INFO  com.gec.wiki.controller.ShopServiceController     :67   [32m4848579920659489  [0;39m ✅ 维修店 1 查询到 1 条服务记录
2025-09-09 21:55:16.581 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4848579920659489  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"content":"123456","cover":"/image/afe19c37-3922-4802-af23-5dd03921bba6_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-09T20:25:19","description":"123456","duration":60,"id":4834096586359847,"isRecommend":1,"name":"汽车维修","originalPrice":40.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T21:42:26"}],"total":1},"message":"查询成功","success":true}
2025-09-09 21:55:16.581 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4848579920659489  [0;39m ------------- 结束 耗时：57 ms -------------
2025-09-09 21:55:34.169 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4848580498850848  [0;39m ------------- 开始 -------------
2025-09-09 21:55:34.169 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4848580498850849  [0;39m ------------- 开始 -------------
2025-09-09 21:55:34.184 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4848580498850848  [0;39m 请求地址: http://localhost:8880/shop/service/categories GET
2025-09-09 21:55:34.184 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4848580498850849  [0;39m 请求地址: http://localhost:8880/shop/service/list GET
2025-09-09 21:55:34.184 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4848580498850849  [0;39m 类名方法: com.gec.wiki.controller.ShopServiceController.getShopServiceList
2025-09-09 21:55:34.184 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4848580498850848  [0;39m 类名方法: com.gec.wiki.controller.ShopServiceController.getServiceCategories
2025-09-09 21:55:34.184 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4848580498850849  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-09 21:55:34.184 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4848580498850848  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-09 21:55:34.184 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4848580498850848  [0;39m 请求参数: []
2025-09-09 21:55:34.184 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4848580498850849  [0;39m 请求参数: [{"page":1,"size":10}]
2025-09-09 21:55:34.184 INFO  com.gec.wiki.controller.ShopServiceController     :83   [32m4848580498850848  [0;39m 📋 获取服务分类列表
2025-09-09 21:55:34.184 INFO  com.gec.wiki.controller.ShopServiceController     :45   [32m4848580498850849  [0;39m 🏪 获取维修店服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-09 21:55:34.184 INFO  com.gec.wiki.service.impl.CategoryServiceImpl     :50   [32m4848580498850848  [0;39m 🌲 构建分类树形结构
2025-09-09 21:55:34.184 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :326  [32m4848580498850849  [0;39m 🏪 构建维修店服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}, shopId: 1
2025-09-09 21:55:34.184 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :367  [32m4848580498850849  [0;39m 🔢 执行维修店服务分页查询：页码=1, 页大小=10
2025-09-09 21:55:34.199 INFO  com.gec.wiki.service.impl.CategoryServiceImpl     :67   [32m4848580498850848  [0;39m ✅ 分类树形结构构建完成，根节点数量：4
2025-09-09 21:55:34.199 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4848580498850848  [0;39m 返回结果: {"content":[{"children":[{"children":[],"id":101,"level":1,"name":"机油保养","parent":100,"sort":1},{"children":[],"id":****************,"level":1,"name":"空调换新","parent":100,"sort":1},{"children":[],"id":****************,"level":1,"name":"汽车全身清洗","parent":100,"sort":1},{"children":[],"id":102,"level":1,"name":"轮胎保养","parent":100,"sort":2},{"children":[],"id":103,"level":1,"name":"制动系统","parent":100,"sort":3}],"id":100,"level":0,"name":"常规保养","parent":0,"sort":1},{"children":[{"children":[],"id":4848310685074464,"level":1,"name":"窗户换新","parent":4848309795718176,"sort":1}],"id":4848309795718176,"level":0,"name":"车窗维修","parent":0,"sort":1},{"children":[{"children":[],"id":201,"level":1,"name":"发动机检修","parent":200,"sort":1},{"children":[],"id":202,"level":1,"name":"冷却系统","parent":200,"sort":2}],"id":200,"level":0,"name":"发动机维修","parent":0,"sort":2},{"children":[{"children":[],"id":301,"level":1,"name":"电瓶维护","parent":300,"sort":1}],"id":300,"level":0,"name":"电气系统","parent":0,"sort":3}],"message":"查询成功","success":true}
2025-09-09 21:55:34.199 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4848580498850848  [0;39m ------------- 结束 耗时：30 ms -------------
2025-09-09 21:55:34.203 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :371  [32m4848580498850849  [0;39m 📋 维修店 1 查询结果：共 1 条记录，当前页 1 条
2025-09-09 21:55:34.205 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :387  [32m4848580498850849  [0;39m ✅ 维修店 1 服务查询完成，返回 1 条记录
2025-09-09 21:55:34.205 INFO  com.gec.wiki.controller.ShopServiceController     :67   [32m4848580498850849  [0;39m ✅ 维修店 1 查询到 1 条服务记录
2025-09-09 21:55:34.205 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4848580498850849  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"content":"123456","cover":"/image/afe19c37-3922-4802-af23-5dd03921bba6_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-09T20:25:19","description":"123456","duration":60,"id":4834096586359847,"isRecommend":1,"name":"汽车维修","originalPrice":40.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T21:42:26"}],"total":1},"message":"查询成功","success":true}
2025-09-09 21:55:34.205 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4848580498850849  [0;39m ------------- 结束 耗时：36 ms -------------
2025-09-09 21:56:42.557 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4848582739788832  [0;39m ------------- 开始 -------------
2025-09-09 21:56:42.564 INFO  com.gec.wiki.aspect.LogAspect                     :54   [32m4848582740018208  [0;39m ------------- 开始 -------------
2025-09-09 21:56:42.566 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4848582739788832  [0;39m 请求地址: http://localhost:8880/shop/service/categories GET
2025-09-09 21:56:42.567 INFO  com.gec.wiki.aspect.LogAspect                     :55   [32m4848582740018208  [0;39m 请求地址: http://localhost:8880/shop/service/list GET
2025-09-09 21:56:42.572 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4848582739788832  [0;39m 类名方法: com.gec.wiki.controller.ShopServiceController.getServiceCategories
2025-09-09 21:56:42.573 INFO  com.gec.wiki.aspect.LogAspect                     :56   [32m4848582740018208  [0;39m 类名方法: com.gec.wiki.controller.ShopServiceController.getShopServiceList
2025-09-09 21:56:42.576 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4848582739788832  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-09 21:56:42.577 INFO  com.gec.wiki.aspect.LogAspect                     :57   [32m4848582740018208  [0;39m 远程地址: 0:0:0:0:0:0:0:1
2025-09-09 21:56:42.578 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4848582739788832  [0;39m 请求参数: []
2025-09-09 21:56:42.580 INFO  com.gec.wiki.controller.ShopServiceController     :83   [32m4848582739788832  [0;39m 📋 获取服务分类列表
2025-09-09 21:56:42.580 INFO  com.gec.wiki.aspect.LogAspect                     :79   [32m4848582740018208  [0;39m 请求参数: [{"page":1,"size":10}]
2025-09-09 21:56:42.581 INFO  com.gec.wiki.controller.ShopServiceController     :45   [32m4848582740018208  [0;39m 🏪 获取维修店服务列表，参数：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}
2025-09-09 21:56:42.582 INFO  com.gec.wiki.service.impl.CategoryServiceImpl     :50   [32m4848582739788832  [0;39m 🌲 构建分类树形结构
2025-09-09 21:56:42.582 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :326  [32m4848582740018208  [0;39m 🏪 构建维修店服务查询条件：ServiceQueryReq{name='null', category1Id=null, category2Id=null, status=null, isRecommend=null, page=1, size=10}, shopId: 1
2025-09-09 21:56:42.590 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :367  [32m4848582740018208  [0;39m 🔢 执行维修店服务分页查询：页码=1, 页大小=10
2025-09-09 21:56:42.645 WARN  com.alibaba.druid.pool.DruidAbstractDataSource    :1494 [32m4848582739788832  [0;39m discard long time none received connection. , jdbcUrl : ********************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 68404
2025-09-09 21:56:42.645 WARN  com.alibaba.druid.pool.DruidAbstractDataSource    :1494 [32m4848582740018208  [0;39m discard long time none received connection. , jdbcUrl : ********************************************************************************************************************************, version : 1.2.5, lastPacketReceivedIdleMillis : 68428
2025-09-09 21:56:42.665 INFO  com.gec.wiki.service.impl.CategoryServiceImpl     :67   [32m4848582739788832  [0;39m ✅ 分类树形结构构建完成，根节点数量：4
2025-09-09 21:56:42.666 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4848582739788832  [0;39m 返回结果: {"content":[{"children":[{"children":[],"id":101,"level":1,"name":"机油保养","parent":100,"sort":1},{"children":[],"id":****************,"level":1,"name":"空调换新","parent":100,"sort":1},{"children":[],"id":****************,"level":1,"name":"汽车全身清洗","parent":100,"sort":1},{"children":[],"id":102,"level":1,"name":"轮胎保养","parent":100,"sort":2},{"children":[],"id":103,"level":1,"name":"制动系统","parent":100,"sort":3}],"id":100,"level":0,"name":"常规保养","parent":0,"sort":1},{"children":[{"children":[],"id":4848310685074464,"level":1,"name":"窗户换新","parent":4848309795718176,"sort":1}],"id":4848309795718176,"level":0,"name":"车窗维修","parent":0,"sort":1},{"children":[{"children":[],"id":201,"level":1,"name":"发动机检修","parent":200,"sort":1},{"children":[],"id":202,"level":1,"name":"冷却系统","parent":200,"sort":2}],"id":200,"level":0,"name":"发动机维修","parent":0,"sort":2},{"children":[{"children":[],"id":301,"level":1,"name":"电瓶维护","parent":300,"sort":1}],"id":300,"level":0,"name":"电气系统","parent":0,"sort":3}],"message":"查询成功","success":true}
2025-09-09 21:56:42.666 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4848582739788832  [0;39m ------------- 结束 耗时：111 ms -------------
2025-09-09 21:56:42.670 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :371  [32m4848582740018208  [0;39m 📋 维修店 1 查询结果：共 1 条记录，当前页 1 条
2025-09-09 21:56:42.675 INFO  com.gec.wiki.service.impl.ServiceServiceImpl      :387  [32m4848582740018208  [0;39m ✅ 维修店 1 服务查询完成，返回 1 条记录
2025-09-09 21:56:42.676 INFO  com.gec.wiki.controller.ShopServiceController     :67   [32m4848582740018208  [0;39m ✅ 维修店 1 查询到 1 条服务记录
2025-09-09 21:56:42.677 INFO  com.gec.wiki.aspect.LogAspect                     :91   [32m4848582740018208  [0;39m 返回结果: {"content":{"list":[{"bookingCount":0,"category1Id":200,"category2Id":201,"categoryName":"发动机检修","completeCount":0,"content":"123456","cover":"/image/afe19c37-3922-4802-af23-5dd03921bba6_e52871ea-9bce-4769-87fb-e5b45962ea1a.jpg","createTime":"2025-09-09T20:25:19","description":"123456","duration":60,"id":4834096586359847,"isRecommend":1,"name":"汽车维修","originalPrice":40.00,"price":60.00,"ratingCount":0,"ratingScore":5.0,"shopId":1,"status":1,"updateTime":"2025-09-09T21:42:26"}],"total":1},"message":"查询成功","success":true}
2025-09-09 21:56:42.678 INFO  com.gec.wiki.aspect.LogAspect                     :92   [32m4848582740018208  [0;39m ------------- 结束 耗时：114 ms -------------
