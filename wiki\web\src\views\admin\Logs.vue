<template>
  <div class="admin-logs">
    <div class="page-header">
      <h1>系统日志</h1>
      <p>查看系统操作和错误日志</p>
    </div>

    <a-card>
      <div class="search-section">
        <a-row :gutter="16">
          <a-col :span="6">
            <a-select
              v-model:value="searchForm.logType"
              placeholder="日志类型"
              allowClear
            >
              <a-select-option value="login">登录日志</a-select-option>
              <a-select-option value="operation">操作日志</a-select-option>
              <a-select-option value="error">错误日志</a-select-option>
            </a-select>
          </a-col>
          <a-col :span="6">
            <a-range-picker v-model:value="searchForm.dateRange" />
          </a-col>
          <a-col :span="4">
            <a-button type="primary" @click="handleSearch">
              <SearchOutlined />
              搜索
            </a-button>
          </a-col>
        </a-row>
      </div>

      <a-table
        :columns="columns"
        :data-source="logs"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        row-key="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'level'">
            <a-tag :color="getLevelColor(record.level)">
              {{ record.level }}
            </a-tag>
          </template>
        </template>
      </a-table>
    </a-card>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue';
import { SearchOutlined } from '@ant-design/icons-vue';

export default defineComponent({
  name: 'AdminLogs',
  components: {
    SearchOutlined
  },
  setup() {
    const loading = ref(false);
    const logs = ref<any[]>([]);
    const searchForm = ref({
      logType: undefined,
      dateRange: undefined
    });

    const pagination = ref({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total: number) => `共 ${total} 条记录`
    });

    const columns = [
      { title: '时间', dataIndex: 'timestamp', key: 'timestamp', width: 180 },
      { title: '级别', dataIndex: 'level', key: 'level', width: 80 },
      { title: '用户', dataIndex: 'username', key: 'username', width: 120 },
      { title: '操作', dataIndex: 'action', key: 'action', width: 150 },
      { title: '详情', dataIndex: 'message', key: 'message' },
      { title: 'IP地址', dataIndex: 'ipAddress', key: 'ipAddress', width: 120 }
    ];

    const getLevelColor = (level: string) => {
      const colors: Record<string, string> = {
        'INFO': 'blue',
        'WARN': 'orange', 
        'ERROR': 'red',
        'DEBUG': 'default'
      };
      return colors[level] || 'default';
    };

    const handleSearch = () => {
      pagination.value.current = 1;
      loadLogs();
    };

    const handleTableChange = (pag: any) => {
      pagination.value = { ...pagination.value, ...pag };
      loadLogs();
    };

    const loadLogs = async () => {
      loading.value = true;
      try {
        // TODO: 实现API调用
        // 模拟数据
        logs.value = [
          {
            id: 1,
            timestamp: '2024-01-15 10:30:15',
            level: 'INFO',
            username: '管理员',
            action: '用户登录',
            message: '管理员登录系统',
            ipAddress: '*************'
          }
        ] as any;
        pagination.value.total = 1;
      } finally {
        loading.value = false;
      }
    };

    onMounted(() => {
      loadLogs();
    });

    return {
      loading,
      logs,
      searchForm,
      pagination,
      columns,
      getLevelColor,
      handleSearch,
      handleTableChange
    };
  }
});
</script>

<style scoped>
.admin-logs {
  padding: 24px;
  flex: 1;
  background: #f0f2f5;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #333;
}

.page-header p {
  margin: 0;
  color: #666;
}

.search-section {
  margin-bottom: 24px;
}
</style>
