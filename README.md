# 汽车维修服务平台

## 项目介绍

这是一个基于Spring Boot + Vue3的汽车维修服务管理平台，提供汽车维修服务的在线预约、管理和查询功能。

## 功能特点

### 前台功能
- 🚗 服务展示：浏览各类汽车维修服务
- 📊 服务分类：按维修类型分类显示服务
- 💰 价格展示：清晰显示服务价格和时长
- 📱 响应式设计：支持移动端访问

### 后台管理
- 🛠️ 服务管理：添加、编辑、删除维修服务项目
- 📂 分类管理：管理服务分类体系
- 🖼️ 图片上传：支持服务图片上传
- 📋 数据统计：服务预约和完成情况统计

## 技术栈

### 后端
- **Spring Boot 2.7.6** - 主框架
- **MyBatis Plus** - ORM框架
- **MySQL** - 数据库
- **Druid** - 数据库连接池
- **Lombok** - 代码简化工具

### 前端
- **Vue 3** - 前端框架
- **Ant Design Vue** - UI组件库
- **TypeScript** - 类型支持
- **Axios** - HTTP客户端
- **Vue Router** - 路由管理

## 项目结构

```
car-service/
├── wiki/                           # 后端项目
│   ├── src/main/java/com/gec/wiki/
│   │   ├── controller/             # 控制器层
│   │   ├── service/               # 服务层
│   │   ├── mapper/                # 数据访问层
│   │   ├── pojo/                  # 实体类
│   │   └── utils/                 # 工具类
│   ├── src/main/resources/
│   │   ├── mapper/                # MyBatis映射文件
│   │   ├── sql/                   # 数据库脚本
│   │   └── application.yml        # 配置文件
│   └── pom.xml                    # Maven配置
├── web/                           # 前端项目
│   ├── src/
│   │   ├── views/                 # 页面组件
│   │   ├── components/            # 公共组件
│   │   ├── router/                # 路由配置
│   │   └── utils/                 # 工具函数
│   └── package.json               # 依赖配置
└── README.md                      # 项目文档
```

## 安装部署

### 环境要求
- JDK 1.8+
- Node.js 14+
- MySQL 5.7+
- Maven 3.6+

### 后端部署

1. **创建数据库**
   ```sql
   CREATE DATABASE car_service CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

2. **导入数据**
   ```bash
   mysql -u root -p car_service < wiki/src/main/resources/sql/car_service.sql
   ```

3. **修改配置**
   编辑 `wiki/src/main/resources/application.yml`
   ```yaml
   spring:
     datasource:
       url: ****************************************************************************************************
       username: root
       password: your_password
   ```

4. **启动后端**
   ```bash
   cd wiki
   mvn spring-boot:run
   ```
   后端服务将运行在 http://localhost:8080

### 前端部署

1. **安装依赖**
   ```bash
   cd web
   npm install
   ```

2. **启动开发服务器**
   ```bash
   npm run serve-dev
   ```
   前端应用将运行在 http://localhost:8081

3. **生产环境构建**
   ```bash
   npm run build-prod
   ```

## 数据库设计

### 主要表结构

- **service** - 汽车维修服务表
- **category** - 服务分类表
- **customer** - 客户信息表
- **vehicle** - 车辆信息表
- **technician** - 技师信息表
- **maintenance_record** - 维修记录表

详细的表结构请参考 `wiki/src/main/resources/sql/car_service.sql` 文件。

## API接口

### 服务管理
- `GET /service/getServiceListByPage` - 分页查询服务列表
- `POST /service/save` - 保存服务信息
- `GET /service/remove` - 删除服务
- `POST /service/uploadImage` - 上传服务图片

### 分类管理
- `GET /category/allList` - 获取所有分类

更多接口详情请参考后端Controller层代码。

## 系统截图

### 首页展示
- 轮播图展示平台特色
- 服务分类导航
- 服务卡片展示，包含价格、时长、统计信息

### 管理后台
- 服务项目管理
- 分类管理
- 图片上传功能

## 开发规范

### 代码规范
- 使用统一的代码格式化规则
- 遵循RESTful API设计规范
- 使用有意义的变量和方法命名

### 提交规范
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式调整
- refactor: 代码重构

## 许可证

本项目基于 MIT 许可证开源。

## 联系方式

如有问题或建议，请提交 Issue 或联系开发团队。
