package com.gec.wiki.pojo.req;

/**
 * 服务查询请求类
 */
public class ServiceQueryReq extends PageReq {
    
    /**
     * 服务名称
     */
    private String name;
    
    /**
     * 一级分类ID
     */
    private Long category1Id;
    
    /**
     * 二级分类ID
     */
    private Long category2Id;
    
    /**
     * 状态(0-下架 1-上架)
     */
    private Integer status;
    
    /**
     * 是否推荐(0-否 1-是)
     */
    private Integer isRecommend;
    
    /**
     * 店铺ID（用于按店铺筛选服务）
     */
    private Long shopId;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getCategory1Id() {
        return category1Id;
    }

    public void setCategory1Id(Long category1Id) {
        this.category1Id = category1Id;
    }

    public Long getCategory2Id() {
        return category2Id;
    }

    public void setCategory2Id(Long category2Id) {
        this.category2Id = category2Id;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getIsRecommend() {
        return isRecommend;
    }

    public void setIsRecommend(Integer isRecommend) {
        this.isRecommend = isRecommend;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    @Override
    public String toString() {
        return "ServiceQueryReq{" +
                "name='" + name + '\'' +
                ", category1Id=" + category1Id +
                ", category2Id=" + category2Id +
                ", status=" + status +
                ", isRecommend=" + isRecommend +
                ", shopId=" + shopId +
                ", page=" + getPage() +
                ", size=" + getSize() +
                '}';
    }
}
