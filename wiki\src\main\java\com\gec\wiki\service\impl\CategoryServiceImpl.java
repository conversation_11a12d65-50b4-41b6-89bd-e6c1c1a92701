package com.gec.wiki.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gec.wiki.pojo.Category;
import com.gec.wiki.pojo.req.CategoryQueryReq;
import com.gec.wiki.pojo.resp.CategoryQueryResp;
import com.gec.wiki.service.CategoryService;
import com.gec.wiki.mapper.CategoryMapper;
import com.gec.wiki.utils.CopyUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【category】的数据库操作Service实现
* @createDate 2025-06-18 14:35:21
*/
@Service
public class CategoryServiceImpl extends ServiceImpl<CategoryMapper, Category>
    implements CategoryService{

    private static final Logger LOG = LoggerFactory.getLogger(CategoryServiceImpl.class);

    @Override
    public List<Category> getByIds(List<Long> ids) {
        if (ObjectUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return this.listByIds(ids);
    }

    @Override
    public List<CategoryQueryResp> allList(CategoryQueryReq req) {
        QueryWrapper<Category> wrapper = new QueryWrapper<>();
        wrapper.orderByAsc("sort");
        List<Category> list = this.list(wrapper);
        List<CategoryQueryResp> resps = CopyUtil.copyList(list, CategoryQueryResp.class);
        return resps;
    }

    @Override
    public List<CategoryQueryResp> getTreeList() {
        LOG.info("🌲 构建分类树形结构");
        
        // 获取所有分类
        QueryWrapper<Category> wrapper = new QueryWrapper<>();
        wrapper.orderByAsc("sort");
        List<Category> allCategories = this.list(wrapper);
        
        if (ObjectUtils.isEmpty(allCategories)) {
            return new ArrayList<>();
        }
        
        // 转换为响应对象
        List<CategoryQueryResp> allCategoryResps = CopyUtil.copyList(allCategories, CategoryQueryResp.class);
        
        // 构建树形结构
        List<CategoryQueryResp> treeList = buildCategoryTree(allCategoryResps, 0L, 0);
        
        LOG.info("✅ 分类树形结构构建完成，根节点数量：{}", treeList.size());
        return treeList;
    }

    @Override
    public List<CategoryQueryResp> getChildrenByParentId(Long parentId) {
        LOG.info("📂 获取父分类 {} 的子分类", parentId);
        
        QueryWrapper<Category> wrapper = new QueryWrapper<>();
        wrapper.eq("parent", parentId == null ? 0 : parentId);
        wrapper.orderByAsc("sort");
        
        List<Category> children = this.list(wrapper);
        List<CategoryQueryResp> resps = CopyUtil.copyList(children, CategoryQueryResp.class);
        
        LOG.info("✅ 获取到 {} 个子分类", resps.size());
        return resps;
    }

    @Override
    public boolean deleteCategoryById(Long id) {
        LOG.info("🗑️ 删除分类，ID：{}", id);
        
        // 检查是否有子分类
        if (hasChildren(id)) {
            LOG.warn("❌ 分类删除失败：存在子分类，需要先删除子分类");
            return false;
        }
        
        // TODO: 检查是否有关联的服务项目
        // 这里可以添加业务逻辑检查，确保分类没有被服务项目使用
        
        boolean result = this.removeById(id);
        if (result) {
            LOG.info("✅ 分类删除成功，ID：{}", id);
        } else {
            LOG.error("❌ 分类删除失败，ID：{}", id);
        }
        
        return result;
    }

    @Override
    public boolean hasChildren(Long id) {
        QueryWrapper<Category> wrapper = new QueryWrapper<>();
        wrapper.eq("parent", id);
        long count = this.count(wrapper);
        return count > 0;
    }

    @Override
    public boolean isNameExists(String name, Long parentId, Long excludeId) {
        QueryWrapper<Category> wrapper = new QueryWrapper<>();
        wrapper.eq("name", name);
        wrapper.eq("parent", parentId == null ? 0 : parentId);
        
        if (excludeId != null) {
            wrapper.ne("id", excludeId);
        }
        
        long count = this.count(wrapper);
        return count > 0;
    }

    /**
     * 递归构建分类树形结构
     */
    private List<CategoryQueryResp> buildCategoryTree(List<CategoryQueryResp> allCategories, Long parentId, int level) {
        List<CategoryQueryResp> result = new ArrayList<>();
        
        for (CategoryQueryResp category : allCategories) {
            Long categoryParent = category.getParent() == null ? 0L : category.getParent();
            Long targetParent = parentId == null ? 0L : parentId;
            
            if (categoryParent.equals(targetParent)) {
                // 设置层级
                category.setLevel(level);
                
                // 递归查找子分类
                List<CategoryQueryResp> children = buildCategoryTree(allCategories, category.getId(), level + 1);
                category.setChildren(children);
                
                result.add(category);
            }
        }
        
        return result;
    }

    /**
     * 递归删除分类及其所有子分类
     */
    private void deleteCategoryAndChildren(Long id) {
        // 先删除所有子分类
        QueryWrapper<Category> wrapper = new QueryWrapper<>();
        wrapper.eq("parent", id);
        List<Category> children = this.list(wrapper);
        
        for (Category child : children) {
            deleteCategoryAndChildren(child.getId());
        }
        
        // 删除当前分类
        this.removeById(id);
        LOG.info("🗑️ 已删除分类，ID：{}", id);
    }
}




