/* 
 * 汽车维修服务平台 - 通用组件样式
 * 可复用的组件样式定义
 */

/* 页面头部样式 */
.page-header-modern {
  background: var(--bg-secondary);
  backdrop-filter: var(--blur-md);
  border-radius: 0 0 var(--radius-2xl) var(--radius-2xl);
  padding: var(--spacing-3xl);
  margin-bottom: var(--spacing-3xl);
  box-shadow: var(--shadow-lg);
  position: relative;
  z-index: var(--z-sticky);
}

.page-header-modern .header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--spacing-3xl);
}

.page-header-modern .title-section h1 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-primary);
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-header-modern .title-section p {
  margin: 0 0 var(--spacing-xl) 0;
  color: var(--text-secondary);
  font-size: var(--text-base);
}

/* 统计卡片样式 */
.stats-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-xl);
  flex-wrap: wrap;
}

.stat-item-modern {
  display: flex;
  align-items: baseline;
  gap: var(--spacing-xs);
}

.stat-number-modern {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--primary-color);
}

.stat-label-modern {
  font-size: var(--text-sm);
  color: var(--text-secondary);
}

.stat-divider-modern {
  width: 1px;
  height: var(--spacing-2xl);
  background: var(--border-medium);
}

/* 操作按钮组样式 */
.action-buttons {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.action-buttons .ant-btn {
  height: 44px;
  border-radius: var(--radius-md);
  font-weight: var(--font-medium);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

/* 卡片网格样式 */
.cards-grid {
  display: grid;
  gap: var(--spacing-2xl);
}

.cards-grid-1 {
  grid-template-columns: 1fr;
}

.cards-grid-2 {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.cards-grid-3 {
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.cards-grid-4 {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

/* 现代卡片样式 */
.modern-card {
  background: var(--bg-secondary);
  backdrop-filter: var(--blur-md);
  border-radius: var(--radius-xl);
  padding: var(--spacing-2xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all var(--transition-bounce);
  position: relative;
  overflow: hidden;
}

.modern-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.modern-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--primary-gradient);
}

/* 空状态样式 */
.empty-state-modern {
  text-align: center;
  padding: var(--spacing-3xl) var(--spacing-2xl);
  background: var(--bg-secondary);
  backdrop-filter: var(--blur-md);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
}

.empty-illustration-modern {
  width: 120px;
  height: 120px;
  margin: 0 auto var(--spacing-3xl);
  border-radius: var(--radius-full);
  background: var(--primary-light);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48px;
  color: var(--primary-color);
}

.empty-state-modern h2 {
  margin: 0 0 var(--spacing-md) 0;
  font-size: var(--text-2xl);
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.empty-state-modern p {
  margin: 0 0 var(--spacing-3xl) 0;
  color: var(--text-secondary);
  font-size: var(--text-base);
}

/* 详情网格样式 */
.detail-grid-modern {
  display: grid;
  gap: var(--spacing-lg);
}

.detail-grid-2 {
  grid-template-columns: repeat(2, 1fr);
}

.detail-grid-3 {
  grid-template-columns: repeat(3, 1fr);
}

.detail-grid-4 {
  grid-template-columns: repeat(4, 1fr);
}

.detail-item-modern {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
  transition: all var(--transition-normal);
}

.detail-item-modern:hover {
  background: var(--bg-primary);
  transform: translateY(-1px);
}

.detail-icon-modern {
  width: 36px;
  height: 36px;
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--text-base);
  color: white;
  flex-shrink: 0;
}

.detail-icon-primary {
  background: var(--primary-gradient);
}

.detail-icon-secondary {
  background: var(--secondary-gradient);
}

.detail-icon-accent-1 {
  background: var(--accent-gradient-1);
}

.detail-icon-accent-2 {
  background: var(--accent-gradient-2);
}

.detail-icon-accent-3 {
  background: var(--accent-gradient-3);
}

.detail-info-modern {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.detail-label-modern {
  font-size: var(--text-xs);
  color: var(--text-secondary);
  line-height: 1;
}

.detail-value-modern {
  font-size: var(--text-sm);
  color: var(--text-primary);
  font-weight: var(--font-medium);
  line-height: 1.4;
}

/* 筛选区域样式 */
.filter-container-modern {
  padding: 0 var(--spacing-3xl) var(--spacing-2xl);
  position: relative;
  z-index: var(--z-sticky);
}

.filter-card-modern {
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  background: var(--bg-secondary);
  backdrop-filter: var(--blur-md);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.filter-section-modern {
  padding: var(--spacing-sm);
}

.filter-row-modern {
  display: flex;
  align-items: center;
  gap: var(--spacing-2xl);
  flex-wrap: wrap;
}

.filter-item-modern {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.filter-item-modern label {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--text-secondary);
}

.filter-actions-modern {
  display: flex;
  gap: var(--spacing-md);
  margin-left: auto;
}

/* 分页样式 */
.pagination-container-modern {
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-3xl);
  padding: var(--spacing-2xl);
  background: var(--bg-secondary);
  backdrop-filter: var(--blur-md);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
}

/* 标签样式 */
.tag-modern {
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  padding: var(--spacing-xs) var(--spacing-md);
  border-radius: var(--radius-md);
  border: none;
}

.tag-primary {
  background: var(--primary-light);
  color: var(--primary-color);
}

.tag-secondary {
  background: rgba(249, 147, 251, 0.1);
  color: #f093fb;
}

.tag-success {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.tag-warning {
  background: rgba(250, 140, 22, 0.1);
  color: #fa8c16;
}

.tag-error {
  background: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
}

/* 头像样式 */
.avatar-modern {
  border: 4px solid rgba(255, 255, 255, 0.8);
  box-shadow: var(--shadow-md);
  background: var(--primary-gradient);
  font-weight: var(--font-semibold);
  color: white;
}

/* 输入框样式 */
.input-modern {
  border-radius: var(--radius-md);
  border: 1px solid var(--border-light);
  transition: all var(--transition-normal);
}

.input-modern:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px var(--primary-light);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .detail-grid-4 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .detail-grid-3 {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .cards-grid-4 {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
}

@media (max-width: 768px) {
  .page-header-modern {
    padding: var(--spacing-2xl) var(--spacing-xl);
    border-radius: 0;
  }
  
  .page-header-modern .header-content {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-xl);
  }
  
  .page-header-modern .title-section h1 {
    font-size: var(--text-2xl);
  }
  
  .stats-container {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .filter-row-modern {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-lg);
  }
  
  .filter-actions-modern {
    margin-left: 0;
  }
  
  .detail-grid-2,
  .detail-grid-3,
  .detail-grid-4 {
    grid-template-columns: 1fr;
  }
  
  .cards-grid-2,
  .cards-grid-3,
  .cards-grid-4 {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .empty-illustration-modern {
    width: 100px;
    height: 100px;
    font-size: 40px;
  }
}

@media (max-width: 480px) {
  .page-header-modern {
    padding: var(--spacing-xl) var(--spacing-lg);
  }
  
  .page-header-modern .title-section h1 {
    font-size: var(--text-xl);
  }
  
  .modern-card {
    padding: var(--spacing-xl);
  }
  
  .detail-item-modern {
    padding: var(--spacing-md);
  }
  
  .detail-icon-modern {
    width: 32px;
    height: 32px;
    font-size: var(--text-sm);
  }
  
  .filter-container-modern {
    padding: 0 var(--spacing-lg) var(--spacing-xl);
  }
  
  .empty-state-modern {
    padding: var(--spacing-2xl) var(--spacing-lg);
  }
}
