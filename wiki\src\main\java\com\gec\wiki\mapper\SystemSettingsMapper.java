package com.gec.wiki.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.gec.wiki.pojo.SystemSettings;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 系统设置Mapper
 */
@Mapper
public interface SystemSettingsMapper extends BaseMapper<SystemSettings> {
    
    /**
     * 根据设置键获取设置值
     */
    @Select("SELECT setting_value FROM system_settings WHERE setting_key = #{settingKey}")
    String getSettingValue(@Param("settingKey") String settingKey);
    
}
