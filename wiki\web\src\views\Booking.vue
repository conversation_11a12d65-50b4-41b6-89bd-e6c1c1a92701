<template>
  <div class="booking-container">
    <div class="booking-content">
      <div class="booking-header">
        <h1>预约服务</h1>
        <p>请填写预约信息，我们将为您安排专业的维修服务</p>
      </div>

      <a-card class="booking-form-card">
        <a-form
          :model="bookingForm"
          :rules="bookingRules"
          @finish="handleSubmit"
          layout="vertical"
          class="booking-form"
        >
          <!-- 维修店选择 -->
          <div class="form-item">
            <label class="form-label">选择维修店 <span class="required">*</span></label>
            <div class="shop-search-container">
              <input
                type="text"
                v-model="bookingForm.shopName"
                placeholder="请输入维修店名称进行搜索，或点击查看所有维修店"
                class="form-input"
                @input="handleShopSearchInput"
                @focus="handleShopFocus"
                @blur="handleShopBlur"
              />
              <button type="button" class="clear-btn" v-if="bookingForm.shopName" @click="clearShopSelection">✕</button>
              
              <!-- 加载状态 -->
              <div v-if="showShopDropdown && shopsLoading" class="shop-dropdown">
                <div class="loading-item">
                  <a-spin size="small" />
                  <span style="margin-left: 8px;">正在加载维修店列表...</span>
                </div>
              </div>

              <!-- 搜索结果下拉列表 -->
              <div v-else-if="showShopDropdown && shopOptions.length > 0" class="shop-dropdown">
                <div
                  v-for="shop in shopOptions"
                  :key="shop.value"
                  class="shop-option"
                  @click="handleShopSelect(shop.value, shop)"
                >
                  <div class="shop-name">{{ shop.label }}</div>
                  <div class="shop-details">
                    <span v-if="shop.address" class="shop-address">📍 {{ shop.address }}</span>
                    <span v-if="shop.phone" class="shop-phone">📞 {{ shop.phone }}</span>
                  </div>
                </div>
              </div>

              <!-- 没有搜索结果时的提示 -->
              <div v-else-if="showShopDropdown && shopOptions.length === 0 && bookingForm.shopName.trim() && !shopsLoading" class="no-results">
                没有找到匹配的维修店
              </div>

              <!-- 初始加载失败提示 -->
              <div v-else-if="showShopDropdown && allShops.length === 0 && !shopsLoading && !bookingForm.shopName.trim()" class="error-message">
                <span>⚠️ 获取维修店列表失败，请</span>
                <a-button type="link" size="small" @click="initAllShops">重新加载</a-button>
              </div>
            </div>
            
            <div class="form-tips">
              <span v-if="!bookingForm.shopId">💡 输入维修店名称搜索，或点击查看所有可选维修店</span>
              <span v-else class="success-tip">✅ 已选择维修店，现在可以选择服务项目了</span>
            </div>
          </div>

          <!-- 服务选择 -->
          <div class="form-item">
            <label class="form-label">选择服务 <span class="required">*</span></label>
            <select 
              v-model="bookingForm.serviceId" 
              class="form-select"
              :disabled="!bookingForm.shopId || services.length === 0"
              @change="onServiceChange"
            >
              <option value="" disabled>
                {{ !bookingForm.shopId ? '请先选择维修店' : (services.length === 0 ? '该维修店暂无可用服务' : '请选择服务项目') }}
              </option>
              <option 
                v-for="service in services" 
                :key="service.id" 
                :value="service.id"
              >
                {{ service.name }} - ¥{{ service.price }}
              </option>
            </select>
            <div v-if="servicesLoading" class="loading-tip">加载中...</div>
            <div class="form-tips" v-if="!bookingForm.shopId">
              <span>💡 选择维修店后，将为您展示该店的服务项目</span>
            </div>
            <div class="form-tips warning" v-else-if="bookingForm.shopId && services.length === 0 && !servicesLoading">
              <span>⚠️ 该维修店暂时没有可预约的服务项目</span>
            </div>
          </div>

          <!-- 车辆选择 -->
          <a-form-item name="vehicleId" label="选择车辆">
            <a-select
              v-model="bookingForm.vehicleId"
              :placeholder="vehicles.length === 0 ? '暂无车辆，请先添加车辆' : '请选择车辆'"
              size="large"
              :disabled="vehicles.length === 0"
              @change="(value) => console.log('车辆选择变更:', value)"
            >
              <a-select-option 
                v-for="vehicle in vehicles" 
                :key="vehicle.id" 
                :value="Number(vehicle.id)"
              >
                <span class="vehicle-option">
                  {{ vehicle.licensePlate }} - {{ vehicle.brand }} {{ vehicle.model }}
                  <a-tag v-if="vehicle.isDefault === 1" color="gold" size="small">默认</a-tag>
                </span>
              </a-select-option>
            </a-select>
            <div class="vehicle-actions">
              <a @click="showAddVehicle" class="add-vehicle-btn">
                <PlusOutlined /> 添加车辆
              </a>
              <a v-if="vehicles.length === 0" @click="goToVehicleManagement" class="manage-vehicle-btn">
                前往车辆管理
              </a>
            </div>
          </a-form-item>

          <!-- 预约时间 -->
          <a-form-item name="bookingDateTime" label="预约时间">
            <a-date-picker
              v-model="bookingForm.bookingDateTime"
              placeholder="选择日期和时间"
              size="large"
              style="width: 100%"
              :disabled-date="disabledDate"
              :showTime="{
                format: 'HH:mm',
                hourStep: 1,
                minuteStep: 30,
                hideDisabledOptions: true
              }"
              format="YYYY-MM-DD HH:mm"
              :disabled-time="disabledTime"
              :locale="locale"
              @change="onDateTimeChange"
            />
            <div class="datetime-tips">
              <span>营业时间：09:00-18:00，每30分钟一个时间段</span>
            </div>
          </a-form-item>

          <!-- 技师选择 -->
          <a-form-item name="technicianId" label="选择技师">
            <a-radio-group v-model="bookingForm.technicianId" size="large">
              <a-radio-button 
                v-for="technician in technicians" 
                :key="technician.id" 
                :value="technician.id"
                class="technician-option"
              >
                <div class="technician-info">
                  <a-avatar :src="technician.avatar" style="margin-right: 8px">
                    {{ technician.name.charAt(0) }}
                  </a-avatar>
                  <div>
                    <div class="technician-name">{{ technician.name }}</div>
                    <div class="technician-level">{{ getLevelText(technician.level) }}</div>
                  </div>
                </div>
              </a-radio-button>
            </a-radio-group>
          </a-form-item>

          <!-- 联系信息 -->
          <a-row :gutter="16">
            <a-col :span="12">
              <a-form-item name="contactName" label="联系人姓名">
                <a-input
                  v-model="bookingForm.contactName"
                  placeholder="请输入联系人姓名"
                  size="large"
                />
              </a-form-item>
            </a-col>
            <a-col :span="12">
              <a-form-item name="contactPhone" label="联系电话">
                <a-input
                  v-model="bookingForm.contactPhone"
                  placeholder="请输入联系电话"
                  size="large"
                />
              </a-form-item>
            </a-col>
          </a-row>

          <!-- 问题描述 -->
          <a-form-item name="problemDescription" label="问题描述">
            <a-textarea
              v-model="bookingForm.problemDescription"
              placeholder="请详细描述车辆问题，以便技师更好地为您服务"
              :rows="4"
              show-count
              :maxlength="500"
            />
          </a-form-item>

          <!-- 备注 -->
          <a-form-item name="remark" label="备注">
            <a-textarea
              v-model="bookingForm.remark"
              placeholder="其他需要说明的事项（可选）"
              :rows="3"
              show-count
              :maxlength="200"
            />
          </a-form-item>

          <!-- 服务费用 -->
          <div class="service-summary" v-if="selectedService">
            <a-card size="small" title="服务费用">
              <div class="fee-item">
                <span>{{ selectedService.name }}</span>
                <span class="fee">¥{{ selectedService.price }}</span>
              </div>
              <a-divider style="margin: 12px 0" />
              <div class="fee-total">
                <span>总计</span>
                <span class="total-fee">¥{{ selectedService.price }}</span>
              </div>
            </a-card>
          </div>

          <!-- 提交按钮 -->
          <a-form-item>
            <a-button
              type="primary"
              html-type="submit"
              size="large"
              :loading="loading"
              class="submit-button"
            >
              确认预约
            </a-button>
          </a-form-item>
        </a-form>
      </a-card>
    </div>

    <!-- 添加车辆模态框 -->
    <a-modal
      :open="addVehicleVisible"
      title="添加车辆"
      @ok="handleAddVehicle"
      @cancel="addVehicleVisible = false"
    >
      <a-form :model="vehicleForm" layout="vertical">
        <a-form-item label="车牌号" required>
          <a-input v-model="vehicleForm.licensePlate" placeholder="请输入车牌号" />
        </a-form-item>
        <a-form-item label="品牌">
          <a-input v-model="vehicleForm.brand" placeholder="请输入车辆品牌" />
        </a-form-item>
        <a-form-item label="型号">
          <a-input v-model="vehicleForm.model" placeholder="请输入车辆型号" />
        </a-form-item>
        <a-form-item label="颜色">
          <a-input v-model="vehicleForm.color" placeholder="请输入车辆颜色" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, computed, nextTick, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import { PlusOutlined } from '@ant-design/icons-vue';
import axios from 'axios';
import locale from 'ant-design-vue/es/date-picker/locale/zh_CN';
import 'dayjs/locale/zh-cn';

// 定义类型接口
interface Service {
  id: number;
  name: string;
  price: number;
  description?: string;
  duration?: number;
}

interface Vehicle {
  id: number;
  licensePlate: string;
  brand: string;
  model: string;
  isDefault?: number;
}

interface Technician {
  id: number;
  name: string;
  level: number;
  avatar?: string;
}


interface Shop {
  id: number;
  name: string;
  address?: string;
  phone?: string;
}

interface BookingForm {
  serviceId: number | null;
  vehicleId: number | null;
  shopId: number | null;
  shopName: string;
  technicianId: number | null;
  contactName: string;
  contactPhone: string;
  bookingDateTime: any;
  problemDescription: string;
  remark: string;
}

export default defineComponent({
  name: 'Booking',
  components: {
    PlusOutlined
  },
  setup() {
    const router = useRouter();
    const route = useRoute();
    const loading = ref(false);
    const addVehicleVisible = ref(false);

    const bookingForm = ref<BookingForm>({
      serviceId: null,
      vehicleId: null,
      shopId: null,
      shopName: '',
      technicianId: null,
      contactName: '',
      contactPhone: '',
      bookingDateTime: null,
      problemDescription: '',
      remark: ''
    });

    const vehicleForm = ref({
      licensePlate: '',
      brand: '',
      model: '',
      color: ''
    });

    const services = ref<Service[]>([]);
    const servicesLoading = ref(false);
    const vehicles = ref<Vehicle[]>([]);
    const shops = ref<Shop[]>([]);
    const shopSearchResults = ref<Shop[]>([]);
    const allShops = ref<Shop[]>([]);
    const shopsLoading = ref(false);
    const shopSearchFocused = ref(false);
    const showShopDropdown = ref(false);
    const technicians = ref<Technician[]>([]);

    const selectedService = computed(() => {
      return services.value.find(service => service.id === bookingForm.value.serviceId);
    });

    const bookingRules = {
      serviceId: [
        { required: true, message: '请选择服务项目', trigger: 'change' }
      ],
      vehicleId: [
        { required: true, message: '请选择车辆', trigger: 'change' }
      ],
      shopName: [
        { required: true, message: '请选择维修店', trigger: 'blur' }
      ],
      contactName: [
        { required: true, message: '请输入联系人姓名', trigger: 'blur' }
      ],
      contactPhone: [
        { required: true, message: '请输入联系电话', trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
      ],
      bookingDateTime: [
        { required: true, message: '请选择预约时间', trigger: 'change' }
      ],
      problemDescription: [
        { required: true, message: '请描述车辆问题', trigger: 'blur' }
      ]
    };

    // 禁用过去的日期
    const disabledDate = (current: any) => {
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      return current && current < today;
    };

    // 禁用时间段（营业时间外）
    const disabledTime = (current: any) => {
      if (!current) return {};
      
      const hour = current.hour();
      const minute = current.minute();
      
      return {
        disabledHours: () => {
          const hours = [];
          // 营业时间 9:00-18:00
          for (let i = 0; i < 9; i++) {
            hours.push(i);
          }
          for (let i = 18; i < 24; i++) {
            hours.push(i);
          }
          return hours;
        },
        disabledMinutes: (selectedHour: number) => {
          const minutes = [];
          // 只允许整点和半点（0分和30分）
          for (let i = 1; i < 60; i++) {
            if (i !== 30) {
              minutes.push(i);
            }
          }
          return minutes;
        }
      };
    };

    const getLevelText = (level: number) => {
      const levels: { [key: number]: string } = { 1: '初级技师', 2: '中级技师', 3: '高级技师', 4: '专家技师' };
      return levels[level] || '技师';
    };

    const onServiceChange = () => {
      // 服务变更时清空技师选择
      bookingForm.value.technicianId = null;
      loadTechnicians();
    };

    const onDateTimeChange = () => {
      // 日期时间变更时的处理
      console.log('预约时间已选择:', bookingForm.value.bookingDateTime);
    };

    // 维修店搜索相关
    const shopOptions = computed(() => {
      const shopsToShow = shopSearchResults.value.length > 0 ? shopSearchResults.value : 
                         (shopSearchFocused.value || !bookingForm.value.shopName.trim()) ? allShops.value : [];
      
      return shopsToShow.map(shop => ({
        value: shop.id,
        label: shop.name,
        address: shop.address,
        phone: shop.phone
      }));
    });

    // 从数据库获取所有维修店数据
    const initAllShops = async () => {
      shopsLoading.value = true;
      try {
        console.log('开始加载维修店列表...');
        const response = await axios.get('/shop/list');

        console.log('API响应:', response.data);

        // 改进响应判断逻辑
        if (response.data && response.data.success !== false) {
          const content = response.data.content || response.data;
          if (Array.isArray(content)) {
            allShops.value = content;
            console.log('成功加载维修店列表:', allShops.value.length, '家');
          } else {
            console.warn('维修店数据格式不正确:', content);
            allShops.value = [];
            message.warning('维修店数据格式异常，请联系管理员');
          }
        } else {
          const errorMsg = (response.data && response.data.message) || '获取维修店列表失败';
          console.error('获取维修店列表失败:', errorMsg);
          message.error(`获取维修店列表失败：${errorMsg}`);
          allShops.value = [];
        }
      } catch (error: any) {
        console.error('加载维修店列表错误:', error);
        const errorMsg = (error.response && error.response.data && error.response.data.message) || error.message || '网络连接异常';
        message.error(`无法获取维修店列表：${errorMsg}`);
        allShops.value = [];
      } finally {
        shopsLoading.value = false;
      }
    };

    const handleShopSearch = async (searchText: string) => {
      console.log('搜索维修店:', searchText);
      
      if (!searchText || searchText.trim().length === 0) {
        // 如果搜索框为空，清空搜索结果，显示所有维修店
        shopSearchResults.value = [];
        return;
      }

      try {
        // 调用后端搜索API
        const response = await axios.get('/shop/search', {
          params: {
            name: searchText.trim(),
            page: 1,
            size: 50
          }
        });

        if (response.data.success !== false && response.data.content) {
          shopSearchResults.value = response.data.content;
          console.log('API搜索结果:', shopSearchResults.value.length, '家维修店');
        } else {
          // 如果API调用失败，使用本地数据进行备选搜索
          console.warn('API搜索失败，使用本地数据搜索');
          const filteredShops = allShops.value.filter(shop => 
            shop.name.toLowerCase().includes(searchText.toLowerCase()) ||
            (shop.address && shop.address.toLowerCase().includes(searchText.toLowerCase()))
          );
          shopSearchResults.value = filteredShops;
        }
      } catch (error) {
        console.error('搜索维修店API错误:', error);
        // API调用失败时，使用本地数据进行备选搜索
        const filteredShops = allShops.value.filter(shop => 
          shop.name.toLowerCase().includes(searchText.toLowerCase()) ||
          (shop.address && shop.address.toLowerCase().includes(searchText.toLowerCase()))
        );
        shopSearchResults.value = filteredShops;
        console.log('本地搜索结果:', filteredShops.length, '家维修店');
      }
    };

    const handleShopSearchInput = (event: Event) => {
      const searchText = (event.target as HTMLInputElement).value;
      handleShopSearch(searchText);
    };

    const handleShopFocus = () => {
      console.log('维修店输入框获得焦点');
      shopSearchFocused.value = true;
      showShopDropdown.value = true;
      // 如果没有搜索文本，显示所有维修店
      if (!bookingForm.value.shopName.trim()) {
        shopSearchResults.value = [];
      }
    };

    const handleShopBlur = () => {
      console.log('维修店输入框失去焦点');
      // 延迟隐藏，避免选择选项时立即隐藏
      setTimeout(() => {
        shopSearchFocused.value = false;
        showShopDropdown.value = false;
      }, 200);
    };

    const clearShopSelection = () => {
      bookingForm.value.shopId = null;
      bookingForm.value.shopName = '';
      bookingForm.value.serviceId = null;
      bookingForm.value.technicianId = null;
      services.value = [];
      technicians.value = [];
      shopSearchResults.value = [];
      showShopDropdown.value = false;
    };

    const handleShopSelect = (value: any, option: any) => {
      const previousShopId = bookingForm.value.shopId;
      bookingForm.value.shopId = option.value;
      bookingForm.value.shopName = option.label;
      
      // 清空之前选择的服务和技师
      if (previousShopId !== option.value) {
        bookingForm.value.serviceId = null;
        bookingForm.value.technicianId = null;
        technicians.value = [];
      }
      
      console.log('选择的维修店:', { id: option.value, name: option.label });
      
      // 加载该维修店的服务项目
      loadServices(option.value);
    };

    const showAddVehicle = () => {
      addVehicleVisible.value = true;
    };

    const goToVehicleManagement = () => {
      router.push('/owner/vehicles');
    };

    const loadServices = async (shopId?: number) => {
      if (!shopId) {
        services.value = [];
        return;
      }
      
      try {
        servicesLoading.value = true;
        // 根据维修店ID获取服务列表
        const response = await axios.get(`/service/getAllServiceList`, {
          params: {
            shopId: shopId,
            status: 1 // 只获取上架的服务
          }
        });
        
        if (response.data.success !== false && response.data.content) {
          services.value = response.data.content || [];
        } else {
          console.warn('获取维修店服务失败，该维修店可能暂无服务项目');
          services.value = [];
        }
        
        console.log(`为维修店 ${shopId} 加载了 ${services.value.length} 个服务项目`);
      } catch (error) {
        console.error('Error loading services for shop:', error);
        message.error('获取维修店服务失败，请重试');
        services.value = [];
      } finally {
        servicesLoading.value = false;
      }
    };


    const loadVehicles = async () => {
      try {
        // 获取当前用户信息
        const userInfoStr = localStorage.getItem('userInfo');
        if (!userInfoStr) {
          message.warning('请先登录');
          router.push('/login');
          return;
        }

        const userInfo = JSON.parse(userInfoStr);
        
        // 调用获取用户车辆列表API
        const response = await axios.get('/vehicle/getVehicleListByPage', {
          params: {
            page: 1,
            size: 100, // 获取所有车辆
            userId: userInfo.id
          }
        });

        if (response.data.success !== false) {
          const vehicleList = response.data.content.list || [];
          vehicles.value = vehicleList.map((vehicle: any) => ({
            id: vehicle.id,
            licensePlate: vehicle.licensePlate,
            brand: vehicle.brand,
            model: vehicle.model,
            isDefault: vehicle.isDefault
          }));
          
          // 自动选择默认车辆
          console.log('当前车辆列表:', vehicles.value);
          console.log('当前选择的vehicleId:', bookingForm.value.vehicleId);
          
          const defaultVehicle = vehicles.value.find(v => v.isDefault === 1);
          console.log('找到的默认车辆:', defaultVehicle);
          
          if (defaultVehicle && !bookingForm.value.vehicleId) {
            // 确保数据类型匹配，转换为数字
            bookingForm.value.vehicleId = Number(defaultVehicle.id);
            console.log(`设置vehicleId为: ${bookingForm.value.vehicleId}，类型: ${typeof bookingForm.value.vehicleId}`);
            console.log(`自动选择默认车辆: ${defaultVehicle.licensePlate} - ${defaultVehicle.brand} ${defaultVehicle.model}`);
            message.success(`已自动选择默认车辆：${defaultVehicle.licensePlate}`, 2);
            
            // 使用nextTick确保DOM更新
            await nextTick(() => {
              console.log('DOM更新后的vehicleId:', bookingForm.value.vehicleId);
            });
          } else if (!defaultVehicle && vehicles.value.length > 0 && !bookingForm.value.vehicleId) {
            // 如果没有设置默认车辆但有车辆，自动选择第一辆
            const firstVehicle = vehicles.value[0];
            bookingForm.value.vehicleId = Number(firstVehicle.id);
            console.log(`设置vehicleId为: ${bookingForm.value.vehicleId}，类型: ${typeof bookingForm.value.vehicleId}`);
            console.log(`自动选择第一辆车辆: ${firstVehicle.licensePlate} - ${firstVehicle.brand} ${firstVehicle.model}`);
            message.info(`已自动选择车辆：${firstVehicle.licensePlate}（建议在车辆管理中设置默认车辆）`, 3);
            
            // 使用nextTick确保DOM更新
            await nextTick(() => {
              console.log('DOM更新后的vehicleId:', bookingForm.value.vehicleId);
            });
          }
          
          if (vehicleList.length === 0) {
            message.info('您还没有添加车辆，请先添加车辆信息');
          }
        } else {
          message.error(response.data.message || '获取车辆列表失败');
          vehicles.value = [];
        }
      } catch (error) {
        console.error('Load vehicles error:', error);
        message.error('加载车辆列表失败，请重试');
        vehicles.value = [];
      }
    };

    const loadTechnicians = async () => {
      try {
        // TODO: 根据服务类型加载合适的技师
        // 临时数据
        technicians.value = [
          { id: 1, name: '张师傅', level: 4, avatar: '' },
          { id: 2, name: '李师傅', level: 3, avatar: '' },
          { id: 3, name: '王师傅', level: 2, avatar: '' }
        ];
      } catch (error) {
        message.error('加载技师列表失败');
      }
    };


    const handleAddVehicle = async () => {
      try {
        // 获取当前用户信息
        const userInfoStr = localStorage.getItem('userInfo');
        if (!userInfoStr) {
          message.warning('请先登录');
          return;
        }

        const userInfo = JSON.parse(userInfoStr);
        
        // 基本验证
        if (!vehicleForm.value.licensePlate.trim()) {
          message.error('请输入车牌号');
          return;
        }
        
        if (!vehicleForm.value.brand.trim()) {
          message.error('请输入车辆品牌');
          return;
        }

        // 调用添加车辆API
        const response = await axios.post('/vehicle/save', {
          licensePlate: vehicleForm.value.licensePlate.trim(),
          brand: vehicleForm.value.brand.trim(),
          model: vehicleForm.value.model.trim() || null,
          color: vehicleForm.value.color.trim() || null,
          mileage: 0,
          isDefault: vehicles.value.length === 0 ? 1 : 0, // 如果是第一辆车，设为默认
          status: 1,
          userId: userInfo.id
        });

        if (response.data.success !== false) {
          message.success('车辆添加成功');
          addVehicleVisible.value = false;
          
          // 重置表单
          vehicleForm.value = {
            licensePlate: '',
            brand: '',
            model: '',
            color: ''
          };
          
          // 重新加载车辆列表
          await loadVehicles();
          
          // 如果这是用户的第一辆车，自动选择它
          if (vehicles.value.length === 1 && !bookingForm.value.vehicleId) {
            bookingForm.value.vehicleId = Number(vehicles.value[0].id);
            console.log(`新增车辆后自动选择: vehicleId=${bookingForm.value.vehicleId}`);
            message.success(`已自动选择车辆：${vehicles.value[0].licensePlate}`, 2);
          }
        } else {
          message.error(response.data.message || '添加车辆失败');
        }
      } catch (error) {
        console.error('Add vehicle error:', error);
        message.error('添加车辆失败，请重试');
      }
    };

    const handleSubmit = async () => {
      if (!localStorage.getItem('token')) {
        message.warning('请先登录');
        router.push('/login');
        return;
      }

      loading.value = true;
      try {
        const formatDateTime = (datetime: any): { date: string, time: string } => {
          if (!datetime) return { date: '', time: '' };
          
          let dateObj;
          if (datetime instanceof Date) {
            dateObj = datetime;
          } else if (typeof datetime === 'object' && datetime.format) {
            // Ant Design Vue日期对象处理
            return {
              date: datetime.format('YYYY-MM-DD'),
              time: datetime.format('HH:mm')
            };
          } else if (typeof datetime === 'string') {
            dateObj = new Date(datetime);
          } else {
            return { date: '', time: '' };
          }
          
          const year = dateObj.getFullYear();
          const month = String(dateObj.getMonth() + 1).padStart(2, '0');
          const day = String(dateObj.getDate()).padStart(2, '0');
          const hour = String(dateObj.getHours()).padStart(2, '0');
          const minute = String(dateObj.getMinutes()).padStart(2, '0');
          
          return {
            date: `${year}-${month}-${day}`,
            time: `${hour}:${minute}`
          };
        };
        
        const { date, time } = formatDateTime(bookingForm.value.bookingDateTime);
          
        const data = {
          ...bookingForm.value,
          bookingDate: date,
          bookingTime: time
        };

        // 删除合并后不需要的字段
        delete (data as any).bookingDateTime;

        // 验证必填字段
        if (!data.shopId || !data.shopName.trim()) {
          message.error('请选择维修店');
          loading.value = false;
          return;
        }

        const response = await axios.post('/booking/create', data);
        
        if (response.data.success) {
          message.success('预约成功！我们将尽快联系您确认服务时间。');
          router.push('/');
        } else {
          message.error(response.data.message || '预约失败');
        }
      } catch (error) {
        message.error('预约失败，请检查网络连接');
      } finally {
        loading.value = false;
      }
    };

    // 从本地存储获取用户信息填充联系人
    const loadUserInfo = () => {
      const userInfoStr = localStorage.getItem('userInfo');
      if (userInfoStr) {
        try {
          const userInfo = JSON.parse(userInfoStr);
          bookingForm.value.contactName = userInfo.realName || '';
          bookingForm.value.contactPhone = userInfo.phone || '';
        } catch (error) {
          console.error('Parse user info error:', error);
        }
      }
    };

    // 监视车辆ID变化
    watch(() => bookingForm.value.vehicleId, (newValue, oldValue) => {
      console.log(`车辆ID从 ${oldValue} 变更为 ${newValue}`);
      if (newValue && vehicles.value.length > 0) {
        const selectedVehicle = vehicles.value.find(v => Number(v.id) === Number(newValue));
        if (selectedVehicle) {
          console.log(`选中的车辆: ${selectedVehicle.licensePlate} - ${selectedVehicle.brand} ${selectedVehicle.model}`);
        }
      }
    }, { immediate: true });

    onMounted(async () => {
      // 初始化维修店数据
      await initAllShops();
      // 不再自动加载服务，等待选择维修店后再加载
      await loadVehicles(); // 等待车辆加载完成，确保自动选择能正常工作
      loadTechnicians();
      loadUserInfo();
    });

    return {
      bookingForm,
      vehicleForm,
      bookingRules,
      loading,
      addVehicleVisible,
      services,
      servicesLoading,
      vehicles,
      shops,
      shopSearchResults,
      allShops,
      shopsLoading,
      shopSearchFocused,
      showShopDropdown,
      shopOptions,
      technicians,
      selectedService,
      disabledDate,
      disabledTime,
      locale,
      getLevelText,
      onServiceChange,
      onDateTimeChange,
      initAllShops,
      handleShopSearch,
      handleShopSearchInput,
      handleShopFocus,
      handleShopBlur,
      handleShopSelect,
      clearShopSelection,
      showAddVehicle,
      goToVehicleManagement,
      handleAddVehicle,
      handleSubmit
    };
  }
});
</script>

<style scoped>
.booking-container {
  min-height: 100vh;
  background: #f0f2f5;
  padding: 24px;
}

.booking-content {
  max-width: 800px;
  margin: 0 auto;
}

.booking-header {
  text-align: center;
  margin-bottom: 32px;
}

.booking-header h1 {
  font-size: 32px;
  color: #333;
  margin-bottom: 8px;
}

.booking-header p {
  color: #666;
  font-size: 16px;
}

.booking-form-card {
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.service-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.service-name {
  font-weight: 500;
}

.service-price {
  color: #ff4d4f;
  font-weight: bold;
}

.vehicle-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.vehicle-actions {
  margin-top: 8px;
  display: flex;
  gap: 16px;
}

.add-vehicle-btn,
.manage-vehicle-btn {
  color: #1890ff;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.add-vehicle-btn:hover,
.manage-vehicle-btn:hover {
  text-decoration: underline;
}

.datetime-tips {
  margin-top: 8px;
  padding: 8px 12px;
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 6px;
  font-size: 12px;
  color: #52c41a;
}

.shop-search-tips {
  margin-top: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  transition: all 0.3s ease;
}

/* 原生表单样式 */
.form-item {
  margin-bottom: 24px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.required {
  color: #ff4d4f;
}

.form-input,
.form-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.form-input:disabled,
.form-select:disabled {
  background-color: #f5f5f5;
  color: #bfbfbf;
  cursor: not-allowed;
}

.shop-search-container {
  position: relative;
}

.clear-btn {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #bfbfbf;
  cursor: pointer;
  font-size: 14px;
  padding: 4px;
}

.clear-btn:hover {
  color: #8c8c8c;
}

.shop-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
}

.loading-item {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  color: #666;
}

.error-message {
  padding: 12px 16px;
  color: #ff4d4f;
  display: flex;
  align-items: center;
  justify-content: center;
}

.no-results {
  padding: 12px 16px;
  text-align: center;
  color: #8c8c8c;
  font-size: 14px;
}

.loading-tip {
  margin-top: 8px;
  padding: 8px 12px;
  background: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 6px;
  font-size: 12px;
  color: #0ea5e9;
  text-align: center;
}

.form-tips {
  margin-top: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  background: #f0f9ff;
  border: 1px solid #bfdbfe;
  color: #0ea5e9;
}

.form-tips.warning {
  background: #fefce8;
  border: 1px solid #fde047;
  color: #ca8a04;
}

.success-tip {
  background: #f6ffed !important;
  border: 1px solid #b7eb8f !important;
  color: #52c41a !important;
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
}

.service-selection-tips {
  margin-top: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
}

.service-selection-tips {
  background: #f0f9ff;
  border: 1px solid #bfdbfe;
  color: #0ea5e9;
}

.service-selection-tips.warning {
  background: #fefce8;
  border: 1px solid #fde047;
  color: #ca8a04;
}

.shop-option {
  padding: 8px 4px;
}

.shop-name {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.shop-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.shop-address,
.shop-phone {
  font-size: 12px;
  color: #8c8c8c;
}

.shop-address {
  margin-bottom: 2px;
}

.technician-option {
  height: auto !important;
  padding: 12px 16px !important;
  margin: 8px 8px 8px 0 !important;
  border-radius: 8px !important;
}

.technician-info {
  display: flex;
  align-items: center;
}

.technician-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.technician-level {
  font-size: 12px;
  color: #666;
}

.time-unavailable {
  color: #999;
  font-size: 12px;
}

.service-summary {
  margin: 24px 0;
}

.fee-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.fee {
  color: #666;
}

.fee-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
}

.total-fee {
  color: #ff4d4f;
  font-size: 18px;
}

.submit-button {
  width: 100%;
  height: 50px;
  font-size: 16px;
  border-radius: 8px;
}
</style>
