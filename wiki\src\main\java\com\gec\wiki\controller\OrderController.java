package com.gec.wiki.controller;

import com.gec.wiki.pojo.resp.CommonResp;
import com.gec.wiki.pojo.resp.PageResp;
import com.gec.wiki.pojo.Booking;
import com.gec.wiki.service.BookingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 订单管理控制器
 */
@RestController
@RequestMapping("/api")
public class OrderController {

    private static final Logger LOG = LoggerFactory.getLogger(OrderController.class);

    @Resource
    private BookingService bookingService;

    /**
     * 获取维修店订单列表
     */
    @GetMapping("/shop/orders")
    public CommonResp<PageResp<Object>> getShopOrders(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String status) {
        
        CommonResp<PageResp<Object>> resp = new CommonResp<>();
        try {
            // TODO: 这里应该从当前登录用户会话中获取维修店ID
            // 暂时使用固定的shopId = 1L
            Long shopId = 1L;
            
            Integer statusInt = null;
            if (status != null && !status.trim().isEmpty()) {
                statusInt = Integer.parseInt(status);
            }
            
            CommonResp<Object> bookingResp = bookingService.getBookingsForShop(shopId, page, size, statusInt);
            
            if (bookingResp.isSuccess()) {
                @SuppressWarnings("unchecked")
                Map<String, Object> pageData = (Map<String, Object>) bookingResp.getContent();
                
                PageResp<Object> pageResp = new PageResp<>();
                pageResp.setTotal(Long.valueOf(pageData.get("total").toString()));
                
                // 修复类型转换问题
                @SuppressWarnings("unchecked")
                java.util.List<Object> dataList = (java.util.List<Object>) pageData.get("list");
                pageResp.setList(dataList);
                
                resp.setSuccess(true);
                resp.setContent(pageResp);
                resp.setMessage("获取订单列表成功");
                
                LOG.info("返回真实数据库订单数据，总数: {}, 页面: {}, 大小: {}", pageResp.getTotal(), page, size);
            } else {
                resp.setSuccess(false);
                resp.setMessage(bookingResp.getMessage());
            }
        } catch (Exception e) {
            LOG.error("获取订单列表失败", e);
            resp.setSuccess(false);
            resp.setMessage("获取订单列表失败");
        }
        return resp;
    }

    /**
     * 确认订单
     */
    @PostMapping("/shop/orders/{orderId}/confirm")
    public CommonResp<Void> confirmOrder(@PathVariable Long orderId) {
        CommonResp<Void> resp = new CommonResp<>();
        try {
            // TODO: 这里应该从当前登录用户会话中获取维修店ID
            // 暂时使用固定的shopId = 1L
            Long shopId = 1L;
            
            CommonResp<Object> updateResp = bookingService.updateBookingStatus(orderId, 2, shopId);
            
            if (updateResp.isSuccess()) {
                resp.setSuccess(true);
                resp.setMessage("订单确认成功");
                LOG.info("确认订单成功: {}", orderId);
            } else {
                resp.setSuccess(false);
                resp.setMessage(updateResp.getMessage());
            }
        } catch (Exception e) {
            LOG.error("确认订单失败", e);
            resp.setSuccess(false);
            resp.setMessage("确认订单失败");
        }
        return resp;
    }

    /**
     * 开始服务
     */
    @PostMapping("/shop/orders/{orderId}/start")
    public CommonResp<Void> startService(@PathVariable Long orderId) {
        CommonResp<Void> resp = new CommonResp<>();
        try {
            // TODO: 这里应该从当前登录用户会话中获取维修店ID
            // 暂时使用固定的shopId = 1L
            Long shopId = 1L;
            
            CommonResp<Object> updateResp = bookingService.updateBookingStatus(orderId, 3, shopId);
            
            if (updateResp.isSuccess()) {
                resp.setSuccess(true);
                resp.setMessage("服务已开始");
                LOG.info("开始服务成功: {}", orderId);
            } else {
                resp.setSuccess(false);
                resp.setMessage(updateResp.getMessage());
            }
        } catch (Exception e) {
            LOG.error("开始服务失败", e);
            resp.setSuccess(false);
            resp.setMessage("开始服务失败");
        }
        return resp;
    }

    /**
     * 完成服务
     */
    @PostMapping("/shop/orders/{orderId}/complete")
    public CommonResp<Void> completeService(@PathVariable Long orderId) {
        CommonResp<Void> resp = new CommonResp<>();
        try {
            // TODO: 这里应该从当前登录用户会话中获取维修店ID
            // 暂时使用固定的shopId = 1L
            Long shopId = 1L;
            
            CommonResp<Object> updateResp = bookingService.updateBookingStatus(orderId, 4, shopId);
            
            if (updateResp.isSuccess()) {
                resp.setSuccess(true);
                resp.setMessage("服务已完成");
                LOG.info("完成服务成功: {}", orderId);
            } else {
                resp.setSuccess(false);
                resp.setMessage(updateResp.getMessage());
            }
        } catch (Exception e) {
            LOG.error("完成服务失败", e);
            resp.setSuccess(false);
            resp.setMessage("完成服务失败");
        }
        return resp;
    }

    /**
     * 获取车主订单列表
     */
    @GetMapping("/owner/orders")
    public CommonResp<PageResp<Object>> getOwnerOrders(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String status) {
        
        CommonResp<PageResp<Object>> resp = new CommonResp<>();
        try {
            // 这里应该根据当前登录的车主ID获取订单
            PageResp<Object> pageResp = new PageResp<>();
            pageResp.setTotal(0L);
            pageResp.setList(java.util.Arrays.asList());
            
            resp.setSuccess(true);
            resp.setContent(pageResp);
            resp.setMessage("获取订单列表成功");
        } catch (Exception e) {
            LOG.error("获取订单列表失败", e);
            resp.setSuccess(false);
            resp.setMessage("获取订单列表失败");
        }
        return resp;
    }

    /**
     * 获取订单统计数据
     */
    @GetMapping("/shop/orders/stats")
    public CommonResp<Map<String, Object>> getOrderStats() {
        CommonResp<Map<String, Object>> resp = new CommonResp<>();
        try {
            // TODO: 这里应该从当前登录用户会话中获取维修店ID
            // 暂时使用固定的shopId = 1L
            Long shopId = 1L;
            
            CommonResp<Object> statsResp = bookingService.getBookingStatsForShop(shopId);
            
            if (statsResp.isSuccess()) {
                @SuppressWarnings("unchecked")
                Map<String, Object> stats = (Map<String, Object>) statsResp.getContent();
                
                resp.setSuccess(true);
                resp.setContent(stats);
                resp.setMessage("获取统计数据成功");
                
                LOG.info("返回真实数据库统计数据: {}", stats);
            } else {
                resp.setSuccess(false);
                resp.setMessage(statsResp.getMessage());
            }
        } catch (Exception e) {
            LOG.error("获取统计数据失败", e);
            resp.setSuccess(false);
            resp.setMessage("获取统计数据失败");
        }
        return resp;
    }
}
