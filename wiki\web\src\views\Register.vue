<template>
  <div class="register-container">
    <div class="register-box">
      <div class="register-header">
        <img src="/images/logo.png" alt="汽车维修" class="logo" />
        <h2>注册汽车维修服务平台</h2>
        <p>加入我们，享受专业的汽车维修服务</p>
      </div>
      
      <a-form
        ref="registerFormRef"
        :model="registerForm"
        :rules="registerRules"
        @finish="handleRegister"
        layout="vertical"
        class="register-form"
      >
        <!-- 用户类型选择 -->
        <a-form-item name="userType" label="用户类型">
          <a-radio-group
            v-model:value="registerForm.userType"
            size="large"
            class="user-type-radio"
          >
            <a-radio :value="1" class="radio-option">
              <div class="radio-content">
                <CarOutlined class="radio-icon" />
                <div class="radio-text">
                  <div class="radio-title">车主</div>
                  <div class="radio-desc">预约维修服务，管理车辆信息</div>
                </div>
              </div>
            </a-radio>
            <a-radio :value="2" class="radio-option">
              <div class="radio-content">
                <ShopOutlined class="radio-icon" />
                <div class="radio-text">
                  <div class="radio-title">维修店</div>
                  <div class="radio-desc">提供维修服务，管理技师和订单</div>
                </div>
              </div>
            </a-radio>
          </a-radio-group>
        </a-form-item>

        <!-- 车主用户名字段 -->
        <a-form-item 
          v-if="registerForm.userType === 1" 
          name="username" 
          label="用户名"
        >
          <a-input
            v-model:value="registerForm.username"
            size="large"
            placeholder="请输入用户名"
          >
            <template #prefix>
              <UserOutlined />
            </template>
          </a-input>
        </a-form-item>

        <!-- 维修店名字段 -->
        <a-form-item 
          v-if="registerForm.userType === 2" 
          name="shopName" 
          label="维修店名"
        >
          <a-input
            v-model:value="registerForm.shopName"
            size="large"
            placeholder="请输入维修店名称"
          >
            <template #prefix>
              <ShopOutlined />
            </template>
          </a-input>
        </a-form-item>
        
        <a-form-item name="realName" label="真实姓名">
          <a-input
            v-model:value="registerForm.realName"
            size="large"
            placeholder="请输入真实姓名"
          >
            <template #prefix>
              <IdcardOutlined />
            </template>
          </a-input>
        </a-form-item>

        <a-form-item name="phone" label="手机号">
          <a-input
            v-model:value="registerForm.phone"
            size="large"
            placeholder="请输入手机号"
          >
            <template #prefix>
              <PhoneOutlined />
            </template>
          </a-input>
        </a-form-item>

        <a-form-item name="email" label="邮箱">
          <a-input
            v-model:value="registerForm.email"
            size="large"
            placeholder="请输入邮箱（可选）"
          >
            <template #prefix>
              <MailOutlined />
            </template>
          </a-input>
        </a-form-item>

        <!-- 维修店地址字段 -->
        <a-form-item 
          v-if="registerForm.userType === 2" 
          name="shopAddress" 
          label="地铺地址"
        >
          <a-input
            v-model:value="registerForm.shopAddress"
            size="large"
            placeholder="请输入维修店地址"
          >
            <template #prefix>
              <EnvironmentOutlined />
            </template>
          </a-input>
        </a-form-item>
        
        <a-form-item name="password" label="密码">
          <a-input-password
            v-model:value="registerForm.password"
            size="large"
            placeholder="请输入密码"
            autocomplete="new-password"
          >
            <template #prefix>
              <LockOutlined />
            </template>
          </a-input-password>
        </a-form-item>

        <a-form-item name="confirmPassword" label="确认密码">
          <a-input-password
            v-model:value="registerForm.confirmPassword"
            size="large"
            placeholder="请再次输入密码"
            autocomplete="new-password"
          >
            <template #prefix>
              <LockOutlined />
            </template>
          </a-input-password>
        </a-form-item>
        
        <a-form-item>
          <a-checkbox v-model:checked="agreeTerms">
            我已阅读并同意 <a href="#" @click.prevent="showTerms">《用户协议》</a> 和 <a href="#" @click.prevent="showPrivacy">《隐私政策》</a>
          </a-checkbox>
        </a-form-item>
        
        <a-form-item>
          <a-button
            type="primary"
            html-type="submit"
            size="large"
            :loading="loading"
            :disabled="!agreeTerms"
            class="register-button"
          >
            注册
          </a-button>
        </a-form-item>
        
        <div class="login-link">
          已有账户？ 
          <router-link to="/login">立即登录</router-link>
        </div>
      </a-form>
    </div>

    <!-- 用户协议模态框 -->
    <a-modal
      v-model:visible="userAgreementVisible"
      title="用户协议"
      width="800px"
      :footer="null"
      class="agreement-modal"
    >
      <div class="agreement-content">
        <h3>汽车维修服务平台用户协议</h3>
        
        <h4>第一条 协议的范围和效力</h4>
        <p>1.1 本协议是您与汽车维修服务平台之间关于使用平台服务所订立的协议。</p>
        <p>1.2 您通过注册、登录、使用平台服务，即表示您已阅读、理解并完全接受本协议的全部条款。</p>
        
        <h4>第二条 服务内容</h4>
        <p>2.1 平台为车主用户提供汽车维修服务预约、维修店查找、服务评价等功能。</p>
        <p>2.2 平台为维修店用户提供服务发布、订单管理、客户沟通等功能。</p>
        <p>2.3 平台有权根据业务发展需要，增加、修改或终止部分服务功能。</p>
        
        <h4>第三条 用户注册和账户管理</h4>
        <p>3.1 用户应提供真实、准确、完整的注册信息。</p>
        <p>3.2 用户对账户和密码的安全负有责任，因账户被盗用造成的损失由用户自行承担。</p>
        <p>3.3 用户不得将账户转让、出售或以其他方式提供给第三方使用。</p>
        
        <h4>第四条 用户行为规范</h4>
        <p>4.1 用户应遵守国家法律法规，不得利用平台从事违法违规活动。</p>
        <p>4.2 用户不得发布虚假信息、恶意评价或进行其他损害平台声誉的行为。</p>
        <p>4.3 维修店用户应确保提供的服务符合相关技术标准和安全要求。</p>
        
        <h4>第五条 服务费用和支付</h4>
        <p>5.1 平台基础服务免费提供，部分增值服务可能收取相应费用。</p>
        <p>5.2 维修服务费用由车主与维修店直接结算，平台不参与资金交易。</p>
        <p>5.3 平台有权调整收费标准，并提前30天通知用户。</p>
        
        <h4>第六条 知识产权</h4>
        <p>6.1 平台的所有内容，包括但不限于文字、图片、软件等，均受知识产权法保护。</p>
        <p>6.2 用户在平台发布的内容，应确保不侵犯他人知识产权。</p>
        
        <h4>第七条 免责声明</h4>
        <p>7.1 平台仅提供信息发布和匹配服务，对维修服务的质量不承担直接责任。</p>
        <p>7.2 因不可抗力、系统故障等原因导致的服务中断，平台不承担责任。</p>
        
        <h4>第八条 协议修改和终止</h4>
        <p>8.1 平台有权修改本协议，修改后的协议将在平台公布。</p>
        <p>8.2 用户违反协议条款的，平台有权终止向该用户提供服务。</p>
        
        <h4>第九条 争议解决</h4>
        <p>9.1 因本协议产生的争议，双方应友好协商解决。</p>
        <p>9.2 协商不成的，任何一方均可向平台所在地人民法院提起诉讼。</p>
        
        <p class="effective-date">本协议自2024年1月1日起生效。</p>
      </div>
      
      <div class="modal-footer">
        <a-button type="primary" @click="userAgreementVisible = false">
          我已阅读并同意
        </a-button>
        <a-button @click="userAgreementVisible = false" style="margin-left: 8px;">
          关闭
        </a-button>
      </div>
    </a-modal>

    <!-- 隐私政策模态框 -->
    <a-modal
      v-model:visible="privacyPolicyVisible"
      title="隐私政策"
      width="800px"
      :footer="null"
      class="agreement-modal"
    >
      <div class="agreement-content">
        <h3>汽车维修服务平台隐私政策</h3>
        
        <h4>第一条 信息收集</h4>
        <p>1.1 我们收集您主动提供的信息：</p>
        <ul>
          <li>注册信息：用户名、真实姓名、手机号、邮箱等</li>
          <li>车主用户：车辆信息、维修历史等</li>
          <li>维修店用户：店铺信息、服务项目、营业执照等</li>
        </ul>
        
        <p>1.2 我们自动收集的信息：</p>
        <ul>
          <li>设备信息：设备型号、操作系统、应用版本等</li>
          <li>日志信息：IP地址、访问时间、操作记录等</li>
          <li>位置信息：用于提供附近维修店推荐服务</li>
        </ul>
        
        <h4>第二条 信息使用</h4>
        <p>2.1 提供和改善服务：</p>
        <ul>
          <li>为您匹配合适的维修服务</li>
          <li>处理您的服务请求和投诉</li>
          <li>改善用户体验和服务质量</li>
        </ul>
        
        <p>2.2 安全保护：</p>
        <ul>
          <li>验证用户身份，防止欺诈行为</li>
          <li>检测和预防安全威胁</li>
          <li>维护平台运行安全</li>
        </ul>
        
        <p>2.3 法律义务：</p>
        <ul>
          <li>遵守适用的法律法规要求</li>
          <li>配合监管部门的合法调查</li>
        </ul>
        
        <h4>第三条 信息共享</h4>
        <p>3.1 我们不会向第三方出售、租赁或交易您的个人信息。</p>
        <p>3.2 在以下情况下，我们可能共享您的信息：</p>
        <ul>
          <li>获得您的明确同意</li>
          <li>为完成服务匹配（如向维修店提供车主联系方式）</li>
          <li>法律法规要求或政府部门要求</li>
          <li>保护平台和用户的合法权益</li>
        </ul>
        
        <h4>第四条 信息存储和安全</h4>
        <p>4.1 数据存储：</p>
        <ul>
          <li>您的信息将存储在中国境内的服务器上</li>
          <li>我们将在必要期限内保留您的信息</li>
        </ul>
        
        <p>4.2 安全措施：</p>
        <ul>
          <li>采用行业标准的加密技术保护数据传输</li>
          <li>建立严格的数据访问权限控制</li>
          <li>定期进行安全评估和漏洞检测</li>
        </ul>
        
        <h4>第五条 您的权利</h4>
        <p>5.1 您有权：</p>
        <ul>
          <li>查询、更正您的个人信息</li>
          <li>删除您的个人信息（法律法规另有规定的除外）</li>
          <li>撤回您的信息使用同意</li>
          <li>要求我们停止处理您的个人信息</li>
        </ul>
        
        <p>5.2 行使权利方式：</p>
        <ul>
          <li>通过平台设置页面进行操作</li>
          <li>联系客服热线：400-123-4567</li>
          <li>发送邮件至：<EMAIL></li>
        </ul>
        
        <h4>第六条 未成年人保护</h4>
        <p>6.1 我们不会主动收集未满18周岁的未成年人个人信息。</p>
        <p>6.2 如发现收集了未成年人信息，我们将立即删除相关数据。</p>
        
        <h4>第七条 政策更新</h4>
        <p>7.1 我们可能不时更新本隐私政策。</p>
        <p>7.2 重大变更将通过平台公告或其他方式通知您。</p>
        <p>7.3 继续使用服务即视为您接受更新后的政策。</p>
        
        <h4>第八条 联系我们</h4>
        <p>如果您对本隐私政策有任何疑问，请通过以下方式联系我们：</p>
        <ul>
          <li>客服热线：400-123-4567</li>
          <li>邮箱：<EMAIL></li>
          <li>地址：北京市朝阳区某某街道123号</li>
        </ul>
        
        <p class="effective-date">本政策自2024年1月1日起生效。</p>
      </div>
      
      <div class="modal-footer">
        <a-button type="primary" @click="privacyPolicyVisible = false">
          我已阅读并了解
        </a-button>
        <a-button @click="privacyPolicyVisible = false" style="margin-left: 8px;">
          关闭
        </a-button>
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, watch } from 'vue';
import { 
  UserOutlined, 
  LockOutlined, 
  PhoneOutlined, 
  MailOutlined,
  IdcardOutlined,
  CarOutlined,
  ShopOutlined,
  EnvironmentOutlined
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import axios from 'axios';
import { useRouter } from 'vue-router';

export default defineComponent({
  name: 'Register',
  components: {
    UserOutlined,
    LockOutlined,
    PhoneOutlined,
    MailOutlined,
    IdcardOutlined,
    CarOutlined,
    ShopOutlined,
    EnvironmentOutlined
  },
  setup() {
    const router = useRouter();
    const loading = ref(false);
    const agreeTerms = ref(false);
    const userAgreementVisible = ref(false);
    const privacyPolicyVisible = ref(false);
    const registerFormRef = ref();

    const registerForm = ref({
      username: '',
      shopName: '',
      realName: '',
      phone: '',
      email: '',
      shopAddress: '',
      password: '',
      confirmPassword: '',
      userType: 1 // 默认选择车主
    });

    // 安全设置
    const securitySettings = ref({
      minPasswordLength: 6,
      requireComplexPassword: false
    });
    
    // 加载安全设置
    const loadSecuritySettings = async () => {
      try {
        const response = await axios.get('/api/admin/security-settings');
        if (response.data.success) {
          securitySettings.value = response.data.content;
        }
      } catch (error) {
        console.error('加载安全设置失败:', error);
      }
    };

    
    // 创建验证规则
    const createValidationRules = () => ({
      realName: [
        { required: true, message: '请输入真实姓名', trigger: 'blur' }
      ],
      phone: [
        { required: true, message: '请输入手机号', trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
      ],
      email: [
        { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
      ],
      userType: [
        { required: true, message: '请选择用户类型', trigger: 'change' }
      ],
      password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
      ],
        confirmPassword: [
          { required: true, message: '请确认密码', trigger: 'blur' },
          {
            validator: (_rule: any, value: string) => {
              if (!value) return Promise.resolve();
              if (!registerForm.value.password) return Promise.resolve();
              if (value !== registerForm.value.password) {
                return Promise.reject(new Error('两次输入的密码不一致'));
              }
              return Promise.resolve();
            },
            trigger: ['blur', 'change']
          }
        ],
      username: [
        { required: true, message: '请输入用户名', trigger: 'blur' },
        { min: 3, max: 20, message: '用户名长度为3-20个字符', trigger: 'blur' }
      ],
      shopName: [
        { required: true, message: '请输入维修店名', trigger: 'blur' },
        { min: 2, max: 50, message: '维修店名长度为2-50个字符', trigger: 'blur' }
      ],
      shopAddress: [
        { required: true, message: '请输入地铺地址', trigger: 'blur' },
        { min: 5, max: 200, message: '地址长度为5-200个字符', trigger: 'blur' }
      ]
    });

    const registerRules = ref(createValidationRules());
    
    const handleRegister = async () => {
      if (!agreeTerms.value) {
        message.warning('请先同意用户协议和隐私政策');
        return;
      }
      
      loading.value = true;
      try {
        // 根据用户类型准备不同的数据
        const requestData: any = {
          realName: registerForm.value.realName,
          phone: registerForm.value.phone,
          email: registerForm.value.email,
          password: registerForm.value.password,
          confirmPassword: registerForm.value.confirmPassword,
          userType: registerForm.value.userType
        };

        if (registerForm.value.userType === 1) {
          // 车主用户
          requestData.username = registerForm.value.username;
        } else if (registerForm.value.userType === 2) {
          // 维修店用户
          requestData.shopName = registerForm.value.shopName;
          requestData.shopAddress = registerForm.value.shopAddress;
          requestData.username = registerForm.value.shopName; // 使用店名作为用户名
        }

        const response = await axios.post('/auth/register', requestData);
        const data = response.data;
        
        if (data.success) {
          message.success('注册成功，请登录');
          router.push('/login');
        } else {
          message.error(data.message || '注册失败');
        }
      } catch (error) {
        message.error('注册失败，请检查网络连接');
        console.error('Register error:', error);
      } finally {
        loading.value = false;
      }
    };
    
    const showTerms = () => {
      userAgreementVisible.value = true;
    };
    
    const showPrivacy = () => {
      privacyPolicyVisible.value = true;
    };

    // 监听密码变化，重新验证确认密码
    watch(
      () => registerForm.value.password,
      () => {
        // 当密码改变时，如果确认密码已有值，则重新验证确认密码
        if (registerForm.value.confirmPassword && registerFormRef.value) {
          registerFormRef.value.validateFields(['confirmPassword']);
        }
      }
    );

    // 监听确认密码变化，实时验证
    watch(
      () => registerForm.value.confirmPassword,
      () => {
        // 当确认密码改变时，如果密码已有值，则重新验证确认密码
        if (registerForm.value.password && registerForm.value.confirmPassword && registerFormRef.value) {
          registerFormRef.value.validateFields(['confirmPassword']);
        }
      }
    );

    // 组件挂载时加载安全设置
    onMounted(() => {
      loadSecuritySettings();
    });

    return {
      registerForm,
      registerFormRef,
      registerRules,
      loading,
      agreeTerms,
      securitySettings,
      userAgreementVisible,
      privacyPolicyVisible,
      handleRegister,
      showTerms,
      showPrivacy
    };
  }
});
</script>

<style scoped>
.register-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.register-box {
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 450px;
  max-height: 90vh;
  overflow-y: auto;
}

.register-header {
  text-align: center;
  margin-bottom: 32px;
}

.logo {
  width: 60px;
  height: 60px;
  margin-bottom: 16px;
}

.register-header h2 {
  color: #333;
  margin-bottom: 8px;
  font-size: 24px;
}

.register-header p {
  color: #666;
  margin-bottom: 0;
}

.register-form {
  margin-top: 32px;
}

.register-button {
  width: 100%;
  height: 44px;
  font-size: 16px;
}

.login-link {
  text-align: center;
  margin-top: 24px;
  color: #666;
}

.login-link a {
  color: #1890ff;
  text-decoration: none;
}

.login-link a:hover {
  text-decoration: underline;
}

.ant-checkbox-wrapper a {
  color: #1890ff;
  text-decoration: none;
}

.ant-checkbox-wrapper a:hover {
  text-decoration: underline;
}

/* 用户类型选择样式 */
.user-type-radio {
  width: 100%;
}

.radio-option {
  width: 100%;
  margin-bottom: 12px;
  padding: 16px;
  border: 2px solid #e8e8e8;
  border-radius: 8px;
  transition: all 0.3s;
  display: flex;
  align-items: flex-start;
}

.radio-option:hover {
  border-color: #1890ff;
  background-color: #f0f5ff;
}

.radio-option.ant-radio-wrapper-checked {
  border-color: #1890ff;
  background-color: #f0f5ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.radio-content {
  display: flex;
  align-items: center;
  width: 100%;
  margin-left: 8px;
}

.radio-icon {
  font-size: 24px;
  color: #1890ff;
  margin-right: 12px;
}

.radio-text {
  flex: 1;
}

.radio-title {
  font-weight: 600;
  font-size: 16px;
  color: #333;
  margin-bottom: 4px;
}

.radio-desc {
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

/* 协议模态框样式 */
.agreement-modal .ant-modal-body {
  max-height: 60vh;
  overflow-y: auto;
  padding: 24px;
}

.agreement-content {
  line-height: 1.6;
  color: #333;
}

.agreement-content h3 {
  text-align: center;
  color: #1890ff;
  margin-bottom: 24px;
  font-size: 20px;
  font-weight: 600;
}

.agreement-content h4 {
  color: #333;
  margin: 20px 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  border-left: 4px solid #1890ff;
  padding-left: 12px;
}

.agreement-content p {
  margin: 8px 0;
  text-indent: 0;
  font-size: 14px;
}

.agreement-content ul {
  margin: 8px 0;
  padding-left: 20px;
}

.agreement-content li {
  margin: 4px 0;
  font-size: 14px;
}

.effective-date {
  text-align: center;
  font-weight: 600;
  color: #666;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #e8e8e8;
}

.modal-footer {
  text-align: center;
  padding: 16px 0;
  border-top: 1px solid #e8e8e8;
  margin-top: 16px;
}
</style>
