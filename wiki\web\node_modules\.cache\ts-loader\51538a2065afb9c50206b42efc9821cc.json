{"remainingRequest": "D:\\JavaCar\\wiki\\wiki\\web\\node_modules\\ts-loader\\index.js??ref--13-1!D:\\JavaCar\\wiki\\wiki\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\JavaCar\\wiki\\wiki\\web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\JavaCar\\wiki\\wiki\\web\\src\\views\\Booking.vue?vue&type=script&lang=ts", "dependencies": [{"path": "D:\\JavaCar\\wiki\\wiki\\web\\src\\views\\Booking.vue", "mtime": 1757599802083}, {"path": "D:\\JavaCar\\wiki\\wiki\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750678170000}, {"path": "D:\\JavaCar\\wiki\\wiki\\web\\node_modules\\ts-loader\\index.js", "mtime": 1750678172000}, {"path": "D:\\JavaCar\\wiki\\wiki\\web\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1750678170000}, {"path": "D:\\JavaCar\\wiki\\wiki\\web\\node_modules\\vue-loader-v16\\dist\\index.js", "mtime": 1750678172000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "file": "D:\\JavaCar\\wiki\\wiki\\web\\src\\views\\Booking.vue.ts", "sourceRoot": "", "sources": ["D:\\JavaCar\\wiki\\wiki\\web\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\JavaCar\\wiki\\wiki\\web\\node_modules\\vue-loader-v16\\dist\\index.js??ref--1-1!D:\\JavaCar\\wiki\\wiki\\web\\src\\views\\Booking.vue?vue&type=script&lang=ts"], "names": [], "mappings": "AACA,OAAO,EAAE,eAAe,EAAE,GAAG,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,KAAK,CAAC;AACjF,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,YAAY,CAAC;AACjD,OAAO,EAAE,OAAO,EAAE,MAAM,gBAAgB,CAAC;AACzC,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,uBAAuB,CAAC;AACjE,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,MAAM,MAAM,4CAA4C,CAAC;AAChE,OAAO,oBAAoB,CAAC;AA+C5B,eAAe,eAAe,CAAC;IAC7B,IAAI,EAAE,SAAS;IACf,UAAU,EAAE;QACV,YAAY;KACb;IACD,KAAK;QACH,MAAM,MAAM,GAAG,SAAS,EAAE,CAAC;QAC3B,MAAM,KAAK,GAAG,QAAQ,EAAE,CAAC;QACzB,MAAM,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;QAC3B,MAAM,iBAAiB,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;QAErC,MAAM,WAAW,GAAG,GAAG,CAAc;YACnC,SAAS,EAAE,IAAI;YACf,SAAS,EAAE,IAAI;YACf,MAAM,EAAE,IAAI;YACZ,QAAQ,EAAE,EAAE;YACZ,YAAY,EAAE,IAAI;YAClB,WAAW,EAAE,EAAE;YACf,YAAY,EAAE,EAAE;YAChB,eAAe,EAAE,IAAI;YACrB,kBAAkB,EAAE,EAAE;YACtB,MAAM,EAAE,EAAE;SACX,CAAC,CAAC;QAEH,MAAM,WAAW,GAAG,GAAG,CAAC;YACtB,YAAY,EAAE,EAAE;YAChB,KAAK,EAAE,EAAE;YACT,KAAK,EAAE,EAAE;YACT,KAAK,EAAE,EAAE;SACV,CAAC,CAAC;QAEH,MAAM,QAAQ,GAAG,GAAG,CAAY,EAAE,CAAC,CAAC;QACpC,MAAM,eAAe,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;QACnC,MAAM,QAAQ,GAAG,GAAG,CAAY,EAAE,CAAC,CAAC;QACpC,MAAM,KAAK,GAAG,GAAG,CAAS,EAAE,CAAC,CAAC;QAC9B,MAAM,iBAAiB,GAAG,GAAG,CAAS,EAAE,CAAC,CAAC;QAC1C,MAAM,QAAQ,GAAG,GAAG,CAAS,EAAE,CAAC,CAAC;QACjC,MAAM,YAAY,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;QAChC,MAAM,iBAAiB,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;QACrC,MAAM,gBAAgB,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;QACpC,MAAM,WAAW,GAAG,GAAG,CAAe,EAAE,CAAC,CAAC;QAE1C,MAAM,eAAe,GAAG,QAAQ,CAAC,GAAG,EAAE;YACpC,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,EAAE,KAAK,WAAW,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;QACpF,CAAC,CAAC,CAAC;QAEH,MAAM,YAAY,GAAG;YACnB,SAAS,EAAE;gBACT,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE;aAC1D;YACD,SAAS,EAAE;gBACT,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE;aACxD;YACD,QAAQ,EAAE;gBACR,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE;aACvD;YACD,WAAW,EAAE;gBACX,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE;aACzD;YACD,YAAY,EAAE;gBACZ,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE;gBACvD,EAAE,OAAO,EAAE,eAAe,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE;aACpE;YACD,eAAe,EAAE;gBACf,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE;aAC1D;YACD,kBAAkB,EAAE;gBAClB,EAAE,QAAQ,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE;aACxD;SACF,CAAC;QAEF,UAAU;QACV,MAAM,YAAY,GAAG,CAAC,OAAY,EAAE,EAAE;YACpC,MAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;YACzB,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC3B,OAAO,OAAO,IAAI,OAAO,GAAG,KAAK,CAAC;QACpC,CAAC,CAAC;QAEF,eAAe;QACf,MAAM,YAAY,GAAG,CAAC,OAAY,EAAE,EAAE;YACpC,IAAI,CAAC,OAAO;gBAAE,OAAO,EAAE,CAAC;YAExB,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,EAAE,CAAC;YAC5B,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;YAEhC,OAAO;gBACL,aAAa,EAAE,GAAG,EAAE;oBAClB,MAAM,KAAK,GAAG,EAAE,CAAC;oBACjB,kBAAkB;oBAClB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;wBAC1B,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;qBACf;oBACD,KAAK,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;wBAC5B,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;qBACf;oBACD,OAAO,KAAK,CAAC;gBACf,CAAC;gBACD,eAAe,EAAE,CAAC,YAAoB,EAAE,EAAE;oBACxC,MAAM,OAAO,GAAG,EAAE,CAAC;oBACnB,mBAAmB;oBACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;wBAC3B,IAAI,CAAC,KAAK,EAAE,EAAE;4BACZ,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;yBACjB;qBACF;oBACD,OAAO,OAAO,CAAC;gBACjB,CAAC;aACF,CAAC;QACJ,CAAC,CAAC;QAEF,MAAM,YAAY,GAAG,CAAC,KAAa,EAAE,EAAE;YACrC,MAAM,MAAM,GAA8B,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC;YACzF,OAAO,MAAM,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC;QAC/B,CAAC,CAAC;QAEF,MAAM,eAAe,GAAG,GAAG,EAAE;YAC3B,cAAc;YACd,WAAW,CAAC,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC;YACtC,eAAe,EAAE,CAAC;QACpB,CAAC,CAAC;QAEF,MAAM,eAAe,GAAG,CAAC,KAAU,EAAE,EAAE;YACrC,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;YAC9B,IAAI,KAAK,IAAI,QAAQ,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;gBACtC,MAAM,eAAe,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;gBACjF,IAAI,eAAe,EAAE;oBACnB,OAAO,CAAC,GAAG,CAAC,UAAU,eAAe,CAAC,YAAY,MAAM,eAAe,CAAC,KAAK,IAAI,eAAe,CAAC,KAAK,EAAE,CAAC,CAAC;oBAC1G,IAAI,eAAe,CAAC,SAAS,KAAK,CAAC,EAAE;wBACnC,OAAO,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC;qBACzB;iBACF;aACF;QACH,CAAC,CAAC;QAEF,MAAM,gBAAgB,GAAG,GAAG,EAAE;YAC5B,aAAa;YACb,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,WAAW,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;QAC7D,CAAC,CAAC;QAEF,SAAS;QACT,MAAM,kBAAkB,GAAG,QAAQ,CAAC,GAAG,EAAE;YACvC,IAAI,WAAW,CAAC,KAAK,CAAC,SAAS,IAAI,QAAQ,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC5D,MAAM,eAAe,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,MAAM,CAAC,WAAW,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;gBACvG,IAAI,eAAe,EAAE;oBACnB,MAAM,WAAW,GAAG,eAAe,CAAC,SAAS,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC;oBACnE,OAAO,aAAa,eAAe,CAAC,YAAY,IAAI,eAAe,CAAC,KAAK,GAAG,WAAW,EAAE,CAAC;iBAC3F;aACF;YACD,OAAO,MAAM,CAAC;QAChB,CAAC,CAAC,CAAC;QAEH,UAAU;QACV,MAAM,WAAW,GAAG,QAAQ,CAAC,GAAG,EAAE;YAChC,MAAM,WAAW,GAAG,iBAAiB,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;gBAC/D,CAAC,iBAAiB,CAAC,KAAK,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;YAEzG,OAAO,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBAC9B,KAAK,EAAE,IAAI,CAAC,EAAE;gBACd,KAAK,EAAE,IAAI,CAAC,IAAI;gBAChB,OAAO,EAAE,IAAI,CAAC,OAAO;gBACrB,KAAK,EAAE,IAAI,CAAC,KAAK;aAClB,CAAC,CAAC,CAAC;QACN,CAAC,CAAC,CAAC;QAEH,gBAAgB;QAChB,MAAM,YAAY,GAAG,KAAK,IAAI,EAAE;YAC9B,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC;YAC1B,IAAI;gBACF,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;gBAC5B,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;gBAE/C,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;gBAErC,WAAW;gBACX,IAAI,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;oBACpD,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,IAAI,QAAQ,CAAC,IAAI,CAAC;oBACvD,IAAI,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;wBAC1B,QAAQ,CAAC,KAAK,GAAG,OAAO,CAAC;wBACzB,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,QAAQ,CAAC,KAAK,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;qBACvD;yBAAM;wBACL,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,CAAC,CAAC;wBACrC,QAAQ,CAAC,KAAK,GAAG,EAAE,CAAC;wBACpB,OAAO,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;qBACrC;iBACF;qBAAM;oBACL,MAAM,QAAQ,GAAG,CAAC,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,WAAW,CAAC;oBACzE,OAAO,CAAC,KAAK,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;oBACtC,OAAO,CAAC,KAAK,CAAC,aAAa,QAAQ,EAAE,CAAC,CAAC;oBACvC,QAAQ,CAAC,KAAK,GAAG,EAAE,CAAC;iBACrB;aACF;YAAC,OAAO,KAAU,EAAE;gBACnB,OAAO,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;gBACnC,MAAM,QAAQ,GAAG,CAAC,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,CAAC,OAAO,IAAI,QAAQ,CAAC;gBACrH,OAAO,CAAC,KAAK,CAAC,aAAa,QAAQ,EAAE,CAAC,CAAC;gBACvC,QAAQ,CAAC,KAAK,GAAG,EAAE,CAAC;aACrB;oBAAS;gBACR,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC;aAC5B;QACH,CAAC,CAAC;QAEF,MAAM,gBAAgB,GAAG,KAAK,EAAE,UAAkB,EAAE,EAAE;YACpD,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;YAElC,IAAI,CAAC,UAAU,IAAI,UAAU,CAAC,IAAI,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE;gBACjD,yBAAyB;gBACzB,iBAAiB,CAAC,KAAK,GAAG,EAAE,CAAC;gBAC7B,OAAO;aACR;YAED,IAAI;gBACF,YAAY;gBACZ,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,cAAc,EAAE;oBAC/C,MAAM,EAAE;wBACN,IAAI,EAAE,UAAU,CAAC,IAAI,EAAE;wBACvB,IAAI,EAAE,CAAC;wBACP,IAAI,EAAE,EAAE;qBACT;iBACF,CAAC,CAAC;gBAEH,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,KAAK,KAAK,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE;oBAC5D,iBAAiB,CAAC,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC;oBAChD,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,iBAAiB,CAAC,KAAK,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;iBACjE;qBAAM;oBACL,yBAAyB;oBACzB,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;oBACjC,MAAM,aAAa,GAAG,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CACjD,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC;wBAC1D,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC,CAAC,CAChF,CAAC;oBACF,iBAAiB,CAAC,KAAK,GAAG,aAAa,CAAC;iBACzC;aACF;YAAC,OAAO,KAAK,EAAE;gBACd,OAAO,CAAC,KAAK,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;gBACpC,wBAAwB;gBACxB,MAAM,aAAa,GAAG,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CACjD,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC;oBAC1D,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC,CAAC,CAChF,CAAC;gBACF,iBAAiB,CAAC,KAAK,GAAG,aAAa,CAAC;gBACxC,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,aAAa,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;aACtD;QACH,CAAC,CAAC;QAEF,MAAM,qBAAqB,GAAG,CAAC,KAAY,EAAE,EAAE;YAC7C,MAAM,UAAU,GAAI,KAAK,CAAC,MAA2B,CAAC,KAAK,CAAC;YAC5D,gBAAgB,CAAC,UAAU,CAAC,CAAC;QAC/B,CAAC,CAAC;QAEF,MAAM,eAAe,GAAG,GAAG,EAAE;YAC3B,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;YAC1B,iBAAiB,CAAC,KAAK,GAAG,IAAI,CAAC;YAC/B,gBAAgB,CAAC,KAAK,GAAG,IAAI,CAAC;YAC9B,mBAAmB;YACnB,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE;gBACtC,iBAAiB,CAAC,KAAK,GAAG,EAAE,CAAC;aAC9B;QACH,CAAC,CAAC;QAEF,MAAM,cAAc,GAAG,GAAG,EAAE;YAC1B,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;YAC1B,mBAAmB;YACnB,UAAU,CAAC,GAAG,EAAE;gBACd,iBAAiB,CAAC,KAAK,GAAG,KAAK,CAAC;gBAChC,gBAAgB,CAAC,KAAK,GAAG,KAAK,CAAC;YACjC,CAAC,EAAE,GAAG,CAAC,CAAC;QACV,CAAC,CAAC;QAEF,MAAM,kBAAkB,GAAG,GAAG,EAAE;YAC9B,WAAW,CAAC,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;YAChC,WAAW,CAAC,KAAK,CAAC,QAAQ,GAAG,EAAE,CAAC;YAChC,WAAW,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC;YACnC,WAAW,CAAC,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC;YACtC,QAAQ,CAAC,KAAK,GAAG,EAAE,CAAC;YACpB,WAAW,CAAC,KAAK,GAAG,EAAE,CAAC;YACvB,iBAAiB,CAAC,KAAK,GAAG,EAAE,CAAC;YAC7B,gBAAgB,CAAC,KAAK,GAAG,KAAK,CAAC;QACjC,CAAC,CAAC;QAEF,MAAM,gBAAgB,GAAG,CAAC,KAAU,EAAE,MAAW,EAAE,EAAE;YACnD,MAAM,cAAc,GAAG,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC;YAChD,WAAW,CAAC,KAAK,CAAC,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC;YACxC,WAAW,CAAC,KAAK,CAAC,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC;YAE1C,eAAe;YACf,IAAI,cAAc,KAAK,MAAM,CAAC,KAAK,EAAE;gBACnC,WAAW,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC;gBACnC,WAAW,CAAC,KAAK,CAAC,YAAY,GAAG,IAAI,CAAC;gBACtC,WAAW,CAAC,KAAK,GAAG,EAAE,CAAC;aACxB;YAED,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,EAAE,EAAE,EAAE,MAAM,CAAC,KAAK,EAAE,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC;YAEjE,cAAc;YACd,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC7B,CAAC,CAAC;QAEF,MAAM,cAAc,GAAG,GAAG,EAAE;YAC1B,iBAAiB,CAAC,KAAK,GAAG,IAAI,CAAC;QACjC,CAAC,CAAC;QAEF,MAAM,qBAAqB,GAAG,GAAG,EAAE;YACjC,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACjC,CAAC,CAAC;QAEF,MAAM,YAAY,GAAG,KAAK,EAAE,MAAe,EAAE,EAAE;YAC7C,IAAI,CAAC,MAAM,EAAE;gBACX,QAAQ,CAAC,KAAK,GAAG,EAAE,CAAC;gBACpB,OAAO;aACR;YAED,IAAI;gBACF,eAAe,CAAC,KAAK,GAAG,IAAI,CAAC;gBAC7B,gBAAgB;gBAChB,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,4BAA4B,EAAE;oBAC7D,MAAM,EAAE;wBACN,MAAM,EAAE,MAAM;wBACd,MAAM,EAAE,CAAC,CAAC,WAAW;qBACtB;iBACF,CAAC,CAAC;gBAEH,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,KAAK,KAAK,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE;oBAC5D,QAAQ,CAAC,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,IAAI,EAAE,CAAC;iBAC9C;qBAAM;oBACL,OAAO,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;oBACvC,QAAQ,CAAC,KAAK,GAAG,EAAE,CAAC;iBACrB;gBAED,OAAO,CAAC,GAAG,CAAC,QAAQ,MAAM,QAAQ,QAAQ,CAAC,KAAK,CAAC,MAAM,QAAQ,CAAC,CAAC;aAClE;YAAC,OAAO,KAAK,EAAE;gBACd,OAAO,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;gBACzD,OAAO,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;gBAC/B,QAAQ,CAAC,KAAK,GAAG,EAAE,CAAC;aACrB;oBAAS;gBACR,eAAe,CAAC,KAAK,GAAG,KAAK,CAAC;aAC/B;QACH,CAAC,CAAC;QAGF,MAAM,YAAY,GAAG,KAAK,IAAI,EAAE;YAC9B,IAAI;gBACF,WAAW;gBACX,MAAM,WAAW,GAAG,YAAY,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;gBACrD,IAAI,CAAC,WAAW,EAAE;oBAChB,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;oBACxB,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBACtB,OAAO;iBACR;gBAED,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;gBAEzC,gBAAgB;gBAChB,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,+BAA+B,EAAE;oBAChE,MAAM,EAAE;wBACN,IAAI,EAAE,CAAC;wBACP,IAAI,EAAE,GAAG;wBACT,MAAM,EAAE,QAAQ,CAAC,EAAE;qBACpB;iBACF,CAAC,CAAC;gBAEH,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;oBACnC,MAAM,WAAW,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE,CAAC;oBACrD,QAAQ,CAAC,KAAK,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,OAAY,EAAE,EAAE,CAAC,CAAC;wBAClD,EAAE,EAAE,OAAO,CAAC,EAAE;wBACd,YAAY,EAAE,OAAO,CAAC,YAAY;wBAClC,KAAK,EAAE,OAAO,CAAC,KAAK;wBACpB,KAAK,EAAE,OAAO,CAAC,KAAK;wBACpB,SAAS,EAAE,OAAO,CAAC,SAAS;qBAC7B,CAAC,CAAC,CAAC;oBAEJ,WAAW;oBACX,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;oBACvC,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,WAAW,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;oBAE5D,MAAM,cAAc,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,KAAK,CAAC,CAAC,CAAC;oBACnE,OAAO,CAAC,GAAG,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;oBAExC,IAAI,cAAc,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,SAAS,EAAE;wBAClD,iBAAiB;wBACjB,WAAW,CAAC,KAAK,CAAC,SAAS,GAAG,MAAM,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC;wBACxD,OAAO,CAAC,GAAG,CAAC,iBAAiB,WAAW,CAAC,KAAK,CAAC,SAAS,QAAQ,OAAO,WAAW,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC;wBACtG,OAAO,CAAC,GAAG,CAAC,aAAa,cAAc,CAAC,YAAY,MAAM,cAAc,CAAC,KAAK,IAAI,cAAc,CAAC,KAAK,EAAE,CAAC,CAAC;wBAC1G,OAAO,CAAC,OAAO,CAAC,eAAe,cAAc,CAAC,YAAY,IAAI,cAAc,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC;wBAEzF,oBAAoB;wBACpB,MAAM,QAAQ,CAAC,GAAG,EAAE;4BAClB,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,WAAW,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;wBAChE,CAAC,CAAC,CAAC;qBACJ;yBAAM,IAAI,CAAC,cAAc,IAAI,QAAQ,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,SAAS,EAAE;wBACvF,yBAAyB;wBACzB,MAAM,YAAY,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;wBACvC,WAAW,CAAC,KAAK,CAAC,SAAS,GAAG,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC;wBACtD,OAAO,CAAC,GAAG,CAAC,iBAAiB,WAAW,CAAC,KAAK,CAAC,SAAS,QAAQ,OAAO,WAAW,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC;wBACtG,OAAO,CAAC,GAAG,CAAC,cAAc,YAAY,CAAC,YAAY,MAAM,YAAY,CAAC,KAAK,IAAI,YAAY,CAAC,KAAK,EAAE,CAAC,CAAC;wBACrG,OAAO,CAAC,IAAI,CAAC,cAAc,YAAY,CAAC,YAAY,IAAI,YAAY,CAAC,KAAK,kBAAkB,EAAE,CAAC,CAAC,CAAC;wBAEjG,oBAAoB;wBACpB,MAAM,QAAQ,CAAC,GAAG,EAAE;4BAClB,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,WAAW,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;wBAChE,CAAC,CAAC,CAAC;qBACJ;oBAED,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;wBAC5B,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;qBACnC;iBACF;qBAAM;oBACL,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,IAAI,UAAU,CAAC,CAAC;oBACnD,QAAQ,CAAC,KAAK,GAAG,EAAE,CAAC;iBACrB;aACF;YAAC,OAAO,KAAK,EAAE;gBACd,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;gBAC7C,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;gBAC9B,QAAQ,CAAC,KAAK,GAAG,EAAE,CAAC;aACrB;QACH,CAAC,CAAC;QAEF,MAAM,eAAe,GAAG,KAAK,IAAI,EAAE;YACjC,IAAI;gBACF,sBAAsB;gBACtB,OAAO;gBACP,WAAW,CAAC,KAAK,GAAG;oBAClB,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE;oBAC5C,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE;oBAC5C,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE;iBAC7C,CAAC;aACH;YAAC,OAAO,KAAK,EAAE;gBACd,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;aAC3B;QACH,CAAC,CAAC;QAGF,MAAM,gBAAgB,GAAG,KAAK,IAAI,EAAE;YAClC,IAAI;gBACF,WAAW;gBACX,MAAM,WAAW,GAAG,YAAY,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;gBACrD,IAAI,CAAC,WAAW,EAAE;oBAChB,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;oBACxB,OAAO;iBACR;gBAED,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;gBAEzC,OAAO;gBACP,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,EAAE,EAAE;oBAC1C,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;oBACxB,OAAO;iBACR;gBAED,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE;oBACnC,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;oBACzB,OAAO;iBACR;gBAED,YAAY;gBACZ,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,IAAI,CAAC,eAAe,EAAE;oBACjD,YAAY,EAAE,WAAW,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,EAAE;oBACnD,KAAK,EAAE,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE;oBACrC,KAAK,EAAE,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,IAAI;oBAC7C,KAAK,EAAE,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,IAAI;oBAC7C,OAAO,EAAE,CAAC;oBACV,SAAS,EAAE,QAAQ,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC9C,MAAM,EAAE,CAAC;oBACT,MAAM,EAAE,QAAQ,CAAC,EAAE;iBACpB,CAAC,CAAC;gBAEH,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,KAAK,KAAK,EAAE;oBACnC,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;oBAC1B,iBAAiB,CAAC,KAAK,GAAG,KAAK,CAAC;oBAEhC,OAAO;oBACP,WAAW,CAAC,KAAK,GAAG;wBAClB,YAAY,EAAE,EAAE;wBAChB,KAAK,EAAE,EAAE;wBACT,KAAK,EAAE,EAAE;wBACT,KAAK,EAAE,EAAE;qBACV,CAAC;oBAEF,WAAW;oBACX,MAAM,YAAY,EAAE,CAAC;oBAErB,oBAAoB;oBACpB,IAAI,QAAQ,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,SAAS,EAAE;wBAC/D,WAAW,CAAC,KAAK,CAAC,SAAS,GAAG,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;wBAC3D,OAAO,CAAC,GAAG,CAAC,wBAAwB,WAAW,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC;wBACnE,OAAO,CAAC,OAAO,CAAC,WAAW,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC,CAAC;qBACjE;iBACF;qBAAM;oBACL,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,IAAI,QAAQ,CAAC,CAAC;iBAClD;aACF;YAAC,OAAO,KAAK,EAAE;gBACd,OAAO,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;gBAC3C,OAAO,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;aAC7B;QACH,CAAC,CAAC;QAEF,MAAM,YAAY,GAAG,KAAK,IAAI,EAAE;YAC9B,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gBAClC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;gBACxB,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACtB,OAAO;aACR;YAED,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;YACrB,IAAI;gBACF,MAAM,cAAc,GAAG,CAAC,QAAa,EAAkC,EAAE;oBACvE,IAAI,CAAC,QAAQ;wBAAE,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;oBAE7C,IAAI,OAAO,CAAC;oBACZ,IAAI,QAAQ,YAAY,IAAI,EAAE;wBAC5B,OAAO,GAAG,QAAQ,CAAC;qBACpB;yBAAM,IAAI,OAAO,QAAQ,KAAK,QAAQ,IAAI,QAAQ,CAAC,MAAM,EAAE;wBAC1D,uBAAuB;wBACvB,OAAO;4BACL,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,YAAY,CAAC;4BACnC,IAAI,EAAE,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC;yBAC/B,CAAC;qBACH;yBAAM,IAAI,OAAO,QAAQ,KAAK,QAAQ,EAAE;wBACvC,OAAO,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC;qBAC9B;yBAAM;wBACL,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE,CAAC;qBAC/B;oBAED,MAAM,IAAI,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;oBACnC,MAAM,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;oBAC9D,MAAM,GAAG,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;oBACvD,MAAM,IAAI,GAAG,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;oBACzD,MAAM,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC;oBAE7D,OAAO;wBACL,IAAI,EAAE,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,EAAE;wBAC/B,IAAI,EAAE,GAAG,IAAI,IAAI,MAAM,EAAE;qBAC1B,CAAC;gBACJ,CAAC,CAAC;gBAEF,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,cAAc,CAAC,WAAW,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;gBAEzE,MAAM,IAAI,GAAG;oBACX,GAAG,WAAW,CAAC,KAAK;oBACpB,WAAW,EAAE,IAAI;oBACjB,WAAW,EAAE,IAAI;iBAClB,CAAC;gBAEF,cAAc;gBACd,OAAQ,IAAY,CAAC,eAAe,CAAC;gBAErC,SAAS;gBACT,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE;oBACzC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;oBACxB,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;oBACtB,OAAO;iBACR;gBAED,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;gBAE3D,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE;oBACzB,OAAO,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAC;oBACxC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;iBAClB;qBAAM;oBACL,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,IAAI,MAAM,CAAC,CAAC;iBAChD;aACF;YAAC,OAAO,KAAK,EAAE;gBACd,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;aAC/B;oBAAS;gBACR,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;aACvB;QACH,CAAC,CAAC;QAEF,mBAAmB;QACnB,MAAM,YAAY,GAAG,GAAG,EAAE;YACxB,MAAM,WAAW,GAAG,YAAY,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YACrD,IAAI,WAAW,EAAE;gBACf,IAAI;oBACF,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;oBACzC,WAAW,CAAC,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,IAAI,EAAE,CAAC;oBACxD,WAAW,CAAC,KAAK,CAAC,YAAY,GAAG,QAAQ,CAAC,KAAK,IAAI,EAAE,CAAC;iBACvD;gBAAC,OAAO,KAAK,EAAE;oBACd,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,CAAC;iBAChD;aACF;QACH,CAAC,CAAC;QAEF,WAAW;QACX,KAAK,CAAC,GAAG,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE;YAC9D,OAAO,CAAC,GAAG,CAAC,SAAS,QAAQ,QAAQ,QAAQ,EAAE,CAAC,CAAC;YACjD,IAAI,QAAQ,IAAI,QAAQ,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;gBACzC,MAAM,eAAe,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;gBACpF,IAAI,eAAe,EAAE;oBACnB,OAAO,CAAC,GAAG,CAAC,UAAU,eAAe,CAAC,YAAY,MAAM,eAAe,CAAC,KAAK,IAAI,eAAe,CAAC,KAAK,EAAE,CAAC,CAAC;iBAC3G;aACF;QACH,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAExB,SAAS,CAAC,KAAK,IAAI,EAAE;YACnB,WAAW;YACX,MAAM,YAAY,EAAE,CAAC;YACrB,uBAAuB;YACvB,MAAM,YAAY,EAAE,CAAC,CAAC,uBAAuB;YAC7C,eAAe,EAAE,CAAC;YAClB,YAAY,EAAE,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,WAAW;YACX,WAAW;YACX,YAAY;YACZ,OAAO;YACP,iBAAiB;YACjB,QAAQ;YACR,eAAe;YACf,QAAQ;YACR,kBAAkB;YAClB,KAAK;YACL,iBAAiB;YACjB,QAAQ;YACR,YAAY;YACZ,iBAAiB;YACjB,gBAAgB;YAChB,WAAW;YACX,WAAW;YACX,eAAe;YACf,YAAY;YACZ,YAAY;YACZ,MAAM;YACN,YAAY;YACZ,eAAe;YACf,eAAe;YACf,gBAAgB;YAChB,YAAY;YACZ,gBAAgB;YAChB,qBAAqB;YACrB,eAAe;YACf,cAAc;YACd,gBAAgB;YAChB,kBAAkB;YAClB,cAAc;YACd,qBAAqB;YACrB,gBAAgB;YAChB,YAAY;YACZ,UAAU;SACX,CAAC;IACJ,CAAC;CACF,CAAC,CAAC", "sourcesContent": ["\r\nimport { defineComponent, ref, onMounted, computed, nextTick, watch } from 'vue';\r\nimport { useRouter, useRoute } from 'vue-router';\r\nimport { message } from 'ant-design-vue';\r\nimport { PlusOutlined, StarFilled } from '@ant-design/icons-vue';\r\nimport axios from 'axios';\r\nimport locale from 'ant-design-vue/es/date-picker/locale/zh_CN';\r\nimport 'dayjs/locale/zh-cn';\r\n\r\n// 定义类型接口\r\ninterface Service {\r\n  id: number;\r\n  name: string;\r\n  price: number;\r\n  description?: string;\r\n  duration?: number;\r\n}\r\n\r\ninterface Vehicle {\r\n  id: number;\r\n  licensePlate: string;\r\n  brand: string;\r\n  model: string;\r\n  isDefault?: number;\r\n}\r\n\r\ninterface Technician {\r\n  id: number;\r\n  name: string;\r\n  level: number;\r\n  avatar?: string;\r\n}\r\n\r\n\r\ninterface Shop {\r\n  id: number;\r\n  name: string;\r\n  address?: string;\r\n  phone?: string;\r\n}\r\n\r\ninterface BookingForm {\r\n  serviceId: number | null;\r\n  vehicleId: number | null;\r\n  shopId: number | null;\r\n  shopName: string;\r\n  technicianId: number | null;\r\n  contactName: string;\r\n  contactPhone: string;\r\n  bookingDateTime: any;\r\n  problemDescription: string;\r\n  remark: string;\r\n}\r\n\r\nexport default defineComponent({\r\n  name: 'Booking',\r\n  components: {\r\n    PlusOutlined\r\n  },\r\n  setup() {\r\n    const router = useRouter();\r\n    const route = useRoute();\r\n    const loading = ref(false);\r\n    const addVehicleVisible = ref(false);\r\n\r\n    const bookingForm = ref<BookingForm>({\r\n      serviceId: null,\r\n      vehicleId: null,\r\n      shopId: null,\r\n      shopName: '',\r\n      technicianId: null,\r\n      contactName: '',\r\n      contactPhone: '',\r\n      bookingDateTime: null,\r\n      problemDescription: '',\r\n      remark: ''\r\n    });\r\n\r\n    const vehicleForm = ref({\r\n      licensePlate: '',\r\n      brand: '',\r\n      model: '',\r\n      color: ''\r\n    });\r\n\r\n    const services = ref<Service[]>([]);\r\n    const servicesLoading = ref(false);\r\n    const vehicles = ref<Vehicle[]>([]);\r\n    const shops = ref<Shop[]>([]);\r\n    const shopSearchResults = ref<Shop[]>([]);\r\n    const allShops = ref<Shop[]>([]);\r\n    const shopsLoading = ref(false);\r\n    const shopSearchFocused = ref(false);\r\n    const showShopDropdown = ref(false);\r\n    const technicians = ref<Technician[]>([]);\r\n\r\n    const selectedService = computed(() => {\r\n      return services.value.find(service => service.id === bookingForm.value.serviceId);\r\n    });\r\n\r\n    const bookingRules = {\r\n      serviceId: [\r\n        { required: true, message: '请选择服务项目', trigger: 'change' }\r\n      ],\r\n      vehicleId: [\r\n        { required: true, message: '请选择车辆', trigger: 'change' }\r\n      ],\r\n      shopName: [\r\n        { required: true, message: '请选择维修店', trigger: 'blur' }\r\n      ],\r\n      contactName: [\r\n        { required: true, message: '请输入联系人姓名', trigger: 'blur' }\r\n      ],\r\n      contactPhone: [\r\n        { required: true, message: '请输入联系电话', trigger: 'blur' },\r\n        { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }\r\n      ],\r\n      bookingDateTime: [\r\n        { required: true, message: '请选择预约时间', trigger: 'change' }\r\n      ],\r\n      problemDescription: [\r\n        { required: true, message: '请描述车辆问题', trigger: 'blur' }\r\n      ]\r\n    };\r\n\r\n    // 禁用过去的日期\r\n    const disabledDate = (current: any) => {\r\n      const today = new Date();\r\n      today.setHours(0, 0, 0, 0);\r\n      return current && current < today;\r\n    };\r\n\r\n    // 禁用时间段（营业时间外）\r\n    const disabledTime = (current: any) => {\r\n      if (!current) return {};\r\n      \r\n      const hour = current.hour();\r\n      const minute = current.minute();\r\n      \r\n      return {\r\n        disabledHours: () => {\r\n          const hours = [];\r\n          // 营业时间 9:00-18:00\r\n          for (let i = 0; i < 9; i++) {\r\n            hours.push(i);\r\n          }\r\n          for (let i = 18; i < 24; i++) {\r\n            hours.push(i);\r\n          }\r\n          return hours;\r\n        },\r\n        disabledMinutes: (selectedHour: number) => {\r\n          const minutes = [];\r\n          // 只允许整点和半点（0分和30分）\r\n          for (let i = 1; i < 60; i++) {\r\n            if (i !== 30) {\r\n              minutes.push(i);\r\n            }\r\n          }\r\n          return minutes;\r\n        }\r\n      };\r\n    };\r\n\r\n    const getLevelText = (level: number) => {\r\n      const levels: { [key: number]: string } = { 1: '初级技师', 2: '中级技师', 3: '高级技师', 4: '专家技师' };\r\n      return levels[level] || '技师';\r\n    };\r\n\r\n    const onServiceChange = () => {\r\n      // 服务变更时清空技师选择\r\n      bookingForm.value.technicianId = null;\r\n      loadTechnicians();\r\n    };\r\n\r\n    const onVehicleChange = (value: any) => {\r\n      console.log('车辆选择变更:', value);\r\n      if (value && vehicles.value.length > 0) {\r\n        const selectedVehicle = vehicles.value.find(v => Number(v.id) === Number(value));\r\n        if (selectedVehicle) {\r\n          console.log(`选中的车辆: ${selectedVehicle.licensePlate} - ${selectedVehicle.brand} ${selectedVehicle.model}`);\r\n          if (selectedVehicle.isDefault === 1) {\r\n            console.log('选择的是默认车辆');\r\n          }\r\n        }\r\n      }\r\n    };\r\n\r\n    const onDateTimeChange = () => {\r\n      // 日期时间变更时的处理\r\n      console.log('预约时间已选择:', bookingForm.value.bookingDateTime);\r\n    };\r\n\r\n    // 车辆选择标签\r\n    const vehicleSelectLabel = computed(() => {\r\n      if (bookingForm.value.vehicleId && vehicles.value.length > 0) {\r\n        const selectedVehicle = vehicles.value.find(v => Number(v.id) === Number(bookingForm.value.vehicleId));\r\n        if (selectedVehicle) {\r\n          const defaultText = selectedVehicle.isDefault === 1 ? ' (默认)' : '';\r\n          return `选择车辆 - 当前：${selectedVehicle.licensePlate} ${selectedVehicle.brand}${defaultText}`;\r\n        }\r\n      }\r\n      return '选择车辆';\r\n    });\r\n\r\n    // 维修店搜索相关\r\n    const shopOptions = computed(() => {\r\n      const shopsToShow = shopSearchResults.value.length > 0 ? shopSearchResults.value :\r\n                         (shopSearchFocused.value || !bookingForm.value.shopName.trim()) ? allShops.value : [];\r\n\r\n      return shopsToShow.map(shop => ({\r\n        value: shop.id,\r\n        label: shop.name,\r\n        address: shop.address,\r\n        phone: shop.phone\r\n      }));\r\n    });\r\n\r\n    // 从数据库获取所有维修店数据\r\n    const initAllShops = async () => {\r\n      shopsLoading.value = true;\r\n      try {\r\n        console.log('开始加载维修店列表...');\r\n        const response = await axios.get('/shop/list');\r\n\r\n        console.log('API响应:', response.data);\r\n\r\n        // 改进响应判断逻辑\r\n        if (response.data && response.data.success !== false) {\r\n          const content = response.data.content || response.data;\r\n          if (Array.isArray(content)) {\r\n            allShops.value = content;\r\n            console.log('成功加载维修店列表:', allShops.value.length, '家');\r\n          } else {\r\n            console.warn('维修店数据格式不正确:', content);\r\n            allShops.value = [];\r\n            message.warning('维修店数据格式异常，请联系管理员');\r\n          }\r\n        } else {\r\n          const errorMsg = (response.data && response.data.message) || '获取维修店列表失败';\r\n          console.error('获取维修店列表失败:', errorMsg);\r\n          message.error(`获取维修店列表失败：${errorMsg}`);\r\n          allShops.value = [];\r\n        }\r\n      } catch (error: any) {\r\n        console.error('加载维修店列表错误:', error);\r\n        const errorMsg = (error.response && error.response.data && error.response.data.message) || error.message || '网络连接异常';\r\n        message.error(`无法获取维修店列表：${errorMsg}`);\r\n        allShops.value = [];\r\n      } finally {\r\n        shopsLoading.value = false;\r\n      }\r\n    };\r\n\r\n    const handleShopSearch = async (searchText: string) => {\r\n      console.log('搜索维修店:', searchText);\r\n      \r\n      if (!searchText || searchText.trim().length === 0) {\r\n        // 如果搜索框为空，清空搜索结果，显示所有维修店\r\n        shopSearchResults.value = [];\r\n        return;\r\n      }\r\n\r\n      try {\r\n        // 调用后端搜索API\r\n        const response = await axios.get('/shop/search', {\r\n          params: {\r\n            name: searchText.trim(),\r\n            page: 1,\r\n            size: 50\r\n          }\r\n        });\r\n\r\n        if (response.data.success !== false && response.data.content) {\r\n          shopSearchResults.value = response.data.content;\r\n          console.log('API搜索结果:', shopSearchResults.value.length, '家维修店');\r\n        } else {\r\n          // 如果API调用失败，使用本地数据进行备选搜索\r\n          console.warn('API搜索失败，使用本地数据搜索');\r\n          const filteredShops = allShops.value.filter(shop => \r\n            shop.name.toLowerCase().includes(searchText.toLowerCase()) ||\r\n            (shop.address && shop.address.toLowerCase().includes(searchText.toLowerCase()))\r\n          );\r\n          shopSearchResults.value = filteredShops;\r\n        }\r\n      } catch (error) {\r\n        console.error('搜索维修店API错误:', error);\r\n        // API调用失败时，使用本地数据进行备选搜索\r\n        const filteredShops = allShops.value.filter(shop => \r\n          shop.name.toLowerCase().includes(searchText.toLowerCase()) ||\r\n          (shop.address && shop.address.toLowerCase().includes(searchText.toLowerCase()))\r\n        );\r\n        shopSearchResults.value = filteredShops;\r\n        console.log('本地搜索结果:', filteredShops.length, '家维修店');\r\n      }\r\n    };\r\n\r\n    const handleShopSearchInput = (event: Event) => {\r\n      const searchText = (event.target as HTMLInputElement).value;\r\n      handleShopSearch(searchText);\r\n    };\r\n\r\n    const handleShopFocus = () => {\r\n      console.log('维修店输入框获得焦点');\r\n      shopSearchFocused.value = true;\r\n      showShopDropdown.value = true;\r\n      // 如果没有搜索文本，显示所有维修店\r\n      if (!bookingForm.value.shopName.trim()) {\r\n        shopSearchResults.value = [];\r\n      }\r\n    };\r\n\r\n    const handleShopBlur = () => {\r\n      console.log('维修店输入框失去焦点');\r\n      // 延迟隐藏，避免选择选项时立即隐藏\r\n      setTimeout(() => {\r\n        shopSearchFocused.value = false;\r\n        showShopDropdown.value = false;\r\n      }, 200);\r\n    };\r\n\r\n    const clearShopSelection = () => {\r\n      bookingForm.value.shopId = null;\r\n      bookingForm.value.shopName = '';\r\n      bookingForm.value.serviceId = null;\r\n      bookingForm.value.technicianId = null;\r\n      services.value = [];\r\n      technicians.value = [];\r\n      shopSearchResults.value = [];\r\n      showShopDropdown.value = false;\r\n    };\r\n\r\n    const handleShopSelect = (value: any, option: any) => {\r\n      const previousShopId = bookingForm.value.shopId;\r\n      bookingForm.value.shopId = option.value;\r\n      bookingForm.value.shopName = option.label;\r\n      \r\n      // 清空之前选择的服务和技师\r\n      if (previousShopId !== option.value) {\r\n        bookingForm.value.serviceId = null;\r\n        bookingForm.value.technicianId = null;\r\n        technicians.value = [];\r\n      }\r\n      \r\n      console.log('选择的维修店:', { id: option.value, name: option.label });\r\n      \r\n      // 加载该维修店的服务项目\r\n      loadServices(option.value);\r\n    };\r\n\r\n    const showAddVehicle = () => {\r\n      addVehicleVisible.value = true;\r\n    };\r\n\r\n    const goToVehicleManagement = () => {\r\n      router.push('/owner/vehicles');\r\n    };\r\n\r\n    const loadServices = async (shopId?: number) => {\r\n      if (!shopId) {\r\n        services.value = [];\r\n        return;\r\n      }\r\n      \r\n      try {\r\n        servicesLoading.value = true;\r\n        // 根据维修店ID获取服务列表\r\n        const response = await axios.get(`/service/getAllServiceList`, {\r\n          params: {\r\n            shopId: shopId,\r\n            status: 1 // 只获取上架的服务\r\n          }\r\n        });\r\n        \r\n        if (response.data.success !== false && response.data.content) {\r\n          services.value = response.data.content || [];\r\n        } else {\r\n          console.warn('获取维修店服务失败，该维修店可能暂无服务项目');\r\n          services.value = [];\r\n        }\r\n        \r\n        console.log(`为维修店 ${shopId} 加载了 ${services.value.length} 个服务项目`);\r\n      } catch (error) {\r\n        console.error('Error loading services for shop:', error);\r\n        message.error('获取维修店服务失败，请重试');\r\n        services.value = [];\r\n      } finally {\r\n        servicesLoading.value = false;\r\n      }\r\n    };\r\n\r\n\r\n    const loadVehicles = async () => {\r\n      try {\r\n        // 获取当前用户信息\r\n        const userInfoStr = localStorage.getItem('userInfo');\r\n        if (!userInfoStr) {\r\n          message.warning('请先登录');\r\n          router.push('/login');\r\n          return;\r\n        }\r\n\r\n        const userInfo = JSON.parse(userInfoStr);\r\n        \r\n        // 调用获取用户车辆列表API\r\n        const response = await axios.get('/vehicle/getVehicleListByPage', {\r\n          params: {\r\n            page: 1,\r\n            size: 100, // 获取所有车辆\r\n            userId: userInfo.id\r\n          }\r\n        });\r\n\r\n        if (response.data.success !== false) {\r\n          const vehicleList = response.data.content.list || [];\r\n          vehicles.value = vehicleList.map((vehicle: any) => ({\r\n            id: vehicle.id,\r\n            licensePlate: vehicle.licensePlate,\r\n            brand: vehicle.brand,\r\n            model: vehicle.model,\r\n            isDefault: vehicle.isDefault\r\n          }));\r\n          \r\n          // 自动选择默认车辆\r\n          console.log('当前车辆列表:', vehicles.value);\r\n          console.log('当前选择的vehicleId:', bookingForm.value.vehicleId);\r\n          \r\n          const defaultVehicle = vehicles.value.find(v => v.isDefault === 1);\r\n          console.log('找到的默认车辆:', defaultVehicle);\r\n          \r\n          if (defaultVehicle && !bookingForm.value.vehicleId) {\r\n            // 确保数据类型匹配，转换为数字\r\n            bookingForm.value.vehicleId = Number(defaultVehicle.id);\r\n            console.log(`设置vehicleId为: ${bookingForm.value.vehicleId}，类型: ${typeof bookingForm.value.vehicleId}`);\r\n            console.log(`自动选择默认车辆: ${defaultVehicle.licensePlate} - ${defaultVehicle.brand} ${defaultVehicle.model}`);\r\n            message.success(`✨ 已自动选择默认车辆：${defaultVehicle.licensePlate} ${defaultVehicle.brand}`, 3);\r\n            \r\n            // 使用nextTick确保DOM更新\r\n            await nextTick(() => {\r\n              console.log('DOM更新后的vehicleId:', bookingForm.value.vehicleId);\r\n            });\r\n          } else if (!defaultVehicle && vehicles.value.length > 0 && !bookingForm.value.vehicleId) {\r\n            // 如果没有设置默认车辆但有车辆，自动选择第一辆\r\n            const firstVehicle = vehicles.value[0];\r\n            bookingForm.value.vehicleId = Number(firstVehicle.id);\r\n            console.log(`设置vehicleId为: ${bookingForm.value.vehicleId}，类型: ${typeof bookingForm.value.vehicleId}`);\r\n            console.log(`自动选择第一辆车辆: ${firstVehicle.licensePlate} - ${firstVehicle.brand} ${firstVehicle.model}`);\r\n            message.info(`🚗 已自动选择车辆：${firstVehicle.licensePlate} ${firstVehicle.brand}（建议在车辆管理中设置默认车辆）`, 4);\r\n            \r\n            // 使用nextTick确保DOM更新\r\n            await nextTick(() => {\r\n              console.log('DOM更新后的vehicleId:', bookingForm.value.vehicleId);\r\n            });\r\n          }\r\n          \r\n          if (vehicleList.length === 0) {\r\n            message.info('您还没有添加车辆，请先添加车辆信息');\r\n          }\r\n        } else {\r\n          message.error(response.data.message || '获取车辆列表失败');\r\n          vehicles.value = [];\r\n        }\r\n      } catch (error) {\r\n        console.error('Load vehicles error:', error);\r\n        message.error('加载车辆列表失败，请重试');\r\n        vehicles.value = [];\r\n      }\r\n    };\r\n\r\n    const loadTechnicians = async () => {\r\n      try {\r\n        // TODO: 根据服务类型加载合适的技师\r\n        // 临时数据\r\n        technicians.value = [\r\n          { id: 1, name: '张师傅', level: 4, avatar: '' },\r\n          { id: 2, name: '李师傅', level: 3, avatar: '' },\r\n          { id: 3, name: '王师傅', level: 2, avatar: '' }\r\n        ];\r\n      } catch (error) {\r\n        message.error('加载技师列表失败');\r\n      }\r\n    };\r\n\r\n\r\n    const handleAddVehicle = async () => {\r\n      try {\r\n        // 获取当前用户信息\r\n        const userInfoStr = localStorage.getItem('userInfo');\r\n        if (!userInfoStr) {\r\n          message.warning('请先登录');\r\n          return;\r\n        }\r\n\r\n        const userInfo = JSON.parse(userInfoStr);\r\n        \r\n        // 基本验证\r\n        if (!vehicleForm.value.licensePlate.trim()) {\r\n          message.error('请输入车牌号');\r\n          return;\r\n        }\r\n        \r\n        if (!vehicleForm.value.brand.trim()) {\r\n          message.error('请输入车辆品牌');\r\n          return;\r\n        }\r\n\r\n        // 调用添加车辆API\r\n        const response = await axios.post('/vehicle/save', {\r\n          licensePlate: vehicleForm.value.licensePlate.trim(),\r\n          brand: vehicleForm.value.brand.trim(),\r\n          model: vehicleForm.value.model.trim() || null,\r\n          color: vehicleForm.value.color.trim() || null,\r\n          mileage: 0,\r\n          isDefault: vehicles.value.length === 0 ? 1 : 0, // 如果是第一辆车，设为默认\r\n          status: 1,\r\n          userId: userInfo.id\r\n        });\r\n\r\n        if (response.data.success !== false) {\r\n          message.success('车辆添加成功');\r\n          addVehicleVisible.value = false;\r\n          \r\n          // 重置表单\r\n          vehicleForm.value = {\r\n            licensePlate: '',\r\n            brand: '',\r\n            model: '',\r\n            color: ''\r\n          };\r\n          \r\n          // 重新加载车辆列表\r\n          await loadVehicles();\r\n          \r\n          // 如果这是用户的第一辆车，自动选择它\r\n          if (vehicles.value.length === 1 && !bookingForm.value.vehicleId) {\r\n            bookingForm.value.vehicleId = Number(vehicles.value[0].id);\r\n            console.log(`新增车辆后自动选择: vehicleId=${bookingForm.value.vehicleId}`);\r\n            message.success(`已自动选择车辆：${vehicles.value[0].licensePlate}`, 2);\r\n          }\r\n        } else {\r\n          message.error(response.data.message || '添加车辆失败');\r\n        }\r\n      } catch (error) {\r\n        console.error('Add vehicle error:', error);\r\n        message.error('添加车辆失败，请重试');\r\n      }\r\n    };\r\n\r\n    const handleSubmit = async () => {\r\n      if (!localStorage.getItem('token')) {\r\n        message.warning('请先登录');\r\n        router.push('/login');\r\n        return;\r\n      }\r\n\r\n      loading.value = true;\r\n      try {\r\n        const formatDateTime = (datetime: any): { date: string, time: string } => {\r\n          if (!datetime) return { date: '', time: '' };\r\n          \r\n          let dateObj;\r\n          if (datetime instanceof Date) {\r\n            dateObj = datetime;\r\n          } else if (typeof datetime === 'object' && datetime.format) {\r\n            // Ant Design Vue日期对象处理\r\n            return {\r\n              date: datetime.format('YYYY-MM-DD'),\r\n              time: datetime.format('HH:mm')\r\n            };\r\n          } else if (typeof datetime === 'string') {\r\n            dateObj = new Date(datetime);\r\n          } else {\r\n            return { date: '', time: '' };\r\n          }\r\n          \r\n          const year = dateObj.getFullYear();\r\n          const month = String(dateObj.getMonth() + 1).padStart(2, '0');\r\n          const day = String(dateObj.getDate()).padStart(2, '0');\r\n          const hour = String(dateObj.getHours()).padStart(2, '0');\r\n          const minute = String(dateObj.getMinutes()).padStart(2, '0');\r\n          \r\n          return {\r\n            date: `${year}-${month}-${day}`,\r\n            time: `${hour}:${minute}`\r\n          };\r\n        };\r\n        \r\n        const { date, time } = formatDateTime(bookingForm.value.bookingDateTime);\r\n          \r\n        const data = {\r\n          ...bookingForm.value,\r\n          bookingDate: date,\r\n          bookingTime: time\r\n        };\r\n\r\n        // 删除合并后不需要的字段\r\n        delete (data as any).bookingDateTime;\r\n\r\n        // 验证必填字段\r\n        if (!data.shopId || !data.shopName.trim()) {\r\n          message.error('请选择维修店');\r\n          loading.value = false;\r\n          return;\r\n        }\r\n\r\n        const response = await axios.post('/booking/create', data);\r\n        \r\n        if (response.data.success) {\r\n          message.success('预约成功！我们将尽快联系您确认服务时间。');\r\n          router.push('/');\r\n        } else {\r\n          message.error(response.data.message || '预约失败');\r\n        }\r\n      } catch (error) {\r\n        message.error('预约失败，请检查网络连接');\r\n      } finally {\r\n        loading.value = false;\r\n      }\r\n    };\r\n\r\n    // 从本地存储获取用户信息填充联系人\r\n    const loadUserInfo = () => {\r\n      const userInfoStr = localStorage.getItem('userInfo');\r\n      if (userInfoStr) {\r\n        try {\r\n          const userInfo = JSON.parse(userInfoStr);\r\n          bookingForm.value.contactName = userInfo.realName || '';\r\n          bookingForm.value.contactPhone = userInfo.phone || '';\r\n        } catch (error) {\r\n          console.error('Parse user info error:', error);\r\n        }\r\n      }\r\n    };\r\n\r\n    // 监视车辆ID变化\r\n    watch(() => bookingForm.value.vehicleId, (newValue, oldValue) => {\r\n      console.log(`车辆ID从 ${oldValue} 变更为 ${newValue}`);\r\n      if (newValue && vehicles.value.length > 0) {\r\n        const selectedVehicle = vehicles.value.find(v => Number(v.id) === Number(newValue));\r\n        if (selectedVehicle) {\r\n          console.log(`选中的车辆: ${selectedVehicle.licensePlate} - ${selectedVehicle.brand} ${selectedVehicle.model}`);\r\n        }\r\n      }\r\n    }, { immediate: true });\r\n\r\n    onMounted(async () => {\r\n      // 初始化维修店数据\r\n      await initAllShops();\r\n      // 不再自动加载服务，等待选择维修店后再加载\r\n      await loadVehicles(); // 等待车辆加载完成，确保自动选择能正常工作\r\n      loadTechnicians();\r\n      loadUserInfo();\r\n    });\r\n\r\n    return {\r\n      bookingForm,\r\n      vehicleForm,\r\n      bookingRules,\r\n      loading,\r\n      addVehicleVisible,\r\n      services,\r\n      servicesLoading,\r\n      vehicles,\r\n      vehicleSelectLabel,\r\n      shops,\r\n      shopSearchResults,\r\n      allShops,\r\n      shopsLoading,\r\n      shopSearchFocused,\r\n      showShopDropdown,\r\n      shopOptions,\r\n      technicians,\r\n      selectedService,\r\n      disabledDate,\r\n      disabledTime,\r\n      locale,\r\n      getLevelText,\r\n      onServiceChange,\r\n      onVehicleChange,\r\n      onDateTimeChange,\r\n      initAllShops,\r\n      handleShopSearch,\r\n      handleShopSearchInput,\r\n      handleShopFocus,\r\n      handleShopBlur,\r\n      handleShopSelect,\r\n      clearShopSelection,\r\n      showAddVehicle,\r\n      goToVehicleManagement,\r\n      handleAddVehicle,\r\n      handleSubmit,\r\n      StarFilled\r\n    };\r\n  }\r\n});\r\n"]}]}