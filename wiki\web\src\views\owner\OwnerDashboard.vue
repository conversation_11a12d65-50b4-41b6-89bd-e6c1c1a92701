<template>
  <div class="owner-dashboard">
    <!-- 顶部欢迎区域 -->
    <div class="dashboard-header">
      <div class="welcome-section">
        <div class="welcome-content">
          <h1>欢迎回来，{{ userInfo.realName || userInfo.username }}</h1>
          <p>您的专属汽车维修服务管理中心</p>
          <div class="weather-info">
            <CloudOutlined />
            <span>今日天气：晴朗 22°C</span>
          </div>
        </div>
        <div class="user-avatar">
          <a-avatar :size="64" :src="userInfo.avatar">
            {{ userInfo.username && userInfo.username.charAt(0).toUpperCase() }}
          </a-avatar>
        </div>
      </div>
      <div class="user-actions">
        <a-button type="primary" size="large" @click="goToBooking">
          <CalendarOutlined />
          立即预约
        </a-button>
        <a-button @click="logout">
          <LogoutOutlined />
          退出登录
        </a-button>
      </div>
    </div>

    <div class="dashboard-content">
      <!-- 快速操作区域 -->
      <div class="quick-actions-section">
        <h2 class="section-title">
          <ThunderboltOutlined />
          快速操作
        </h2>
        <div class="quick-actions-grid">
          <div class="action-card" @click="goToBooking">
            <div class="action-icon booking">
              <CalendarOutlined />
            </div>
            <div class="action-content">
              <h3>预约维修</h3>
              <p>快速预约维修服务</p>
            </div>
          </div>
          <div class="action-card" @click="goToVehicles">
            <div class="action-icon vehicle">
              <CarOutlined />
            </div>
            <div class="action-content">
              <h3>我的车辆</h3>
              <p>管理车辆信息</p>
            </div>
          </div>
          <div class="action-card" @click="goToOrders">
            <div class="action-icon orders">
              <FileTextOutlined />
            </div>
            <div class="action-content">
              <h3>维修记录</h3>
              <p>查看历史记录</p>
            </div>
          </div>
          <div class="action-card" @click="goToProfile">
            <div class="action-icon profile">
              <UserOutlined />
            </div>
            <div class="action-content">
              <h3>个人设置</h3>
              <p>修改个人信息</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <a-row :gutter="24" class="main-content">
        <!-- 我的预约 -->
        <a-col :xs="24" :md="16">
          <a-card class="booking-card">
            <template #title>
              <div class="card-title">
                <ScheduleOutlined />
                <span>最近预约</span>
              </div>
            </template>
            <template #extra>
              <a-button type="link" @click="goToOrders">查看全部</a-button>
            </template>
            
            <div v-if="recentBookings.length === 0" class="empty-state">
              <a-empty description="暂无预约记录">
                <a-button type="primary" @click="goToBooking">立即预约</a-button>
              </a-empty>
            </div>
            
            <div v-else class="booking-list">
              <div 
                v-for="item in recentBookings" 
                :key="item.id" 
                class="booking-item"
              >
                <div class="booking-icon">
                  <a-avatar :style="{ backgroundColor: getStatusColor(item.status) }">
                    <ToolOutlined />
                  </a-avatar>
                </div>
                <div class="booking-info">
                  <h4>{{ item.serviceName }}</h4>
                  <p class="booking-time">
                    <ClockCircleOutlined />
                    {{ item.bookingDate }} {{ item.bookingTime }}
                  </p>
                  <a-tag :color="getStatusColor(item.status)">
                    {{ getStatusText(item.status) }}
                  </a-tag>
                </div>
                <div class="booking-actions">
                  <a-button size="small" type="link">详情</a-button>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>

        <!-- 统计信息 -->
        <a-col :xs="24" :md="8">
          <a-card class="stats-card">
            <template #title>
              <div class="card-title">
                <BarChartOutlined />
                <span>统计信息</span>
              </div>
            </template>
            
            <div class="stats-grid">
              <div class="stat-item">
                <div class="stat-icon total">
                  <CalendarOutlined />
                </div>
                <div class="stat-content">
                  <div class="stat-number">{{ stats.totalBookings }}</div>
                  <div class="stat-label">总预约次数</div>
                </div>
              </div>
              
              <div class="stat-item">
                <div class="stat-icon completed">
                  <CheckCircleOutlined />
                </div>
                <div class="stat-content">
                  <div class="stat-number">{{ stats.completedBookings }}</div>
                  <div class="stat-label">已完成维修</div>
                </div>
              </div>
              
              <div class="stat-item">
                <div class="stat-icon vehicles">
                  <CarOutlined />
                </div>
                <div class="stat-content">
                  <div class="stat-number">{{ stats.totalVehicles }}</div>
                  <div class="stat-label">车辆数量</div>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 热门服务推荐 -->
      <div class="services-section">
        <h2 class="section-title">
          <StarOutlined />
          热门服务推荐
        </h2>
        <div class="services-grid">
          <div 
            v-for="service in hotServices" 
            :key="service.id"
            class="service-card"
            @click="bookService(service.id)"
          >
            <div class="service-image">
              <img :src="service.image || '/image/default-service.png'" :alt="service.name" />
            </div>
            <div class="service-content">
              <h3>{{ service.name }}</h3>
              <p>{{ service.description }}</p>
              <div class="service-footer">
                <div class="service-price">¥{{ service.price }}</div>
                <a-button type="primary" size="small">立即预约</a-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import axios from 'axios';
import { clearUserSession, safeNavigate } from '../../utils/auth';
import {
  CalendarOutlined,
  CarOutlined,
  FileTextOutlined,
  UserOutlined,
  ToolOutlined,
  CloudOutlined,
  LogoutOutlined,
  ThunderboltOutlined,
  ScheduleOutlined,
  ClockCircleOutlined,
  BarChartOutlined,
  CheckCircleOutlined,
  StarOutlined
} from '@ant-design/icons-vue';

export default defineComponent({
  name: 'OwnerDashboard',
  components: {
    CalendarOutlined,
    CarOutlined,
    FileTextOutlined,
    UserOutlined,
    ToolOutlined,
    CloudOutlined,
    LogoutOutlined,
    ThunderboltOutlined,
    ScheduleOutlined,
    ClockCircleOutlined,
    BarChartOutlined,
    CheckCircleOutlined,
    StarOutlined
  },
  setup() {
    const router = useRouter();
    const loadingBookings = ref(false);
    
    const userInfo = ref<any>({});
    const recentBookings = ref<any[]>([]);
    const hotServices = ref<any[]>([]);
    const stats = ref({
      totalBookings: 0,
      completedBookings: 0,
      totalVehicles: 0
    });

    const goToBooking = () => {
      safeNavigate(router, '/booking');
    };

    const goToVehicles = () => {
      safeNavigate(router, '/owner/vehicles');
    };

    const goToOrders = () => {
      safeNavigate(router, '/owner/orders');
    };

    const goToProfile = () => {
      safeNavigate(router, '/owner/profile');
    };

    const logout = () => {
      clearUserSession();
      message.success('退出登录成功');
      // 强制刷新到首页确保状态完全清除
      window.location.href = '/';
    };

    const bookService = (serviceId: number) => {
      router.push({ path: '/booking', query: { serviceId } });
    };

    const getStatusColor = (status: number) => {
      const colors: Record<number, string> = {
        1: 'orange',
        2: 'blue', 
        3: 'cyan',
        4: 'green',
        5: 'red'
      };
      return colors[status] || 'default';
    };

    const getStatusText = (status: number) => {
      const texts: Record<number, string> = {
        1: '待确认',
        2: '已确认',
        3: '服务中', 
        4: '已完成',
        5: '已取消'
      };
      return texts[status] || '未知';
    };

    const loadUserInfo = () => {
      const userInfoStr = localStorage.getItem('userInfo');
      if (userInfoStr) {
        try {
          userInfo.value = JSON.parse(userInfoStr);
        } catch (error) {
          console.error('Parse user info error:', error);
        }
      }
    };

    const loadRecentBookings = async () => {
      loadingBookings.value = true;
      try {
        // TODO: 实现获取用户预约记录的API
        // const response = await axios.get('/owner/bookings/recent');
        // recentBookings.value = response.data.content;
        
        // 模拟数据
        recentBookings.value = [
          {
            id: 1,
            serviceName: '机油更换',
            bookingDate: '2024-01-15',
            bookingTime: '10:00',
            status: 2
          },
          {
            id: 2,
            serviceName: '轮胎更换',
            bookingDate: '2024-01-12',
            bookingTime: '14:30',
            status: 4
          }
        ];
      } catch (error) {
        console.error('Load bookings error:', error);
      } finally {
        loadingBookings.value = false;
      }
    };

    const loadHotServices = async () => {
      try {
        const response = await axios.get("/service/getServiceListByPage", {
          params: {
            page: 1,
            size: 4
          }
        });
        const data = response.data;
        if (data.success) {
          hotServices.value = data.content.list;
        }
      } catch (error) {
        console.error('Load services error:', error);
      }
    };

    const loadStats = () => {
      // TODO: 实现统计数据API
      stats.value = {
        totalBookings: 8,
        completedBookings: 6,
        totalVehicles: 2
      };
    };

    onMounted(() => {
      loadUserInfo();
      loadRecentBookings();
      loadHotServices();
      loadStats();
    });

    return {
      userInfo,
      recentBookings,
      hotServices,
      stats,
      loadingBookings,
      goToBooking,
      goToVehicles,
      goToOrders,
      goToProfile,
      logout,
      bookService,
      getStatusColor,
      getStatusText
    };
  }
});
</script>

<style scoped>
.owner-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0;
  position: relative;
}

.owner-dashboard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.03)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

/* 顶部头部区域 */
.dashboard-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 0 0 24px 24px;
  padding: 32px;
  margin-bottom: 32px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

.welcome-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.welcome-content h1 {
  margin: 0 0 8px 0;
  color: #1a1a1a;
  font-size: 32px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-content p {
  margin: 0 0 12px 0;
  color: #666;
  font-size: 16px;
}

.weather-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #888;
  font-size: 14px;
}

.user-avatar {
  margin-left: 24px;
}

.user-actions {
  display: flex;
  gap: 12px;
  margin-top: 16px;
}

.user-actions .ant-btn {
  height: 44px;
  border-radius: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 主要内容区域 */
.dashboard-content {
  padding: 0 32px 32px;
  position: relative;
  z-index: 1;
}

/* 快速操作区域 */
.quick-actions-section {
  margin-bottom: 32px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0 0 24px 0;
  color: white;
  font-size: 24px;
  font-weight: 600;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.action-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  gap: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.action-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.action-icon {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  flex-shrink: 0;
}

.action-icon.booking {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.action-icon.vehicle {
  background: linear-gradient(135deg, #f093fb, #f5576c);
}

.action-icon.orders {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.action-icon.profile {
  background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.action-content h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
}

.action-content p {
  margin: 0;
  font-size: 14px;
  color: #666;
}

/* 主要内容卡片 */
.main-content {
  margin-bottom: 32px;
}

.booking-card,
.stats-card {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
}

.card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

/* 预约列表 */
.empty-state {
  text-align: center;
  padding: 40px 0;
}

.booking-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.booking-item {
  display: flex;
  align-items: center;
  padding: 20px;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 12px;
  transition: all 0.3s;
}

.booking-item:hover {
  background: rgba(248, 250, 252, 1);
  transform: translateX(4px);
}

.booking-icon {
  margin-right: 16px;
}

.booking-info {
  flex: 1;
}

.booking-info h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.booking-time {
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 6px;
  color: #666;
  font-size: 14px;
}

/* 统计卡片 */
.stats-grid {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 12px;
  transition: all 0.3s;
}

.stat-item:hover {
  background: rgba(248, 250, 252, 1);
  transform: scale(1.02);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
  flex-shrink: 0;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.stat-icon.completed {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.stat-icon.vehicles {
  background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.stat-label {
  color: #666;
  font-size: 14px;
}

/* 服务推荐区域 */
.services-section {
  margin-bottom: 32px;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.service-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.service-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
}

.service-image {
  height: 180px;
  overflow: hidden;
  position: relative;
}

.service-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s;
}

.service-card:hover .service-image img {
  transform: scale(1.1);
}

.service-content {
  padding: 24px;
}

.service-content h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
}

.service-content p {
  margin: 0 0 20px 0;
  font-size: 14px;
  color: #666;
  line-height: 1.6;
}

.service-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.service-price {
  font-size: 20px;
  font-weight: 700;
  background: linear-gradient(135deg, #f093fb, #f5576c);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .quick-actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    padding: 24px 20px;
    border-radius: 0;
  }
  
  .dashboard-content {
    padding: 0 20px 24px;
  }
  
  .welcome-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .welcome-content h1 {
    font-size: 24px;
  }
  
  .quick-actions-grid {
    grid-template-columns: 1fr;
  }
  
  .action-card {
    padding: 20px;
  }
  
  .services-grid {
    grid-template-columns: 1fr;
  }
  
  .main-content .ant-col {
    margin-bottom: 24px;
  }
}

@media (max-width: 480px) {
  .dashboard-header {
    padding: 20px 16px;
  }
  
  .dashboard-content {
    padding: 0 16px 20px;
  }
  
  .section-title {
    font-size: 20px;
  }
  
  .action-card {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }
}
</style>
