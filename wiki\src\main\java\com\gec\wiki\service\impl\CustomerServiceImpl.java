package com.gec.wiki.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.gec.wiki.pojo.Customer;
import com.gec.wiki.service.CustomerService;
import com.gec.wiki.mapper.CustomerMapper;
import org.springframework.stereotype.Component;

import java.util.List;

/**
* @description 针对表【customer(客户信息)】的数据库操作Service实现
*/
@Component
public class CustomerServiceImpl extends ServiceImpl<CustomerMapper, Customer>
    implements CustomerService{

    @Override
    public List<Customer> getVipCustomers() {
        QueryWrapper<Customer> wrapper = new QueryWrapper<>();
        wrapper.ge("level", 2); // 获取VIP及以上级别的客户
        return this.list(wrapper);
    }
}
