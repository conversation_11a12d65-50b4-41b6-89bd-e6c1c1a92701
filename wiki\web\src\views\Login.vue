<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <img src="/image/default-service.png" alt="汽车维修" class="logo" />
        <h2>登录汽车维修服务平台</h2>
        <p>欢迎回来，请登录您的账户</p>
      </div>
      
      <a-form
        :model="loginForm"
        :rules="loginRules"
        @finish="handleLogin"
        layout="vertical"
        class="login-form"
      >
        <a-form-item name="username" label="用户名">
          <a-input
            v-model:value="loginForm.username"
            size="large"
            placeholder="请输入用户名/手机号"
            prefix=""
          >
            <template #prefix>
              <UserOutlined />
            </template>
          </a-input>
        </a-form-item>
        
        <a-form-item name="password" label="密码">
          <a-input-password
            v-model:value="loginForm.password"
            size="large"
            placeholder="请输入密码"
          >
            <template #prefix>
              <LockOutlined />
            </template>
          </a-input-password>
        </a-form-item>
        
        <a-form-item>
          <div class="login-options">
            <a-checkbox v-model:checked="rememberMe">记住我</a-checkbox>
            <a href="#" @click="showForgotPassword">忘记密码？</a>
          </div>
        </a-form-item>
        
        <!-- 锁定状态提示 -->
        <a-alert
          v-if="lockInfo.locked"
          :message="lockInfo.message"
          type="error"
          show-icon
          :style="{ marginBottom: '16px' }"
        />
        
        <a-form-item>
          <a-button
            type="primary"
            html-type="submit"
            size="large"
            :loading="loading"
            :disabled="lockInfo.locked"
            class="login-button"
          >
            {{ lockInfo.locked ? '账号已锁定' : '登录' }}
          </a-button>
        </a-form-item>
        
        <div class="register-link">
          还没有账户？ 
          <router-link to="/register">立即注册</router-link>
        </div>
      </a-form>
    </div>

    <!-- 忘记密码模态框 -->
    <a-modal
      v-model:visible="forgotPasswordVisible"
      :title="modalTitle"
      :width="500"
      :footer="null"
      @cancel="resetForgotPassword"
    >
      <!-- 步骤一：验证用户信息 -->
      <div v-if="currentStep === 1" class="forgot-step">
        <div class="step-header">
          <h3>验证身份信息</h3>
          <p>请输入您注册时的真实信息以验证身份</p>
        </div>
        
        <a-form
          :model="verifyForm"
          :rules="verifyRules"
          ref="verifyFormRef"
          layout="vertical"
        >
          <a-form-item name="realName" label="真实姓名">
            <a-input
              v-model:value="verifyForm.realName"
              placeholder="请输入您的真实姓名"
              size="large"
            >
              <template #prefix>
                <UserOutlined />
              </template>
            </a-input>
          </a-form-item>
          
          <a-form-item name="phone" label="手机号码">
            <a-input
              v-model:value="verifyForm.phone"
              placeholder="请输入您的手机号码"
              size="large"
            >
              <template #prefix>
                <PhoneOutlined />
              </template>
            </a-input>
          </a-form-item>
          
          <a-form-item name="email" label="邮箱地址">
            <a-input
              v-model:value="verifyForm.email"
              placeholder="请输入您的邮箱地址"
              size="large"
            >
              <template #prefix>
                <MailOutlined />
              </template>
            </a-input>
          </a-form-item>
          
          <div class="step-actions">
            <a-button @click="resetForgotPassword" style="margin-right: 12px;">
              取消
            </a-button>
            <a-button
              type="primary"
              :loading="verifyLoading"
              @click="handleVerifyUser"
            >
              验证身份
            </a-button>
          </div>
        </a-form>
      </div>

      <!-- 步骤二：输入QQ邮箱授权码 -->
      <div v-if="currentStep === 2" class="forgot-step">
        <div class="step-header">
          <h3>QQ邮箱授权码</h3>
          <p>请输入您的QQ邮箱（{{ verifyForm.email }}）的SMTP授权码</p>
          <div class="auth-code-help">
            <a href="#" @click="showAuthCodeHelp">如何获取QQ邮箱授权码？</a>
          </div>
        </div>
        
        <a-form
          :model="authCodeForm"
          :rules="authCodeRules"
          ref="authCodeFormRef"
          layout="vertical"
        >
          <a-form-item name="userEmail" label="您的QQ邮箱">
            <a-input
              v-model:value="authCodeForm.userEmail"
              placeholder="请输入您的QQ邮箱（用于发送验证码）"
              size="large"
            >
              <template #prefix>
                <MailOutlined />
              </template>
            </a-input>
          </a-form-item>
          
          <a-form-item name="authCode" label="SMTP授权码">
            <a-input-password
              v-model:value="authCodeForm.authCode"
              placeholder="请输入16位QQ邮箱SMTP授权码"
              size="large"
              maxlength="16"
            >
              <template #prefix>
                <KeyOutlined />
              </template>
            </a-input-password>
          </a-form-item>
          
          <div class="step-actions">
            <a-button @click="goBackToVerify" style="margin-right: 12px;">
              返回上一步
            </a-button>
            <a-button
              type="primary"
              :loading="authCodeLoading"
              @click="handleSendCodeWithAuth"
            >
              发送验证码
            </a-button>
          </div>
        </a-form>
      </div>

      <!-- 步骤三：输入验证码 -->
      <div v-if="currentStep === 3" class="forgot-step">
        <div class="step-header">
          <h3>邮箱验证</h3>
          <p>验证码已发送至 {{ maskedEmail }}，请查收邮件</p>
        </div>
        
        <a-form
          :model="codeForm"
          :rules="codeRules"
          ref="codeFormRef"
          layout="vertical"
        >
          <a-form-item name="verificationCode" label="验证码">
            <a-input
              v-model:value="codeForm.verificationCode"
              placeholder="请输入6位验证码"
              size="large"
              maxlength="6"
            >
              <template #prefix>
                <SafetyOutlined />
              </template>
            </a-input>
          </a-form-item>
          
          <div class="resend-section">
            <span v-if="countdown > 0" class="countdown-text">
              {{ countdown }}秒后可重新发送
            </span>
            <a
              v-else
              @click="resendCode"
              :disabled="resendLoading"
              class="resend-link"
            >
              重新发送验证码
            </a>
          </div>
          
          <div class="step-actions">
            <a-button @click="goBackToAuthCode" style="margin-right: 12px;">
              返回上一步
            </a-button>
            <a-button
              type="primary"
              :loading="codeLoading"
              @click="handleVerifyCode"
            >
              验证
            </a-button>
          </div>
        </a-form>
      </div>

      <!-- 步骤四：设置新密码 -->
      <div v-if="currentStep === 4" class="forgot-step">
        <div class="step-header">
          <h3>设置新密码</h3>
          <p>请为您的账号设置新密码</p>
        </div>
        
        <a-form
          :model="newPasswordForm"
          :rules="newPasswordRules"
          ref="newPasswordFormRef"
          layout="vertical"
        >
          <a-form-item name="newPassword" label="新密码">
            <a-input-password
              v-model:value="newPasswordForm.newPassword"
              placeholder="请输入新密码（6-20位）"
              size="large"
            >
              <template #prefix>
                <LockOutlined />
              </template>
            </a-input-password>
          </a-form-item>
          
          <a-form-item name="confirmPassword" label="确认密码">
            <a-input-password
              v-model:value="newPasswordForm.confirmPassword"
              placeholder="请再次输入新密码"
              size="large"
            >
              <template #prefix>
                <LockOutlined />
              </template>
            </a-input-password>
          </a-form-item>
          
          <div class="step-actions">
            <a-button @click="goBackToCode" style="margin-right: 12px;">
              返回上一步
            </a-button>
            <a-button
              type="primary"
              :loading="resetPasswordLoading"
              @click="handleResetPassword"
            >
              重置密码
            </a-button>
          </div>
        </a-form>
      </div>

      <!-- 步骤五：密码重置成功 -->
      <div v-if="currentStep === 5" class="forgot-step">
        <div class="step-header success">
          <div class="success-icon">
            <CheckCircleOutlined />
          </div>
          <h3>密码重置成功</h3>
          <p>您的密码已成功重置，现在可以使用新密码登录了</p>
        </div>
        
        <div class="account-info">
          <div class="info-item">
            <label>用户名：</label>
            <span class="info-value">{{ recoveredAccount.username }}</span>
            <a-button
              type="text"
              size="small"
              @click="copyToClipboard(recoveredAccount.username)"
              class="copy-btn"
            >
              <CopyOutlined /> 复制
            </a-button>
          </div>
          
          <div class="success-message">
            <p>✅ 密码重置成功！</p>
            <p>请使用您刚才设置的新密码登录系统。</p>
          </div>
        </div>
        
        <div class="step-actions">
          <a-button type="primary" @click="goToLogin" class="full-width">
            返回登录
          </a-button>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onUnmounted, watch } from 'vue';
import { 
  UserOutlined, 
  LockOutlined, 
  PhoneOutlined, 
  MailOutlined, 
  SafetyOutlined,
  CheckCircleOutlined,
  CopyOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  KeyOutlined
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import axios from 'axios';
import { useRouter } from 'vue-router';
import { setUserSession, getDefaultDashboard } from '../utils/auth';

export default defineComponent({
  name: 'Login',
  components: {
    UserOutlined,
    LockOutlined,
    PhoneOutlined,
    MailOutlined,
    SafetyOutlined,
    CheckCircleOutlined,
    CopyOutlined,
    EyeOutlined,
    EyeInvisibleOutlined,
    KeyOutlined
  },
  setup() {
    const router = useRouter();
    const loading = ref(false);
    const rememberMe = ref(false);
    
    // 登录表单
    const loginForm = ref({
      username: '',
      password: ''
    });
    
    // 锁定信息
    const lockInfo = ref({
      locked: false,
      message: '',
      lockedUntil: null,
      failCount: 0,
      maxFailCount: 5
    });
    
    const loginRules = {
      username: [
        { required: true, message: '请输入用户名', trigger: 'blur' }
      ],
      password: [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 6, message: '密码至少6位', trigger: 'blur' }
      ]
    };

    // 忘记密码相关状态
    const forgotPasswordVisible = ref(false);
    const currentStep = ref(1);
    const verifyLoading = ref(false);
    const authCodeLoading = ref(false);
    const codeLoading = ref(false);
    const resendLoading = ref(false);
    const resetPasswordLoading = ref(false);
    const countdown = ref(0);
    const showPassword = ref(false);
    let countdownTimer: any = null;

    // 验证用户信息表单
    const verifyForm = ref({
      realName: '',
      phone: '',
      email: ''
    });

    const verifyRules = {
      realName: [
        { required: true, message: '请输入真实姓名', trigger: 'blur' },
        { min: 2, message: '姓名至少2个字符', trigger: 'blur' }
      ],
      phone: [
        { required: true, message: '请输入手机号码', trigger: 'blur' },
        { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
      ],
      email: [
        { required: true, message: '请输入邮箱地址', trigger: 'blur' },
        { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
      ]
    };

    // 授权码表单
    const authCodeForm = ref({
      userEmail: '',
      authCode: ''
    });

    const authCodeRules = {
      userEmail: [
        { required: true, message: '请输入您的QQ邮箱', trigger: 'blur' },
        { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' },
        { pattern: /^[a-zA-Z0-9._%+-]+@qq\.com$/, message: '请输入QQ邮箱地址', trigger: 'blur' }
      ],
      authCode: [
        { required: true, message: '请输入QQ邮箱SMTP授权码', trigger: 'blur' },
        { len: 16, message: '授权码必须是16位字符', trigger: 'blur' }
      ]
    };

    // 验证码表单
    const codeForm = ref({
      verificationCode: ''
    });

    const codeRules = {
      verificationCode: [
        { required: true, message: '请输入验证码', trigger: 'blur' },
        { pattern: /^\d{6}$/, message: '请输入6位数字验证码', trigger: 'blur' }
      ]
    };

    // 新密码表单
    const newPasswordForm = ref({
      newPassword: '',
      confirmPassword: ''
    });

    const newPasswordRules = {
      newPassword: [
        { required: true, message: '请输入新密码', trigger: 'blur' },
        { min: 6, message: '密码至少6位', trigger: 'blur' },
        { max: 20, message: '密码不能超过20位', trigger: 'blur' }
      ],
      confirmPassword: [
        { required: true, message: '请确认密码', trigger: 'blur' },
        { 
          validator: (rule: any, value: string) => {
            if (value && value !== newPasswordForm.value.newPassword) {
              return Promise.reject('两次输入的密码不一致');
            }
            return Promise.resolve();
          },
          trigger: 'blur'
        }
      ]
    };

    // 找回的账号信息
    const recoveredAccount = ref({
      username: '',
      password: ''
    });

    // 计算属性
    const modalTitle = computed(() => {
      const titles = {
        1: '忘记密码 - 验证身份',
        2: '忘记密码 - QQ邮箱授权',
        3: '忘记密码 - 邮箱验证',
        4: '忘记密码 - 设置新密码',
        5: '密码重置成功'
      };
      return titles[currentStep.value as keyof typeof titles] || '忘记密码';
    });

    const maskedEmail = computed(() => {
      const email = verifyForm.value.email;
      if (!email) return '';
      const [username, domain] = email.split('@');
      if (username.length <= 3) {
        return `${username[0]}***@${domain}`;
      }
      return `${username.slice(0, 2)}***${username.slice(-1)}@${domain}`;
    });

    // 登录功能
    const handleLogin = async () => {
      loading.value = true;
      try {
        const response = await axios.post('/auth/login', loginForm.value);
        const data = response.data;
        
        if (data.success) {
          // 登录成功，清除锁定状态
          clearLockInfo();
          message.success('登录成功');
          
          // 验证返回的用户信息
          const { token, userInfo } = data.content;
          if (!token || !userInfo || !userInfo.userType || ![1, 2, 3].includes(userInfo.userType)) {
            message.error('登录返回的用户信息无效');
            return;
          }
          
          // 使用权限工具保存用户信息
          setUserSession(token, userInfo);
          
          // 根据用户类型跳转到对应Dashboard
          const defaultPath = getDefaultDashboard(userInfo.userType);
          router.push(defaultPath);
        } else {
          // 登录失败，检查是否有锁定信息
          if (data.content && data.content.locked) {
            handleLockInfo(data.content);
          } else {
            // 普通的登录失败
            message.error(data.message || '登录失败');
          }
        }
      } catch (error) {
        message.error('登录失败，请检查网络连接');
        console.error('Login error:', error);
      } finally {
        loading.value = false;
      }
    };
    
    // 处理锁定信息
    const handleLockInfo = (lockData: any) => {
      lockInfo.value = {
        locked: lockData.locked,
        message: `账号已被锁定，剩余失败次数: ${lockData.failCount}/${lockData.maxFailCount}`,
        lockedUntil: lockData.lockedUntil,
        failCount: lockData.failCount,
        maxFailCount: lockData.maxFailCount
      };
      
      if (lockData.lockedUntil) {
        const lockUntilTime = new Date(lockData.lockedUntil.replace('T', ' '));
        const now = new Date();
        const remainingMinutes = Math.ceil((lockUntilTime.getTime() - now.getTime()) / 60000);
        
        if (remainingMinutes > 0) {
          lockInfo.value.message = `账号已被锁定，请在${remainingMinutes}分钟后重试`;
          
          // 启动倒计时
          startLockCountdown(lockUntilTime);
        } else {
          // 锁定已过期
          clearLockInfo();
        }
      }
    };
    
    // 启动锁定倒计时
    const startLockCountdown = (lockUntilTime: Date) => {
      const updateCountdown = () => {
        const now = new Date();
        const remainingMs = lockUntilTime.getTime() - now.getTime();
        
        if (remainingMs <= 0) {
          clearLockInfo();
          message.info('账号锁定已解除，可以重新登录');
          return;
        }
        
        const remainingMinutes = Math.ceil(remainingMs / 60000);
        lockInfo.value.message = `账号已被锁定，请在${remainingMinutes}分钟后重试`;
        
        setTimeout(updateCountdown, 30000); // 每30秒更新一次
      };
      
      updateCountdown();
    };
    
    // 清除锁定信息
    const clearLockInfo = () => {
      lockInfo.value = {
        locked: false,
        message: '',
        lockedUntil: null,
        failCount: 0,
        maxFailCount: 5
      };
    };

    // 监听用户名变化，清除锁定状态
    watch(() => loginForm.value.username, () => {
      if (lockInfo.value.locked) {
        clearLockInfo();
      }
    });

    // 忘记密码功能
    const showForgotPassword = () => {
      forgotPasswordVisible.value = true;
      currentStep.value = 1;
    };

    const resetForgotPassword = () => {
      forgotPasswordVisible.value = false;
      currentStep.value = 1;
      verifyForm.value = { realName: '', phone: '', email: '' };
      authCodeForm.value = { userEmail: '', authCode: '' };
      codeForm.value = { verificationCode: '' };
      newPasswordForm.value = { newPassword: '', confirmPassword: '' };
      recoveredAccount.value = { username: '', password: '' };
      showPassword.value = false;
      stopCountdown();
    };

    const handleVerifyUser = async () => {
      try {
        verifyLoading.value = true;
        
        // 调用真实API进行身份验证
        const response = await axios.post('/auth/verify-user-info', verifyForm.value);
        const data = response.data;
        
        if (data.success) {
          message.success('身份验证成功，请提供QQ邮箱授权码');
          
          // 预填充用户邮箱（如果是QQ邮箱的话）
          if (verifyForm.value.email.endsWith('@qq.com')) {
            authCodeForm.value.userEmail = verifyForm.value.email;
          }
          currentStep.value = 2;
        } else {
          message.error(data.message || '未找到匹配的用户信息，请检查姓名、手机号和邮箱是否正确');
        }
        
      } catch (error: any) {
        message.error('验证失败，请稍后重试');
        console.error('Verify user error:', error);
      } finally {
        verifyLoading.value = false;
      }
    };

    const handleSendCodeWithAuth = async () => {
      try {
        authCodeLoading.value = true;
        
        // 调用新的API发送验证码
        const response = await axios.post('/auth/send-verification-code-with-auth', {
          email: verifyForm.value.email,
          userEmail: authCodeForm.value.userEmail,
          authCode: authCodeForm.value.authCode,
          type: 'forgot_password'
        });
        
        const data = response.data;
        
        if (data.success) {
          message.success('验证码已发送至您的邮箱');
          currentStep.value = 3;
          startCountdown();
        } else {
          message.error(data.message || '发送验证码失败，请检查授权码是否正确');
        }
        
      } catch (error: any) {
        message.error('发送验证码失败，请检查授权码是否正确');
        console.error('Send code with auth error:', error);
      } finally {
        authCodeLoading.value = false;
      }
    };

    const sendVerificationCode = async () => {
      try {
        const response = await axios.post('/auth/send-verification-code', {
          email: verifyForm.value.email,
          type: 'forgot_password'
        });
        
        if (!response.data.success) {
          throw new Error(response.data.message || '发送验证码失败');
        }
        
        message.success('验证码已发送至您的邮箱');
      } catch (error) {
        message.error('发送验证码失败，请稍后重试');
        throw error;
      }
    };

    const handleVerifyCode = async () => {
      try {
        codeLoading.value = true;
        
        // 调用真实API进行验证码验证和密码找回
        const response = await axios.post('/auth/verify-code-and-recover', {
          email: verifyForm.value.email,
          verificationCode: codeForm.value.verificationCode,
          realName: verifyForm.value.realName,
          phone: verifyForm.value.phone
        });
        
        const data = response.data;
        
        if (data.success) {
          // 存储用户信息，但不显示密码
          recoveredAccount.value = { username: data.content.username, password: '' };
          currentStep.value = 4;
          message.success('验证成功！请设置您的新密码');
        } else {
          message.error(data.message || '验证码错误或已过期');
        }
      } catch (error) {
        message.error('验证失败，请检查验证码是否正确');
        console.error('Verify code error:', error);
      } finally {
        codeLoading.value = false;
      }
    };

    const resendCode = async () => {
      try {
        resendLoading.value = true;
        await handleSendCodeWithAuth();
        startCountdown();
      } catch (error) {
        console.error('Resend code error:', error);
      } finally {
        resendLoading.value = false;
      }
    };

    const startCountdown = () => {
      countdown.value = 60;
      countdownTimer = setInterval(() => {
        countdown.value--;
        if (countdown.value <= 0) {
          stopCountdown();
        }
      }, 1000);
    };

    const stopCountdown = () => {
      if (countdownTimer) {
        clearInterval(countdownTimer);
        countdownTimer = null;
      }
      countdown.value = 0;
    };

    const goBackToVerify = () => {
      currentStep.value = 1;
      authCodeForm.value = { userEmail: '', authCode: '' };
    };

    const goBackToAuthCode = () => {
      currentStep.value = 2;
      codeForm.value.verificationCode = '';
      stopCountdown();
    };

    const goBackToCode = () => {
      currentStep.value = 3;
      newPasswordForm.value = { newPassword: '', confirmPassword: '' };
    };

    const handleResetPassword = async () => {
      try {
        resetPasswordLoading.value = true;
        
        // 调用密码重置API
        const response = await axios.post('/auth/reset-password', {
          email: verifyForm.value.email,
          realName: verifyForm.value.realName,
          phone: verifyForm.value.phone,
          newPassword: newPasswordForm.value.newPassword
        });
        
        const data = response.data;
        
        if (data.success) {
          currentStep.value = 5;
          message.success('密码重置成功！');
        } else {
          message.error(data.message || '密码重置失败，请稍后重试');
        }
      } catch (error) {
        message.error('密码重置失败，请稍后重试');
        console.error('Reset password error:', error);
      } finally {
        resetPasswordLoading.value = false;
      }
    };

    const goToLogin = () => {
      resetForgotPassword();
    };

    const togglePasswordVisibility = () => {
      showPassword.value = !showPassword.value;
    };

    const copyToClipboard = async (text: string) => {
      try {
        await navigator.clipboard.writeText(text);
        message.success('已复制到剪贴板');
      } catch (error) {
        // 降级方案
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        message.success('已复制到剪贴板');
      }
    };

    const showAuthCodeHelp = () => {
      message.info({
        content: '请按以下步骤获取QQ邮箱授权码：\n1. 登录QQ邮箱网页版\n2. 进入设置 → 账户\n3. 开启IMAP/SMTP服务\n4. 点击"生成授权码"\n5. 发送短信验证后获得16位授权码',
        duration: 8
      });
    };

    // 组件卸载时清理定时器
    onUnmounted(() => {
      stopCountdown();
    });
    
    return {
      // 登录相关
      loginForm,
      loginRules,
      loading,
      rememberMe,
      lockInfo,
      handleLogin,
      handleLockInfo,
      startLockCountdown,
      clearLockInfo,
      
      // 忘记密码相关
      forgotPasswordVisible,
      currentStep,
      modalTitle,
      verifyForm,
      verifyRules,
      verifyLoading,
      authCodeForm,
      authCodeRules,
      authCodeLoading,
      codeForm,
      codeRules,
      codeLoading,
      resendLoading,
      newPasswordForm,
      newPasswordRules,
      resetPasswordLoading,
      countdown,
      maskedEmail,
      recoveredAccount,
      showPassword,
      
      // 方法
      showForgotPassword,
      resetForgotPassword,
      handleVerifyUser,
      handleSendCodeWithAuth,
      handleVerifyCode,
      resendCode,
      goBackToVerify,
      goBackToAuthCode,
      goBackToCode,
      handleResetPassword,
      goToLogin,
      togglePasswordVisibility,
      copyToClipboard,
      showAuthCodeHelp
    };
  }
});
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-box {
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.logo {
  width: 60px;
  height: 60px;
  margin-bottom: 16px;
}

.login-header h2 {
  color: #333;
  margin-bottom: 8px;
  font-size: 24px;
}

.login-header p {
  color: #666;
  margin-bottom: 0;
}

.login-form {
  margin-top: 32px;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.login-options a {
  color: #1890ff;
  text-decoration: none;
}

.login-options a:hover {
  text-decoration: underline;
}

.login-button {
  width: 100%;
  height: 44px;
  font-size: 16px;
}

.register-link {
  text-align: center;
  margin-top: 24px;
  color: #666;
}

.register-link a {
  color: #1890ff;
  text-decoration: none;
}

.register-link a:hover {
  text-decoration: underline;
}

/* 忘记密码模态框样式 */
.forgot-step {
  padding: 20px 0;
}

.step-header {
  text-align: center;
  margin-bottom: 24px;
}

.step-header h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  color: #333;
}

.step-header p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.step-header.success {
  margin-bottom: 32px;
}

.success-icon {
  font-size: 48px;
  color: #52c41a;
  margin-bottom: 16px;
}

.step-actions {
  margin-top: 24px;
  text-align: right;
}

.step-actions .full-width {
  width: 100%;
}

.resend-section {
  margin-bottom: 16px;
  text-align: center;
}

.countdown-text {
  color: #666;
  font-size: 14px;
}

.resend-link {
  color: #1890ff;
  text-decoration: none;
  font-size: 14px;
}

.resend-link:hover {
  text-decoration: underline;
}

.resend-link:disabled {
  color: #d9d9d9;
  cursor: not-allowed;
}

.account-info {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e8e8e8;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item label {
  font-weight: 500;
  color: #333;
  min-width: 70px;
  margin-right: 12px;
}

.info-value {
  flex: 1;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  color: #1890ff;
  font-weight: 500;
}

.password-value {
  letter-spacing: 2px;
}

.copy-btn, .toggle-btn {
  margin-left: 8px;
  color: #666;
}

.copy-btn:hover, .toggle-btn:hover {
  color: #1890ff;
}

.success-message {
  text-align: center;
  padding: 20px;
  background: #f6ffed;
  border-radius: 6px;
  border: 1px solid #b7eb8f;
  margin-top: 16px;
}

.success-message p {
  margin: 8px 0;
  color: #52c41a;
  font-weight: 500;
}

.success-message p:first-child {
  font-size: 16px;
  margin-bottom: 12px;
}

.auth-code-help {
  margin-top: 12px;
  text-align: center;
}

.auth-code-help a {
  color: #1890ff;
  text-decoration: none;
  font-size: 14px;
}

.auth-code-help a:hover {
  text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 576px) {
  .step-actions {
    text-align: center;
  }
  
  .step-actions .ant-btn {
    margin: 0 4px 8px 4px;
  }
  
  .info-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .info-item label {
    min-width: auto;
    margin-bottom: 8px;
  }
  
  .info-value {
    width: 100%;
    word-break: break-all;
  }
  
  .copy-btn, .toggle-btn {
    margin-left: 0;
    margin-top: 8px;
  }
}
</style>
