<template>
  <div class="order-management">
    <div class="page-header">
      <h2>订单管理</h2>
      <a-button type="primary" @click="refreshOrders">
        <ReloadOutlined />
        刷新
      </a-button>
    </div>

    <div class="order-stats">
      <a-row :gutter="16">
        <a-col :span="6">
          <a-card class="stat-card pending">
            <div class="stat-content">
              <div class="stat-number">{{ pendingCount }}</div>
              <div class="stat-label">待处理订单</div>
            </div>
            <ClockCircleOutlined class="stat-icon" />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card processing">
            <div class="stat-content">
              <div class="stat-number">{{ processingCount }}</div>
              <div class="stat-label">进行中订单</div>
            </div>
            <ToolOutlined class="stat-icon" />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card completed">
            <div class="stat-content">
              <div class="stat-number">{{ completedCount }}</div>
              <div class="stat-label">已完成订单</div>
            </div>
            <CheckCircleOutlined class="stat-icon" />
          </a-card>
        </a-col>
        <a-col :span="6">
          <a-card class="stat-card revenue">
            <div class="stat-content">
              <div class="stat-number">¥{{ totalRevenue }}</div>
              <div class="stat-label">本月收入</div>
            </div>
            <DollarOutlined class="stat-icon" />
          </a-card>
        </a-col>
      </a-row>
    </div>

    <a-card class="order-table-card">
      <template #title>
        <div class="table-header">
          <span>订单列表</span>
          <div class="table-actions">
            <a-select v-model:value="statusFilter" style="width: 120px;" @change="handleFilterChange">
              <a-select-option value="">全部状态</a-select-option>
              <a-select-option value="1">待处理</a-select-option>
              <a-select-option value="2">已确认</a-select-option>
              <a-select-option value="3">进行中</a-select-option>
              <a-select-option value="4">已完成</a-select-option>
              <a-select-option value="5">已取消</a-select-option>
            </a-select>
          </div>
        </div>
      </template>

      <a-table 
        :dataSource="orders" 
        :columns="columns" 
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
        rowKey="id"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="getStatusColor(record.status)">
              {{ getStatusText(record.status) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button 
                v-if="record.status === 1" 
                type="primary" 
                size="small"
                @click="confirmOrder(record.id)"
              >
                确认订单
              </a-button>
              <a-button 
                v-if="record.status === 2" 
                type="primary" 
                size="small"
                @click="startService(record.id)"
              >
                开始服务
              </a-button>
              <a-button 
                v-if="record.status === 3" 
                type="primary" 
                size="small"
                @click="completeService(record.id)"
              >
                完成服务
              </a-button>
              <a-button 
                size="small"
                @click="viewOrderDetail(record.id)"
              >
                查看详情
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
    </a-card>

    <!-- 订单详情模态框 -->
    <a-modal
      v-model:visible="detailVisible"
      title="订单详情"
      width="800px"
      :footer="null"
    >
      <div v-if="selectedOrder" class="order-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="订单编号">{{ selectedOrder.orderNumber }}</a-descriptions-item>
          <a-descriptions-item label="客户姓名">{{ selectedOrder.customerName }}</a-descriptions-item>
          <a-descriptions-item label="联系电话">{{ selectedOrder.customerPhone }}</a-descriptions-item>
          <a-descriptions-item label="车辆信息">{{ selectedOrder.vehicleInfo }}</a-descriptions-item>
          <a-descriptions-item label="服务项目">{{ selectedOrder.serviceName }}</a-descriptions-item>
          <a-descriptions-item label="预约时间">{{ selectedOrder.appointmentTime }}</a-descriptions-item>
          <a-descriptions-item label="订单状态">
            <a-tag :color="getStatusColor(selectedOrder.status)">
              {{ getStatusText(selectedOrder.status) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="订单金额">¥{{ selectedOrder.amount }}</a-descriptions-item>
          <a-descriptions-item label="特殊要求" :span="2">
            {{ selectedOrder.requirements || '无' }}
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { 
  ReloadOutlined, 
  ClockCircleOutlined, 
  ToolOutlined, 
  CheckCircleOutlined,
  DollarOutlined 
} from '@ant-design/icons-vue';
import axios from 'axios';

// 响应式数据
const loading = ref(false);
const orders = ref([]);
const statusFilter = ref('');
const detailVisible = ref(false);
const selectedOrder = ref(null);

// 统计数据
const pendingCount = ref(8);
const processingCount = ref(5);
const completedCount = ref(156);
const totalRevenue = ref(28600);

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true,
  showTotal: (total: number) => `共 ${total} 条数据`
});

// 表格列定义
const columns = [
  {
    title: '订单编号',
    dataIndex: 'orderNumber',
    key: 'orderNumber',
    width: 150
  },
  {
    title: '客户姓名',
    dataIndex: 'customerName',
    key: 'customerName',
    width: 120
  },
  {
    title: '联系电话',
    dataIndex: 'customerPhone',
    key: 'customerPhone',
    width: 130
  },
  {
    title: '服务项目',
    dataIndex: 'serviceName',
    key: 'serviceName',
    width: 150
  },
  {
    title: '预约时间',
    dataIndex: 'appointmentTime',
    key: 'appointmentTime',
    width: 160
  },
  {
    title: '订单状态',
    key: 'status',
    width: 100
  },
  {
    title: '订单金额',
    dataIndex: 'amount',
    key: 'amount',
    width: 100,
    customRender: ({ text }: { text: number }) => `¥${text}`
  },
  {
    title: '操作',
    key: 'action',
    width: 200
  }
];

// 状态颜色映射
const getStatusColor = (status: number): string => {
  const colors: Record<number, string> = {
    1: 'orange',     // 待处理
    2: 'blue',       // 已确认
    3: 'processing', // 进行中
    4: 'success',    // 已完成
    5: 'error'       // 已取消
  };
  return colors[status] || 'default';
};

// 状态文本映射
const getStatusText = (status: number): string => {
  const texts: Record<number, string> = {
    1: '待处理',
    2: '已确认',
    3: '进行中',
    4: '已完成',
    5: '已取消'
  };
  return texts[status] || '未知';
};

// 加载订单数据
const loadOrders = async () => {
  try {
    loading.value = true;
    const params = {
      page: pagination.current,
      size: pagination.pageSize,
      status: statusFilter.value
    };
    
    const response = await axios.get('/api/shop/orders', { params });
    
    if (response.data.success) {
      orders.value = response.data.content.list || [];
      pagination.total = response.data.content.total || 0;
    } else {
      message.error('加载订单数据失败');
    }
  } catch (error) {
    console.error('加载订单失败:', error);
    // 模拟数据用于演示
    orders.value = [
      {
        id: 1,
        orderNumber: 'ORD202501040001',
        customerName: '张先生',
        customerPhone: '13800138001',
        serviceName: '机油更换',
        vehicleInfo: '本田雅阁 京A12345',
        appointmentTime: '2025-01-04 09:00',
        status: 1,
        amount: 350,
        requirements: '希望使用全合成机油'
      },
      {
        id: 2,
        orderNumber: 'ORD202501040002',
        customerName: '李女士',
        customerPhone: '13800138002',
        serviceName: '轮胎更换',
        vehicleInfo: '大众帕萨特 京B56789',
        appointmentTime: '2025-01-04 14:30',
        status: 2,
        amount: 1200,
        requirements: '四个轮胎一起换'
      },
      {
        id: 3,
        orderNumber: 'ORD202501040003',
        customerName: '王先生',
        customerPhone: '13800138003',
        serviceName: '刹车维修',
        vehicleInfo: '奥迪A6 京C98765',
        appointmentTime: '2025-01-04 16:00',
        status: 3,
        amount: 800,
        requirements: '前刹车片需要更换'
      }
    ];
    pagination.total = orders.value.length;
  } finally {
    loading.value = false;
  }
};

// 加载统计数据
const loadStats = async () => {
  try {
    const response = await axios.get('/api/shop/orders/stats');
    
    if (response.data.success) {
      const stats = response.data.content;
      pendingCount.value = stats.pendingOrders || 0;
      processingCount.value = stats.processingOrders || 0;
      completedCount.value = stats.completedOrders || 0;
      totalRevenue.value = stats.monthlyRevenue || 0;
    } else {
      message.error('加载统计数据失败');
    }
  } catch (error) {
    console.error('加载统计数据失败:', error);
    // 保留默认值作为备用
  }
};

// 刷新订单
const refreshOrders = () => {
  loadOrders();
  loadStats();
};

// 筛选变化处理
const handleFilterChange = () => {
  pagination.current = 1;
  loadOrders();
};

// 表格变化处理
const handleTableChange = (pag: any) => {
  pagination.current = pag.current;
  pagination.pageSize = pag.pageSize;
  loadOrders();
};

// 确认订单
const confirmOrder = async (orderId: number) => {
  try {
    const response = await axios.post(`/api/shop/orders/${orderId}/confirm`);
    if (response.data.success) {
      message.success('订单已确认');
      loadOrders();
      loadStats();
    } else {
      message.error(response.data.message || '确认订单失败');
    }
  } catch (error) {
    console.error('确认订单失败:', error);
    message.error('确认订单失败');
  }
};

// 开始服务
const startService = async (orderId: number) => {
  try {
    const response = await axios.post(`/api/shop/orders/${orderId}/start`);
    if (response.data.success) {
      message.success('服务已开始');
      loadOrders();
      loadStats();
    } else {
      message.error(response.data.message || '开始服务失败');
    }
  } catch (error) {
    console.error('开始服务失败:', error);
    message.error('开始服务失败');
  }
};

// 完成服务
const completeService = async (orderId: number) => {
  try {
    const response = await axios.post(`/api/shop/orders/${orderId}/complete`);
    if (response.data.success) {
      message.success('服务已完成');
      loadOrders();
      loadStats();
    } else {
      message.error(response.data.message || '完成服务失败');
    }
  } catch (error) {
    console.error('完成服务失败:', error);
    message.error('完成服务失败');
  }
};

// 查看订单详情
const viewOrderDetail = (orderId: number) => {
  const order = orders.value.find((o: any) => o.id === orderId);
  if (order) {
    selectedOrder.value = order;
    detailVisible.value = true;
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadOrders();
  loadStats();
});
</script>

<style scoped>
.order-management {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-header h2 {
  margin: 0;
  color: #1890ff;
  font-size: 24px;
  font-weight: 600;
}

.order-stats {
  margin-bottom: 24px;
}

.stat-card {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
}

.stat-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  transform: translateY(-2px);
}

.stat-card.pending {
  border-left: 4px solid #faad14;
}

.stat-card.processing {
  border-left: 4px solid #1890ff;
}

.stat-card.completed {
  border-left: 4px solid #52c41a;
}

.stat-card.revenue {
  border-left: 4px solid #722ed1;
}

.stat-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.stat-number {
  font-size: 28px;
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.stat-icon {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32px;
  color: #d9d9d9;
}

.order-table-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-header span {
  font-weight: 600;
  color: #1890ff;
}

.order-detail {
  margin-top: 16px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .order-stats :deep(.ant-col) {
    margin-bottom: 16px;
  }
}

@media (max-width: 768px) {
  .order-management {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .stat-number {
    font-size: 24px;
  }
  
  .table-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
}
</style>
