package com.gec.wiki.controller.admin;

import com.gec.wiki.domain.SecuritySettings;
import com.gec.wiki.pojo.resp.CommonResp;
import com.gec.wiki.service.SecuritySettingsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 管理员安全设置控制器
 */
@RestController
@RequestMapping("/api/admin")
public class AdminSecurityController {
    
    private static final Logger LOG = LoggerFactory.getLogger(AdminSecurityController.class);
    
    @Resource
    private SecuritySettingsService securitySettingsService;
    
    /**
     * 获取安全设置
     */
    @GetMapping("/security-settings")
    public CommonResp<SecuritySettings> getSecuritySettings() {
        CommonResp<SecuritySettings> resp = new CommonResp<>();
        
        try {
            SecuritySettings settings = securitySettingsService.getSecuritySettings();
            resp.setSuccess(true);
            resp.setContent(settings);
            resp.setMessage("获取安全设置成功");
            
            LOG.info("管理员获取安全设置成功: {}", settings);
            
        } catch (Exception e) {
            LOG.error("获取安全设置失败", e);
            resp.setSuccess(false);
            resp.setMessage("获取安全设置失败: " + e.getMessage());
        }
        
        return resp;
    }
    
    /**
     * 保存安全设置
     */
    @PostMapping("/security-settings")
    public CommonResp<Boolean> saveSecuritySettings(@RequestBody SecuritySettings securitySettings) {
        CommonResp<Boolean> resp = new CommonResp<>();
        
        try {
            // 参数验证
            if (securitySettings == null) {
                resp.setSuccess(false);
                resp.setMessage("安全设置参数不能为空");
                return resp;
            }
            
            // 验证参数范围
            if (securitySettings.getMaxLoginFailCount() != null && 
                (securitySettings.getMaxLoginFailCount() < 3 || securitySettings.getMaxLoginFailCount() > 10)) {
                resp.setSuccess(false);
                resp.setMessage("登录错误次数限制必须在3-10次之间");
                return resp;
            }
            
            if (securitySettings.getLockDurationMinutes() != null && 
                (securitySettings.getLockDurationMinutes() < 5 || securitySettings.getLockDurationMinutes() > 1440)) {
                resp.setSuccess(false);
                resp.setMessage("锁定时间必须在5-1440分钟之间");
                return resp;
            }
            
            if (securitySettings.getMinPasswordLength() != null && 
                (securitySettings.getMinPasswordLength() < 6 || securitySettings.getMinPasswordLength() > 20)) {
                resp.setSuccess(false);
                resp.setMessage("密码最小长度必须在6-20位之间");
                return resp;
            }
            
            boolean success = securitySettingsService.saveSecuritySettings(securitySettings);
            
            if (success) {
                resp.setSuccess(true);
                resp.setContent(true);
                resp.setMessage("安全设置保存成功");
                LOG.info("管理员保存安全设置成功: {}", securitySettings);
            } else {
                resp.setSuccess(false);
                resp.setMessage("安全设置保存失败");
                LOG.warn("管理员保存安全设置失败: {}", securitySettings);
            }
            
        } catch (Exception e) {
            LOG.error("保存安全设置失败", e);
            resp.setSuccess(false);
            resp.setMessage("保存安全设置失败: " + e.getMessage());
        }
        
        return resp;
    }
    
    /**
     * 测试密码验证
     */
    @PostMapping("/validate-password")
    public CommonResp<String> validatePassword(@RequestBody String password) {
        CommonResp<String> resp = new CommonResp<>();
        
        try {
            String validationResult = securitySettingsService.validatePassword(password);
            
            if (validationResult == null) {
                resp.setSuccess(true);
                resp.setMessage("密码验证通过");
            } else {
                resp.setSuccess(false);
                resp.setMessage(validationResult);
            }
            
        } catch (Exception e) {
            LOG.error("密码验证失败", e);
            resp.setSuccess(false);
            resp.setMessage("密码验证失败: " + e.getMessage());
        }
        
        return resp;
    }
}
