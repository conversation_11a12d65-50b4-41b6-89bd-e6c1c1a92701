package com.gec.wiki.controller;

import com.gec.wiki.pojo.req.UserLoginReq;
import com.gec.wiki.pojo.req.UserRegisterReq;
import com.gec.wiki.pojo.resp.CommonResp;
import com.gec.wiki.pojo.User;
import com.gec.wiki.service.UserService;
import com.gec.wiki.service.EmailService;
import com.gec.wiki.utils.TokenManager;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户认证控制器
 */
@RestController
@RequestMapping("/auth")
public class AuthController {

    private static final Logger LOG = LoggerFactory.getLogger(AuthController.class);

    @Resource
    private UserService userService;
    
    @Resource
    private EmailService emailService;
    
    @Resource
    private TokenManager tokenManager;

    /**
     * 用户登录
     */
    @PostMapping("/login")
    public CommonResp<Object> login(@RequestBody UserLoginReq req) {
        return userService.login(req);
    }

    /**
     * 用户注册
     */
    @PostMapping("/register")
    public CommonResp<Object> register(@RequestBody UserRegisterReq req) {
        return userService.register(req);
    }

    /**
     * 用户登出
     */
    @PostMapping("/logout")
    public CommonResp<Object> logout() {
        CommonResp<Object> resp = new CommonResp<>();
        try {
            // 获取token并从TokenManager中移除
            HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
            String token = request.getHeader("Authorization");
            if (token != null && !token.trim().isEmpty()) {
                tokenManager.removeToken(token);
                LOG.info("用户登出成功，token已清理");
            }
            
            resp.setSuccess(true);
            resp.setMessage("登出成功");
        } catch (Exception e) {
            LOG.error("登出处理异常", e);
            resp.setSuccess(true);
            resp.setMessage("登出成功");
        }
        return resp;
    }

    /**
     * 获取当前用户信息
     */
    @GetMapping("/userinfo")
    public CommonResp<Object> getUserInfo() {
        CommonResp<Object> resp = new CommonResp<>();
        // TODO: 实现获取用户信息逻辑（需要从token解析用户信息）
        resp.setSuccess(true);
        return resp;
    }

    /**
     * 验证用户身份信息（忘记密码第一步）
     */
    @PostMapping("/verify-user-info")
    public CommonResp<Object> verifyUserInfo(@RequestBody VerifyUserInfoReq req) {
        CommonResp<Object> resp = new CommonResp<>();
        try {
            // 查找匹配的用户
            User user = userService.findByRealNameAndPhoneAndEmail(
                req.getRealName(), req.getPhone(), req.getEmail());
            
            if (user != null) {
                resp.setSuccess(true);
                resp.setMessage("身份验证成功");
            } else {
                resp.setSuccess(false);
                resp.setMessage("未找到匹配的用户信息");
            }
        } catch (Exception e) {
            resp.setSuccess(false);
            resp.setMessage("验证失败，系统异常");
        }
        return resp;
    }

    /**
     * 发送验证码到邮箱（忘记密码第二步）
     */
    @PostMapping("/send-verification-code")
    public CommonResp<Object> sendVerificationCode(@RequestBody SendCodeReq req) {
        CommonResp<Object> resp = new CommonResp<>();
        try {
            LOG.info("开始发送验证码到邮箱：{}", req.getEmail());
            
            // 1. 生成6位随机验证码
            String code = emailService.generateVerificationCode();
            
            // 2. 存储到Redis，设置5分钟过期
            emailService.storeVerificationCode(req.getEmail(), code);
            
            // 3. 发送邮件
            boolean emailSent = emailService.sendVerificationCode(req.getEmail(), code);
            
            if (emailSent) {
                resp.setSuccess(true);
                resp.setMessage("验证码已发送至您的邮箱，请注意查收");
                LOG.info("验证码发送成功，邮箱：{}", req.getEmail());
            } else {
                resp.setSuccess(false);
                resp.setMessage("验证码发送失败，请稍后重试");
                LOG.error("验证码发送失败，邮箱：{}", req.getEmail());
            }
            
        } catch (Exception e) {
            LOG.error("发送验证码异常，邮箱：{}，错误：", req.getEmail(), e);
            resp.setSuccess(false);
            resp.setMessage("发送验证码失败，系统异常");
        }
        return resp;
    }

    /**
     * 使用用户授权码发送验证码到邮箱（忘记密码增强版）
     */
    @PostMapping("/send-verification-code-with-auth")
    public CommonResp<Object> sendVerificationCodeWithAuth(@RequestBody SendCodeWithAuthReq req) {
        CommonResp<Object> resp = new CommonResp<>();
        try {
            LOG.info("开始使用用户授权码发送验证码到邮箱：{}", req.getEmail());
            
            // 1. 生成6位随机验证码
            String code = emailService.generateVerificationCode();
            
            // 2. 存储到Redis，设置5分钟过期
            emailService.storeVerificationCode(req.getEmail(), code);
            
            // 3. 使用用户提供的授权码发送邮件
            boolean emailSent = emailService.sendVerificationCodeWithAuthCode(
                req.getEmail(), code, req.getUserEmail(), req.getAuthCode());
            
            if (emailSent) {
                resp.setSuccess(true);
                resp.setMessage("验证码已发送至您的邮箱，请注意查收");
                LOG.info("使用用户授权码发送验证码成功，邮箱：{}", req.getEmail());
            } else {
                resp.setSuccess(false);
                resp.setMessage("验证码发送失败，请检查授权码是否正确");
                LOG.error("使用用户授权码发送验证码失败，邮箱：{}", req.getEmail());
            }
            
        } catch (Exception e) {
            LOG.error("使用用户授权码发送验证码异常，邮箱：{}，错误：", req.getEmail(), e);
            resp.setSuccess(false);
            resp.setMessage("发送验证码失败，请检查授权码是否正确");
        }
        return resp;
    }

    /**
     * 验证验证码并找回密码（忘记密码第三步）
     */
    @PostMapping("/verify-code-and-recover")
    public CommonResp<Object> verifyCodeAndRecover(@RequestBody RecoverPasswordReq req) {
        CommonResp<Object> resp = new CommonResp<>();
        try {
            LOG.info("开始验证验证码并找回密码，邮箱：{}", req.getEmail());
            
            // 1. 验证验证码是否正确且未过期
            boolean codeValid = emailService.verifyCode(req.getEmail(), req.getVerificationCode());
            if (!codeValid) {
                resp.setSuccess(false);
                resp.setMessage("验证码错误或已过期，请重新获取");
                return resp;
            }
            
            // 2. 再次验证用户身份信息
            User user = userService.findByRealNameAndPhoneAndEmail(
                req.getRealName(), req.getPhone(), req.getEmail());
            
            if (user != null) {
                // 3. 验证成功，清除验证码
                emailService.removeVerificationCode(req.getEmail());
                
                // 返回用户信息，准备进行密码重置
                Map<String, Object> result = new HashMap<>();
                result.put("username", user.getUsername());
                result.put("verified", true);
                
                resp.setSuccess(true);
                resp.setMessage("验证成功，请设置新密码");
                resp.setContent(result);
                
                LOG.info("验证码验证成功，用户：{}", user.getUsername());
            } else {
                resp.setSuccess(false);
                resp.setMessage("用户信息不匹配");
            }
        } catch (Exception e) {
            LOG.error("验证验证码并找回密码失败，邮箱：{}，错误：", req.getEmail(), e);
            resp.setSuccess(false);
            resp.setMessage("验证失败，系统异常");
        }
        return resp;
    }

    /**
     * 重置密码（忘记密码最后一步）
     */
    @PostMapping("/reset-password")
    public CommonResp<Object> resetPassword(@RequestBody ResetPasswordReq req) {
        CommonResp<Object> resp = new CommonResp<>();
        try {
            LOG.info("开始重置密码，邮箱：{}", req.getEmail());
            
            // 1. 再次验证用户身份信息
            User user = userService.findByRealNameAndPhoneAndEmail(
                req.getRealName(), req.getPhone(), req.getEmail());
            
            if (user != null) {
                // 2. 重置用户密码
                boolean resetSuccess = userService.resetPassword(user.getId(), req.getNewPassword());
                
                if (resetSuccess) {
                    resp.setSuccess(true);
                    resp.setMessage("密码重置成功");
                    LOG.info("密码重置成功，用户：{}", user.getUsername());
                } else {
                    resp.setSuccess(false);
                    resp.setMessage("密码重置失败，请稍后重试");
                }
            } else {
                resp.setSuccess(false);
                resp.setMessage("用户信息验证失败");
            }
        } catch (Exception e) {
            LOG.error("重置密码异常，邮箱：{}，错误：", req.getEmail(), e);
            resp.setSuccess(false);
            resp.setMessage("密码重置失败，系统异常");
        }
        return resp;
    }

    // 内部类：请求对象
    public static class VerifyUserInfoReq {
        private String realName;
        private String phone;
        private String email;

        public String getRealName() { return realName; }
        public void setRealName(String realName) { this.realName = realName; }
        public String getPhone() { return phone; }
        public void setPhone(String phone) { this.phone = phone; }
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
    }

    public static class SendCodeReq {
        private String email;
        private String type;

        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
    }

    public static class SendCodeWithAuthReq {
        private String email;
        private String userEmail;
        private String authCode;
        private String type;

        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        public String getUserEmail() { return userEmail; }
        public void setUserEmail(String userEmail) { this.userEmail = userEmail; }
        public String getAuthCode() { return authCode; }
        public void setAuthCode(String authCode) { this.authCode = authCode; }
        public String getType() { return type; }
        public void setType(String type) { this.type = type; }
    }

    public static class RecoverPasswordReq {
        private String realName;
        private String phone;
        private String email;
        private String verificationCode;

        public String getRealName() { return realName; }
        public void setRealName(String realName) { this.realName = realName; }
        public String getPhone() { return phone; }
        public void setPhone(String phone) { this.phone = phone; }
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        public String getVerificationCode() { return verificationCode; }
        public void setVerificationCode(String verificationCode) { this.verificationCode = verificationCode; }
    }

    public static class ResetPasswordReq {
        private String realName;
        private String phone;
        private String email;
        private String newPassword;

        public String getRealName() { return realName; }
        public void setRealName(String realName) { this.realName = realName; }
        public String getPhone() { return phone; }
        public void setPhone(String phone) { this.phone = phone; }
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        public String getNewPassword() { return newPassword; }
        public void setNewPassword(String newPassword) { this.newPassword = newPassword; }
    }
}
