/**
 * 权限验证工具
 */

export interface UserInfo {
  id: number;
  username: string;
  realName: string;
  userType: number;
  [key: string]: any;
}

// 用户类型常量
export const USER_TYPES = {
  OWNER: 1,    // 车主
  SHOP: 2,     // 维修店
  ADMIN: 3     // 管理员
} as const;

// 用户类型名称映射
export const USER_TYPE_NAMES = {
  [USER_TYPES.OWNER]: '车主',
  [USER_TYPES.SHOP]: '维修店',
  [USER_TYPES.ADMIN]: '管理员'
} as const;

/**
 * 获取当前用户信息
 */
export function getCurrentUser(): UserInfo | null {
  try {
    const userInfoStr = localStorage.getItem('userInfo');
    if (!userInfoStr) return null;
    
    const userInfo = JSON.parse(userInfoStr);
    
    // 验证用户信息完整性
    if (!userInfo.id || !userInfo.userType || ![1, 2, 3].includes(userInfo.userType)) {
      console.error('Invalid user info:', userInfo);
      clearUserSession();
      return null;
    }
    
    return userInfo;
  } catch (error) {
    console.error('Failed to parse user info:', error);
    clearUserSession();
    return null;
  }
}

/**
 * 获取当前用户token
 */
export function getCurrentToken(): string | null {
  return localStorage.getItem('token');
}

/**
 * 检查用户是否已登录
 */
export function isAuthenticated(): boolean {
  const token = getCurrentToken();
  const userInfo = getCurrentUser();
  return !!(token && userInfo);
}

/**
 * 检查用户是否有指定角色权限
 */
export function hasRole(requiredRole: number): boolean {
  const userInfo = getCurrentUser();
  if (!userInfo) return false;
  
  return userInfo.userType === requiredRole;
}

/**
 * 检查用户是否有访问指定路径的权限
 */
export function canAccessPath(path: string): boolean {
  const userInfo = getCurrentUser();
  if (!userInfo) return false;
  
  const userType = userInfo.userType;
  
  // 路径权限映射
  const pathPermissions: Record<string, number[]> = {
    // 管理员路径
    '/admin': [USER_TYPES.ADMIN],
    '/admin/dashboard': [USER_TYPES.ADMIN],
    '/admin/users': [USER_TYPES.ADMIN],
    '/admin/orders': [USER_TYPES.ADMIN],
    '/admin/services': [USER_TYPES.ADMIN],
    '/admin/reports': [USER_TYPES.ADMIN],
    '/admin/settings': [USER_TYPES.ADMIN],
    '/admin/logs': [USER_TYPES.ADMIN],
    
    // 维修店路径
    '/shop': [USER_TYPES.SHOP],
    '/shop/dashboard': [USER_TYPES.SHOP],
    '/shop/orders': [USER_TYPES.SHOP],
    '/shop/services': [USER_TYPES.SHOP],
    '/shop/technicians': [USER_TYPES.SHOP],
    '/shop/reports': [USER_TYPES.SHOP],
    
    // 车主路径
    '/owner': [USER_TYPES.OWNER],
    '/owner/dashboard': [USER_TYPES.OWNER],
    '/owner/profile': [USER_TYPES.OWNER],
    '/owner/vehicles': [USER_TYPES.OWNER],
    '/owner/orders': [USER_TYPES.OWNER],
    
    // 公共路径 - 所有用户都可以访问
    '/': [USER_TYPES.OWNER, USER_TYPES.SHOP, USER_TYPES.ADMIN],
    '/booking': [USER_TYPES.OWNER, USER_TYPES.SHOP, USER_TYPES.ADMIN],
    '/about': [USER_TYPES.OWNER, USER_TYPES.SHOP, USER_TYPES.ADMIN]
  };
  
  // 检查精确匹配
  if (pathPermissions[path]) {
    return pathPermissions[path].includes(userType);
  }
  
  // 检查路径前缀匹配
  for (const [pathPattern, allowedRoles] of Object.entries(pathPermissions)) {
    if (path.startsWith(pathPattern + '/')) {
      return allowedRoles.includes(userType);
    }
  }
  
  // 默认拒绝访问
  return false;
}

/**
 * 获取用户对应的默认Dashboard路径
 */
export function getDefaultDashboard(userType: number): string {
  switch (userType) {
    case USER_TYPES.ADMIN:
      return '/admin/dashboard';
    case USER_TYPES.SHOP:
      return '/shop/dashboard';
    case USER_TYPES.OWNER:
      return '/owner/dashboard';
    default:
      return '/login';
  }
}

/**
 * 清除用户会话
 */
export function clearUserSession(): void {
  localStorage.removeItem('token');
  localStorage.removeItem('userInfo');
  
  // 触发退出登录事件
  import('./eventBus').then(({ eventBus, AUTH_EVENTS }) => {
    eventBus.emit(AUTH_EVENTS.AUTH_STATE_CHANGED, null);
  });
}

/**
 * 设置用户会话
 */
export function setUserSession(token: string, userInfo: UserInfo): void {
  localStorage.setItem('token', token);
  localStorage.setItem('userInfo', JSON.stringify(userInfo));
  
  // 触发登录成功事件
  import('./eventBus').then(({ eventBus, AUTH_EVENTS }) => {
    eventBus.emit(AUTH_EVENTS.AUTH_STATE_CHANGED, userInfo);
  });
}

/**
 * 安全导航 - 检查权限后跳转
 */
export function safeNavigate(router: any, path: string): boolean {
  if (canAccessPath(path)) {
    router.push(path);
    return true;
  } else {
    const userInfo = getCurrentUser();
    if (userInfo) {
      // 重定向到用户的默认dashboard
      router.push(getDefaultDashboard(userInfo.userType));
      console.warn(`Access denied to ${path}, redirected to dashboard`);
    } else {
      // 未登录，跳转到登录页
      router.push('/login');
    }
    return false;
  }
}

/**
 * 格式化用户类型显示
 */
export function formatUserType(userType: number): string {
  return USER_TYPE_NAMES[userType as keyof typeof USER_TYPE_NAMES] || '未知';
}
