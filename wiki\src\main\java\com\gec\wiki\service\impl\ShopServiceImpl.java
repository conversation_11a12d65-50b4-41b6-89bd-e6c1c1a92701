package com.gec.wiki.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.gec.wiki.mapper.UserMapper;
import com.gec.wiki.pojo.User;
import com.gec.wiki.pojo.Shop;
import com.gec.wiki.service.ShopService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.ArrayList;

/**
 * 维修店服务实现
 */
@Service
public class ShopServiceImpl implements ShopService {

    private static final Logger LOG = LoggerFactory.getLogger(ShopServiceImpl.class);

    @Resource
    private UserMapper userMapper;

    @Override
    public List<Shop> getAllActiveShops() {
        try {
            QueryWrapper<User> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_type", 2); // 获取维修店用户
            queryWrapper.eq("status", 1); // 只获取状态正常的用户
            queryWrapper.orderByDesc("id"); // 按ID降序排列
            List<User> users = userMapper.selectList(queryWrapper);

            // 将User转换为Shop对象
            List<Shop> shops = new ArrayList<>();
            for (User user : users) {
                Shop shop = new Shop();
                shop.setId(user.getId());
                shop.setName(user.getRealName() != null ? user.getRealName() : user.getUsername());
                shop.setAddress(user.getAddress());
                shop.setPhone(user.getPhone());
                shop.setBusinessHours("09:00-18:00"); // 默认营业时间
                shop.setRating(4.5); // 默认评分
                shop.setStatus(user.getStatus());
                shop.setDescription("专业的汽车维修服务");
                shops.add(shop);
            }

            LOG.info("获取到 {} 家营业中的维修店", shops.size());
            return shops;
        } catch (Exception e) {
            LOG.error("获取维修店列表失败", e);
            throw new RuntimeException("获取维修店列表失败", e);
        }
    }

    @Override
    public List<Shop> searchShopsByName(String name) {
        try {
            if (!StringUtils.hasText(name)) {
                return getAllActiveShops();
            }

            QueryWrapper<User> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_type", 2); // 获取维修店用户
            queryWrapper.eq("status", 1); // 只搜索状态正常的用户
            queryWrapper.and(wrapper -> wrapper
                .like("real_name", name.trim())
                .or()
                .like("username", name.trim())
                .or()
                .like("address", name.trim())
            );
            queryWrapper.orderByDesc("id");

            List<User> users = userMapper.selectList(queryWrapper);

            // 将User转换为Shop对象
            List<Shop> shops = new ArrayList<>();
            for (User user : users) {
                Shop shop = new Shop();
                shop.setId(user.getId());
                shop.setName(user.getRealName() != null ? user.getRealName() : user.getUsername());
                shop.setAddress(user.getAddress());
                shop.setPhone(user.getPhone());
                shop.setBusinessHours("09:00-18:00"); // 默认营业时间
                shop.setRating(4.5); // 默认评分
                shop.setStatus(user.getStatus());
                shop.setDescription("专业的汽车维修服务");
                shops.add(shop);
            }

            LOG.info("搜索关键词 '{}' 找到 {} 家维修店", name, shops.size());
            return shops;
        } catch (Exception e) {
            LOG.error("搜索维修店失败，关键词：{}", name, e);
            throw new RuntimeException("搜索维修店失败", e);
        }
    }

    @Override
    public Shop getShopById(Long id) {
        try {
            if (id == null) {
                return null;
            }

            User user = userMapper.selectById(id);
            if (user == null || user.getUserType() != 2) {
                LOG.info("获取维修店详情，ID：{}，结果：未找到", id);
                return null;
            }

            // 将User转换为Shop对象
            Shop shop = new Shop();
            shop.setId(user.getId());
            shop.setName(user.getRealName() != null ? user.getRealName() : user.getUsername());
            shop.setAddress(user.getAddress());
            shop.setPhone(user.getPhone());
            shop.setBusinessHours("09:00-18:00"); // 默认营业时间
            shop.setRating(4.5); // 默认评分
            shop.setStatus(user.getStatus());
            shop.setDescription("专业的汽车维修服务");

            LOG.info("获取维修店详情，ID：{}，结果：找到", id);
            return shop;
        } catch (Exception e) {
            LOG.error("获取维修店详情失败，ID：{}", id, e);
            throw new RuntimeException("获取维修店详情失败", e);
        }
    }

    @Override
    public boolean saveShop(Shop shop) {
        try {
            if (shop == null) {
                return false;
            }
            // 注意：这个方法现在不应该被使用，因为维修店信息存储在users表中
            // 如果需要创建维修店，应该创建user_type=2的用户
            LOG.warn("saveShop方法被调用，但维修店信息现在存储在users表中");
            return false;
        } catch (Exception e) {
            LOG.error("保存维修店失败", e);
            throw new RuntimeException("保存维修店失败", e);
        }
    }

    @Override
    public boolean updateShop(Shop shop) {
        try {
            if (shop == null || shop.getId() == null) {
                return false;
            }
            // 注意：这个方法现在不应该被使用，因为维修店信息存储在users表中
            // 如果需要更新维修店，应该更新对应的用户信息
            LOG.warn("updateShop方法被调用，但维修店信息现在存储在users表中");
            return false;
        } catch (Exception e) {
            LOG.error("更新维修店失败，ID：{}", shop.getId(), e);
            throw new RuntimeException("更新维修店失败", e);
        }
    }

    @Override
    public boolean deleteShop(Long id) {
        try {
            if (id == null) {
                return false;
            }
            // 注意：这个方法现在不应该被使用，因为维修店信息存储在users表中
            // 如果需要删除维修店，应该删除或禁用对应的用户
            LOG.warn("deleteShop方法被调用，但维修店信息现在存储在users表中");
            return false;
        } catch (Exception e) {
            LOG.error("删除维修店失败，ID：{}", id, e);
            throw new RuntimeException("删除维修店失败", e);
        }
    }
}
