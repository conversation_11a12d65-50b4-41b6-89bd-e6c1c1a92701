/**
 * 全局事件总线
 * 用于处理组件间的状态同步
 */

type EventCallback = (...args: any[]) => void;

interface IEventBus {
  on(event: string, callback: EventCallback): void;
  off(event: string, callback: EventCallback): void;
  emit(event: string, ...args: any[]): void;
  clear(): void;
}

// 使用工厂函数创建事件总线，避免类属性语法问题
function createEventBus(): IEventBus {
  const events = new Map<string, EventCallback[]>();

  return {
    // 监听事件
    on(event: string, callback: EventCallback) {
      if (!events.has(event)) {
        events.set(event, []);
      }
      events.get(event)!.push(callback);
    },

    // 移除事件监听
    off(event: string, callback: EventCallback) {
      const callbacks = events.get(event);
      if (callbacks) {
        const index = callbacks.indexOf(callback);
        if (index > -1) {
          callbacks.splice(index, 1);
        }
      }
    },

    // 触发事件
    emit(event: string, ...args: any[]) {
      const callbacks = events.get(event);
      if (callbacks) {
        callbacks.forEach(callback => callback(...args));
      }
    },

    // 清除所有事件监听
    clear() {
      events.clear();
    }
  };
}

// 创建全局事件总线实例
export const eventBus = createEventBus();

// 定义事件常量
export const AUTH_EVENTS = {
  LOGIN_SUCCESS: 'login_success',
  LOGOUT_SUCCESS: 'logout_success',
  AUTH_STATE_CHANGED: 'auth_state_changed'
} as const;
