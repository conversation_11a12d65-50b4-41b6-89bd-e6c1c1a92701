<template>
  <div class="service-management">
    <div class="page-header">
      <div class="header-content">
        <h1>服务管理</h1>
        <p>管理您的汽车维修服务项目，设置价格和状态</p>
      </div>
      <div class="header-actions">
        <a-button type="primary" @click="showAddServiceModal">
          <PlusOutlined />
          添加服务
        </a-button>
        <a-button @click="refreshServiceList">
          <ReloadOutlined />
          刷新
        </a-button>
      </div>
    </div>

    <div class="page-content">
      <a-card>
        <!-- 搜索和筛选区域 -->
        <div class="search-section">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-input-search
                v-model:value="searchForm.name"
                placeholder="搜索服务名称"
                @search="onSearch"
                @change="onSearchChange"
              />
            </a-col>
            <a-col :span="5">
              <a-select
                v-model:value="searchForm.category1Id"
                placeholder="一级分类"
                allow-clear
                @change="onCategory1Change"
              >
                <a-select-option
                  v-for="category in firstLevelCategories"
                  :key="category.id"
                  :value="category.id"
                >
                  {{ category.name }}
                </a-select-option>
              </a-select>
            </a-col>
            <a-col :span="5">
              <a-select
                v-model:value="searchForm.category2Id"
                placeholder="二级分类"
                allow-clear
                :disabled="!searchForm.category1Id"
                @change="onSearch"
              >
                <a-select-option
                  v-for="category in secondLevelCategories"
                  :key="category.id"
                  :value="category.id"
                >
                  {{ category.name }}
                </a-select-option>
              </a-select>
            </a-col>
            <a-col :span="4">
              <a-select
                v-model:value="searchForm.status"
                placeholder="状态"
                allow-clear
                @change="onSearch"
              >
                <a-select-option :value="1">上架</a-select-option>
                <a-select-option :value="0">下架</a-select-option>
              </a-select>
            </a-col>
            <a-col :span="4">
              <a-space>
                <a-button type="primary" @click="onSearch">搜索</a-button>
                <a-button @click="resetSearch">重置</a-button>
              </a-space>
            </a-col>
          </a-row>
        </div>

        <!-- 服务统计卡片 -->
        <div class="stats-section">
          <a-row :gutter="16">
            <a-col :span="6">
              <a-card class="stat-card">
                <a-statistic title="总服务数" :value="stats.total" :prefix="h(ToolOutlined)" />
              </a-card>
            </a-col>
            <a-col :span="6">
              <a-card class="stat-card">
                <a-statistic title="上架服务" :value="stats.online" :prefix="h(CheckCircleOutlined)" />
              </a-card>
            </a-col>
            <a-col :span="6">
              <a-card class="stat-card">
                <a-statistic title="下架服务" :value="stats.offline" :prefix="h(MinusCircleOutlined)" />
              </a-card>
            </a-col>
            <a-col :span="6">
              <a-card class="stat-card">
                <a-statistic title="总预约数" :value="stats.totalBookings" :prefix="h(CalendarOutlined)" />
              </a-card>
            </a-col>
          </a-row>
        </div>

        <!-- 服务列表 -->
        <div class="table-section">
          <a-table
            :columns="columns"
            :data-source="serviceList"
            :pagination="pagination"
            :loading="loading"
            row-key="id"
            @change="handleTableChange"
          >
            <template #bodyCell="{ column, record, index }">
              <template v-if="column.key === 'index'">
                {{ (pagination.current - 1) * pagination.pageSize + index + 1 }}
              </template>
              
              <template v-else-if="column.key === 'cover'">
                <div class="service-cover">
                  <a-image
                    v-if="record.cover"
                    :src="getImageUrl(record.cover)"
                    :width="60"
                    :height="45"
                    :style="{ borderRadius: '4px', objectFit: 'cover' }"
                    :preview="{ src: getImageUrl(record.cover) }"
                  />
                  <div v-else class="no-image">
                    <PictureOutlined />
                    <span>暂无图片</span>
                  </div>
                </div>
              </template>
              
              <template v-else-if="column.key === 'name'">
                <div class="service-info">
                  <div class="service-name">{{ record.name }}</div>
                  <div class="service-desc">{{ record.description || '暂无描述' }}</div>
                </div>
              </template>
              
              <template v-else-if="column.key === 'category'">
                <a-tag color="blue">{{ record.categoryName || '未分类' }}</a-tag>
              </template>
              
              <template v-else-if="column.key === 'price'">
                <div class="price-info">
                  <div class="current-price">¥{{ record.price || 0 }}</div>
                  <div v-if="record.originalPrice && record.originalPrice > record.price" class="original-price">
                    原价: ¥{{ record.originalPrice }}
                  </div>
                </div>
              </template>
              
              <template v-else-if="column.key === 'duration'">
                <a-tag color="green">{{ record.duration || 0 }}分钟</a-tag>
              </template>
              
              <template v-else-if="column.key === 'stats'">
                <div class="service-stats">
                  <div class="stat-item">
                    <span class="stat-label">预约:</span>
                    <span class="stat-value">{{ record.bookingCount || 0 }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">完成:</span>
                    <span class="stat-value">{{ record.completeCount || 0 }}</span>
                  </div>
                  <div class="stat-item">
                    <span class="stat-label">评分:</span>
                    <a-rate :value="(record.ratingScore || 5) / 1" disabled allow-half :style="{ fontSize: '12px' }" />
                  </div>
                </div>
              </template>
              
              <template v-else-if="column.key === 'status'">
                <a-switch
                  :checked="record.status === 1"
                  :loading="record.statusLoading"
                  @change="(checked) => handleStatusChange(record, checked)"
                >
                  <template #checkedChildren>上架</template>
                  <template #unCheckedChildren>下架</template>
                </a-switch>
              </template>
              
              <template v-else-if="column.key === 'actions'">
                <a-space>
                  <a-button type="text" size="small" @click="showEditServiceModal(record)">
                    <EditOutlined />
                    编辑
                  </a-button>
                  <a-button type="text" size="small" @click="viewServiceDetail(record)">
                    <EyeOutlined />
                    查看
                  </a-button>
                  <a-popconfirm
                    title="确定要删除这个服务吗？"
                    @confirm="handleDeleteService(record.id)"
                  >
                    <a-button type="text" size="small" danger>
                      <DeleteOutlined />
                      删除
                    </a-button>
                  </a-popconfirm>
                </a-space>
              </template>
            </template>
          </a-table>
        </div>
      </a-card>
    </div>

    <!-- 添加/编辑服务对话框 -->
    <a-modal
      :visible="serviceModalVisible"
      :title="serviceModalTitle"
      :confirm-loading="serviceModalLoading"
      @ok="handleSaveService"
      @cancel="handleCancelServiceModal"
      width="800px"
      :maskClosable="false"
    >
      <a-form
        ref="serviceFormRef"
        :model="serviceForm"
        :rules="serviceFormRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="服务名称" name="name" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
              <a-input v-model:value="serviceForm.name" placeholder="请输入服务名称" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="服务时长" name="duration" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
              <a-input-number
                v-model:value="serviceForm.duration"
                :min="1"
                :max="999"
                placeholder="分钟"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="一级分类" name="category1Id">
          <a-select
            v-model:value="serviceForm.category1Id"
            placeholder="选择一级分类"
            @change="onServiceCategory1Change"
          >
            <a-select-option
              v-for="category in firstLevelCategories"
              :key="category.id"
              :value="category.id"
            >
              {{ category.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        
        <a-form-item label="二级分类" name="category2Id">
          <a-select
            v-model:value="serviceForm.category2Id"
            placeholder="选择二级分类"
            :disabled="!serviceForm.category1Id"
          >
            <a-select-option
              v-for="category in serviceSecondLevelCategories"
              :key="category.id"
              :value="category.id"
            >
              {{ category.name }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="服务价格" name="price" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
              <a-input-number
                v-model:value="serviceForm.price"
                :min="0"
                :precision="2"
                placeholder="元"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="原价" name="originalPrice" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
              <a-input-number
                v-model:value="serviceForm.originalPrice"
                :min="0"
                :precision="2"
                placeholder="元"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="服务描述" name="description">
          <a-textarea
            v-model:value="serviceForm.description"
            placeholder="请输入服务描述"
            :rows="3"
          />
        </a-form-item>

        <a-form-item label="详细内容" name="content">
          <a-textarea
            v-model:value="serviceForm.content"
            placeholder="请输入服务详细内容"
            :rows="4"
          />
        </a-form-item>

        <a-form-item label="服务图片" name="cover">
          <div class="upload-section">
            <a-upload
              :file-list="fileList"
              :before-upload="beforeUpload"
              @remove="handleRemoveImage"
              list-type="picture-card"
              :max-count="1"
            >
              <div v-if="fileList.length < 1">
                <plus-outlined />
                <div style="margin-top: 8px">上传图片</div>
              </div>
            </a-upload>
          </div>
        </a-form-item>

        <a-form-item label="状态" name="status">
          <a-radio-group v-model:value="serviceForm.status">
            <a-radio :value="1">上架</a-radio>
            <a-radio :value="0">下架</a-radio>
          </a-radio-group>
        </a-form-item>
        
        <a-form-item label="推荐" name="isRecommend">
          <a-radio-group v-model:value="serviceForm.isRecommend">
            <a-radio :value="1">是</a-radio>
            <a-radio :value="0">否</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 服务详情对话框 -->
    <a-modal
      :visible="detailModalVisible"
      title="服务详情"
      @cancel="detailModalVisible = false"
      :footer="null"
      width="700px"
    >
      <div v-if="selectedService" class="service-detail">
        <a-descriptions :column="2" bordered>
          <a-descriptions-item label="服务名称">{{ selectedService.name }}</a-descriptions-item>
          <a-descriptions-item label="分类">{{ selectedService.categoryName }}</a-descriptions-item>
          <a-descriptions-item label="价格">¥{{ selectedService.price }}</a-descriptions-item>
          <a-descriptions-item label="原价">¥{{ selectedService.originalPrice || '无' }}</a-descriptions-item>
          <a-descriptions-item label="时长">{{ selectedService.duration }}分钟</a-descriptions-item>
          <a-descriptions-item label="状态">
            <a-tag :color="selectedService.status === 1 ? 'green' : 'red'">
              {{ selectedService.status === 1 ? '上架' : '下架' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="预约次数">{{ selectedService.bookingCount || 0 }}</a-descriptions-item>
          <a-descriptions-item label="完成次数">{{ selectedService.completeCount || 0 }}</a-descriptions-item>
          <a-descriptions-item label="评分">
            <a-rate :value="(selectedService.ratingScore || 5) / 1" disabled allow-half />
            <span style="margin-left: 8px;">{{ selectedService.ratingScore || 5.0 }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="评价数量">{{ selectedService.ratingCount || 0 }}</a-descriptions-item>
          <a-descriptions-item label="创建时间" :span="2">{{ formatDateTime(selectedService.createTime) }}</a-descriptions-item>
          <a-descriptions-item label="更新时间" :span="2">{{ formatDateTime(selectedService.updateTime) }}</a-descriptions-item>
          <a-descriptions-item label="服务描述" :span="2">{{ selectedService.description || '暂无描述' }}</a-descriptions-item>
          <a-descriptions-item label="详细内容" :span="2">
            <div class="content-detail">{{ selectedService.content || '暂无详细内容' }}</div>
          </a-descriptions-item>
        </a-descriptions>
        
        <div v-if="selectedService.cover" class="service-image" style="margin-top: 16px;">
          <h4>服务图片</h4>
          <a-image :src="getImageUrl(selectedService.cover)" :width="200" />
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, computed, reactive, h } from 'vue';
import { message } from 'ant-design-vue';
import axios from 'axios';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  EyeOutlined,
  PictureOutlined,
  ToolOutlined,
  CheckCircleOutlined,
  MinusCircleOutlined,
  CalendarOutlined
} from '@ant-design/icons-vue';

interface ServiceData {
  id: number;
  name: string;
  category1Id: number;
  category2Id: number;
  categoryName?: string;
  description?: string;
  content?: string;
  cover?: string;
  price: number;
  originalPrice?: number;
  duration: number;
  bookingCount?: number;
  completeCount?: number;
  ratingCount?: number;
  ratingScore?: number;
  status: number;
  isRecommend: number;
  createTime?: string;
  updateTime?: string;
  statusLoading?: boolean;
}

interface CategoryData {
  id: number;
  name: string;
  parent: number;
  children?: CategoryData[];
}

export default defineComponent({
  name: 'ServiceManagement',
  components: {
    PlusOutlined,
    EditOutlined,
    DeleteOutlined,
    ReloadOutlined,
    EyeOutlined,
    PictureOutlined,
    ToolOutlined,
    CheckCircleOutlined,
    MinusCircleOutlined,
    CalendarOutlined
  },
  setup() {
    const loading = ref(false);
    const serviceList = ref<ServiceData[]>([]);
    const categories = ref<CategoryData[]>([]);
    const selectedService = ref<ServiceData | null>(null);

    // 搜索表单
    const searchForm = reactive({
      name: '',
      category1Id: undefined as number | undefined,
      category2Id: undefined as number | undefined,
      status: undefined as number | undefined
    });

    // 统计数据
    const stats = ref({
      total: 0,
      online: 0,
      offline: 0,
      totalBookings: 0
    });

    // 分页
    const pagination = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total: number) => `共 ${total} 条记录`
    });

    // 服务对话框相关
    const serviceModalVisible = ref(false);
    const serviceModalLoading = ref(false);
    const serviceModalTitle = ref('');
    const serviceFormRef = ref();
    const serviceForm = reactive({
      id: null as number | null,
      name: '',
      category1Id: undefined as number | undefined,
      category2Id: undefined as number | undefined,
      description: '',
      content: '',
      cover: '',
      price: undefined as number | undefined,
      originalPrice: undefined as number | undefined,
      duration: undefined as number | undefined,
      status: 1,
      isRecommend: 0
    });

    // 详情对话框
    const detailModalVisible = ref(false);

    // 文件上传
    const fileList = ref<any[]>([]);

    const serviceFormRules = {
      name: [
        { required: true, message: '请输入服务名称', trigger: 'blur' },
        { min: 1, max: 50, message: '服务名称长度在1-50个字符', trigger: 'blur' }
      ],
      category1Id: [
        { required: true, message: '请选择一级分类', trigger: 'change' }
      ],
      category2Id: [
        { required: true, message: '请选择二级分类', trigger: 'change' }
      ],
      price: [
        { required: true, message: '请输入服务价格', trigger: 'blur' }
      ],
      duration: [
        { required: true, message: '请输入服务时长', trigger: 'blur' }
      ]
    };

    // 表格列定义
    const columns = [
      {
        title: '序号',
        key: 'index',
        width: 80,
        align: 'center'
      },
      {
        title: '服务图片',
        key: 'cover',
        width: 100,
        align: 'center'
      },
      {
        title: '服务信息',
        key: 'name',
        width: 200
      },
      {
        title: '分类',
        key: 'category',
        width: 120,
        align: 'center'
      },
      {
        title: '价格',
        key: 'price',
        width: 120,
        align: 'center'
      },
      {
        title: '时长',
        key: 'duration',
        width: 80,
        align: 'center'
      },
      {
        title: '统计数据',
        key: 'stats',
        width: 160
      },
      {
        title: '状态',
        key: 'status',
        width: 100,
        align: 'center'
      },
      {
        title: '操作',
        key: 'actions',
        width: 180,
        align: 'center'
      }
    ];

    // 计算属性
    const firstLevelCategories = computed(() => {
      return categories.value.filter(cat => cat.parent === 0);
    });

    const secondLevelCategories = computed(() => {
      if (!searchForm.category1Id) return [];
      const parent = categories.value.find(cat => cat.id === searchForm.category1Id);
      return parent && parent.children ? parent.children : [];
    });

    const serviceSecondLevelCategories = computed(() => {
      if (!serviceForm.category1Id) return [];
      const parent = categories.value.find(cat => cat.id === serviceForm.category1Id);
      return parent && parent.children ? parent.children : [];
    });

    // 加载服务分类
    const loadCategories = async () => {
      try {
        const response = await axios.get('/shop/service/categories');
        if (response.data.success) {
          categories.value = response.data.content || [];
        }
      } catch (error) {
        console.error('Load categories error:', error);
      }
    };

    // 加载服务列表
    const loadServiceList = async () => {
      loading.value = true;
      try {
        const params = {
          page: pagination.current,
          size: pagination.pageSize,
          name: searchForm.name || undefined,
          category1Id: searchForm.category1Id || undefined,
          category2Id: searchForm.category2Id || undefined,
          status: searchForm.status !== undefined ? searchForm.status : undefined
        };

        const response = await axios.get('/shop/service/list', { params });
        if (response.data.success) {
          const pageData = response.data.content;
          serviceList.value = pageData.list || [];
          pagination.total = pageData.total || 0;
          
          // 计算统计数据
          updateStats();
        } else {
          message.error(response.data.message || '获取服务列表失败');
        }
      } catch (error) {
        console.error('Load service list error:', error);
        message.error('获取服务列表失败');
      } finally {
        loading.value = false;
      }
    };

    // 更新统计数据
    const updateStats = () => {
      stats.value.total = serviceList.value.length;
      stats.value.online = serviceList.value.filter(s => s.status === 1).length;
      stats.value.offline = serviceList.value.filter(s => s.status === 0).length;
      stats.value.totalBookings = serviceList.value.reduce((sum, s) => sum + (s.bookingCount || 0), 0);
    };

    // 刷新服务列表
    const refreshServiceList = () => {
      loadServiceList();
    };

    // 搜索
    const onSearch = () => {
      pagination.current = 1;
      loadServiceList();
    };

    // 搜索框内容变化
    const onSearchChange = () => {
      if (!searchForm.name.trim()) {
        onSearch();
      }
    };

    // 重置搜索
    const resetSearch = () => {
      searchForm.name = '';
      searchForm.category1Id = undefined;
      searchForm.category2Id = undefined;
      searchForm.status = undefined;
      pagination.current = 1;
      loadServiceList();
    };

    // 一级分类变化
    const onCategory1Change = (value: any) => {
      searchForm.category2Id = undefined;
      onSearch();
    };

    // 服务一级分类变化
    const onServiceCategory1Change = (value: any) => {
      serviceForm.category2Id = undefined;
    };

    // 表格分页变化
    const handleTableChange = (pag: any) => {
      pagination.current = pag.current;
      pagination.pageSize = pag.pageSize;
      loadServiceList();
    };

    // 显示添加服务对话框
    const showAddServiceModal = () => {
      serviceModalTitle.value = '添加服务';
      serviceForm.id = null;
      serviceForm.name = '';
      serviceForm.category1Id = undefined;
      serviceForm.category2Id = undefined;
      serviceForm.description = '';
      serviceForm.content = '';
      serviceForm.cover = '';
      serviceForm.price = undefined;
      serviceForm.originalPrice = undefined;
      serviceForm.duration = undefined;
      serviceForm.status = 1;
      serviceForm.isRecommend = 0;
      fileList.value = [];
      serviceModalVisible.value = true;
    };

    // 显示编辑服务对话框
    const showEditServiceModal = (service: ServiceData) => {
      serviceModalTitle.value = '编辑服务';
      serviceForm.id = service.id;
      serviceForm.name = service.name || '';
      serviceForm.category1Id = service.category1Id || undefined;
      serviceForm.category2Id = service.category2Id || undefined;
      serviceForm.description = service.description || '';
      serviceForm.content = service.content || '';
      serviceForm.cover = service.cover || '';
      serviceForm.price = service.price || undefined;
      serviceForm.originalPrice = service.originalPrice || undefined;
      serviceForm.duration = service.duration || undefined;
      serviceForm.status = service.status || 1;
      serviceForm.isRecommend = service.isRecommend || 0;
      
      // 设置文件列表
      if (service.cover) {
        // 构建完整的图片URL
        const imageUrl = service.cover.startsWith('http') ? service.cover : `http://localhost:8880${service.cover}`;
        fileList.value = [{
          uid: '-1',
          name: 'service-cover.jpg',
          status: 'done',
          url: imageUrl,
          thumbUrl: imageUrl
        }];
      } else {
        fileList.value = [];
      }
      
      serviceModalVisible.value = true;
    };

    // 取消服务对话框
    const handleCancelServiceModal = () => {
      serviceModalVisible.value = false;
      if (serviceFormRef.value) {
        serviceFormRef.value.resetFields();
      }
      fileList.value = [];
    };

    // 保存服务
    const handleSaveService = async () => {
      try {
        if (serviceFormRef.value) {
          await serviceFormRef.value.validate();
        }
        serviceModalLoading.value = true;

        const saveData = {
          id: serviceForm.id,
          name: serviceForm.name.trim(),
          category1Id: serviceForm.category1Id,
          category2Id: serviceForm.category2Id,
          description: serviceForm.description,
          content: serviceForm.content,
          cover: serviceForm.cover,
          price: serviceForm.price,
          originalPrice: serviceForm.originalPrice,
          duration: serviceForm.duration,
          status: serviceForm.status,
          isRecommend: serviceForm.isRecommend
        };

        const response = await axios.post('/shop/service/save', saveData);
        if (response.data.success) {
          message.success(response.data.message || '保存成功');
          serviceModalVisible.value = false;
          await loadServiceList();
        } else {
          message.error(response.data.message || '保存失败');
        }
      } catch (error) {
        console.error('Save service error:', error);
        message.error('保存失败');
      } finally {
        serviceModalLoading.value = false;
      }
    };

    // 删除服务
    const handleDeleteService = async (id: number) => {
      try {
        const response = await axios.delete(`/shop/service/${id}`);
        if (response.data.success) {
          message.success('删除成功');
          await loadServiceList();
        } else {
          message.error(response.data.message || '删除失败');
        }
      } catch (error) {
        console.error('Delete service error:', error);
        message.error('删除失败');
      }
    };

    // 状态变化
    const handleStatusChange = async (record: ServiceData, checked: boolean) => {
      record.statusLoading = true;
      try {
        const status = checked ? 1 : 0;
        const response = await axios.put(`/shop/service/${record.id}/status?status=${status}`);
        if (response.data.success) {
          record.status = status;
          message.success(`已${checked ? '上架' : '下架'}`);
          updateStats();
        } else {
          message.error(response.data.message || '状态更新失败');
        }
      } catch (error) {
        console.error('Update status error:', error);
        message.error('状态更新失败');
      } finally {
        record.statusLoading = false;
      }
    };

    // 查看服务详情
    const viewServiceDetail = (service: ServiceData) => {
      selectedService.value = service;
      detailModalVisible.value = true;
    };

    // 文件上传前处理
    const beforeUpload = async (file: any) => {
      console.log('🔍 开始上传文件:', file);
      
      // 验证文件类型
      const isImage = file.type.startsWith('image/');
      if (!isImage) {
        message.error('只能上传图片文件!');
        return false;
      }

      // 验证文件大小
      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isLt2M) {
        message.error('图片大小不能超过 2MB!');
        return false;
      }

      // 验证文件格式
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
      if (!allowedTypes.includes(file.type)) {
        message.error('只支持 JPEG、PNG、GIF 格式的图片!');
        return false;
      }

      try {
        message.loading('正在上传图片...', 0);
        
        const formData = new FormData();
        formData.append('file', file);
        
        console.log('📤 发送上传请求...');
        const response = await axios.post('/shop/service/upload', formData, {
          headers: { 
            'Content-Type': 'multipart/form-data'
          },
          timeout: 30000 // 30秒超时
        });
        
        message.destroy(); // 关闭loading消息
        
        console.log('📥 上传响应:', response.data);
        
        if (response.data.success) {
          serviceForm.cover = response.data.content;
          message.success('图片上传成功');
          
          // 更新文件列表
          const imageUrl = response.data.content.startsWith('http') ? response.data.content : `http://localhost:8880${response.data.content}`;
          fileList.value = [{
            uid: file.uid,
            name: file.name,
            status: 'done',
            url: imageUrl,
            thumbUrl: imageUrl
          }];
          
          console.log('✅ 图片上传成功，URL:', response.data.content);
        } else {
          console.error('❌ 上传失败:', response.data.message);
          message.error(response.data.message || '图片上传失败');
        }
      } catch (error: any) {
        message.destroy(); // 关闭loading消息
        console.error('💥 Upload error:', error);
        
        let errorMessage = '图片上传失败';
        if (error.response) {
          // 服务器返回错误
          errorMessage = (error.response.data && error.response.data.message) ? error.response.data.message : `服务器错误: ${error.response.status}`;
        } else if (error.request) {
          // 网络错误
          errorMessage = '网络连接失败，请检查网络连接';
        } else {
          // 其他错误
          errorMessage = error.message || '未知错误';
        }
        
        message.error(`上传失败：${errorMessage}`);
      }
      
      return false; // 阻止自动上传
    };

    // 移除图片
    const handleRemoveImage = () => {
      serviceForm.cover = '';
      fileList.value = [];
    };

    // 格式化时间
    const formatDateTime = (dateTime: string) => {
      if (!dateTime) return '—';
      return new Date(dateTime).toLocaleString('zh-CN');
    };

    // 获取完整的图片URL
    const getImageUrl = (imagePath: string) => {
      if (!imagePath) return '';
      if (imagePath.startsWith('http')) {
        return imagePath;
      }
      return `http://localhost:8880${imagePath}`;
    };

    onMounted(() => {
      loadCategories();
      loadServiceList();
    });

    return {
      h,
      loading,
      serviceList,
      categories,
      selectedService,
      searchForm,
      stats,
      pagination,
      serviceModalVisible,
      serviceModalLoading,
      serviceModalTitle,
      serviceFormRef,
      serviceForm,
      serviceFormRules,
      detailModalVisible,
      fileList,
      columns,
      firstLevelCategories,
      secondLevelCategories,
      serviceSecondLevelCategories,
      refreshServiceList,
      onSearch,
      onSearchChange,
      resetSearch,
      onCategory1Change,
      onServiceCategory1Change,
      handleTableChange,
      showAddServiceModal,
      showEditServiceModal,
      handleCancelServiceModal,
      handleSaveService,
      handleDeleteService,
      handleStatusChange,
      viewServiceDetail,
      beforeUpload,
      handleRemoveImage,
      formatDateTime,
      getImageUrl
    };
  }
});
</script>

<style scoped>
.service-management {
  background: #f0f2f5;
  padding: 24px;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content h1 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 28px;
}

.header-content p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.header-actions .ant-btn {
  margin-left: 12px;
}

.page-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-section {
  padding: 24px 24px 0 24px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 24px;
}

.stats-section {
  padding: 0 24px 24px 24px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 24px;
}

.stat-card {
  text-align: center;
  border-radius: 8px;
}

.table-section {
  padding: 0 24px 24px 24px;
}

.service-cover {
  display: flex;
  align-items: center;
  justify-content: center;
}

.no-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 45px;
  background: #f5f5f5;
  border-radius: 4px;
  color: #999;
  font-size: 12px;
}

.service-info {
  max-width: 180px;
}

.service-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.service-desc {
  font-size: 12px;
  color: #999;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.price-info {
  text-align: center;
}

.current-price {
  font-size: 16px;
  font-weight: bold;
  color: #f5222d;
}

.original-price {
  font-size: 12px;
  color: #999;
  text-decoration: line-through;
}

.service-stats {
  font-size: 12px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.stat-label {
  color: #666;
}

.stat-value {
  color: #333;
  font-weight: 500;
}

.upload-section {
  text-align: left;
}

.service-detail .content-detail {
  max-height: 200px;
  overflow-y: auto;
  white-space: pre-wrap;
  line-height: 1.6;
}

.service-image {
  text-align: center;
}

/* 响应式 */
@media (max-width: 768px) {
  .service-management {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .header-actions {
    align-self: stretch;
  }
  
  .header-actions .ant-btn {
    margin-left: 0;
    margin-right: 12px;
  }
  
  .search-section,
  .stats-section,
  .table-section {
    padding: 16px;
  }
}
</style>
