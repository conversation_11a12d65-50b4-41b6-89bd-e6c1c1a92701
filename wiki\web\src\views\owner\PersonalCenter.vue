<template>
  <div class="personal-center">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1>个人中心</h1>
          <p>管理您的个人信息和车辆资料</p>
        </div>
        <div class="header-actions">
          <a-button type="primary" size="large" @click="editProfile">
            <EditOutlined />
            编辑资料
          </a-button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <a-row :gutter="[24, 24]">
        <!-- 个人信息卡片 -->
        <a-col :xs="24" :lg="8">
          <a-card class="profile-card">
            <div class="profile-header">
              <div class="avatar-section">
                <a-avatar :size="100" :src="userInfo.avatar" class="user-avatar">
                  {{ userInfo.username && userInfo.username.charAt(0).toUpperCase() }}
                </a-avatar>
                <a-button size="small" type="link" class="change-avatar">
                  <CameraOutlined />
                  更换头像
                </a-button>
              </div>
              <div class="profile-info">
                <h2>{{ userInfo.username || '用户' }}</h2>
                <div class="user-badge">
                  <a-tag color="gold" class="vip-tag">
                    <CrownOutlined />
                    {{ userStats.memberLevel || 'VIP' }}会员
                  </a-tag>
                </div>
                <p class="user-motto">让每一次出行都更安心</p>
              </div>
            </div>
            
            <div class="stats-section">
              <div class="stats-grid">
                <div class="stat-card">
                  <div class="stat-icon spent">
                    <DollarOutlined />
                  </div>
                  <div class="stat-info">
                    <div class="stat-value">¥{{ userStats.totalSpent || 0 }}</div>
                    <div class="stat-label">累计消费</div>
                  </div>
                </div>
                <div class="stat-card">
                  <div class="stat-icon service">
                    <ToolOutlined />
                  </div>
                  <div class="stat-info">
                    <div class="stat-value">{{ userStats.serviceCount || 0 }}</div>
                    <div class="stat-label">服务次数</div>
                  </div>
                </div>
              </div>
            </div>
            
            <a-divider />
            
            <div class="contact-section">
              <h3>联系信息</h3>
              <div class="contact-list">
                <div class="contact-item">
                  <div class="contact-icon">
                    <PhoneOutlined />
                  </div>
                  <div class="contact-info">
                    <span class="contact-label">手机号码</span>
                    <span class="contact-value">{{ userInfo.phone || '未设置' }}</span>
                  </div>
                </div>
                <div class="contact-item">
                  <div class="contact-icon">
                    <MailOutlined />
                  </div>
                  <div class="contact-info">
                    <span class="contact-label">邮箱地址</span>
                    <span class="contact-value">{{ userInfo.email || '未设置' }}</span>
                  </div>
                </div>
                <div class="contact-item">
                  <div class="contact-icon">
                    <CalendarOutlined />
                  </div>
                  <div class="contact-info">
                    <span class="contact-label">注册时间</span>
                    <span class="contact-value">{{ formatDate(userInfo.createTime) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>

        <!-- 车辆信息卡片 -->
        <a-col :xs="24" :lg="16">
          <a-card class="vehicle-card">
            <template #title>
              <div class="card-title">
                <CarOutlined />
                <span>我的车辆</span>
                <a-badge :count="vehicles.length" :number-style="{ backgroundColor: '#52c41a' }" />
              </div>
            </template>
            <template #extra>
              <a-button type="primary" @click="addVehicle">
                <PlusOutlined />
                添加车辆
              </a-button>
            </template>

            <div v-if="vehicles.length === 0" class="empty-vehicles">
              <div class="empty-illustration">
                <CarOutlined />
              </div>
              <h3>暂无车辆信息</h3>
              <p>添加您的爱车，享受更好的维修服务</p>
              <a-button type="primary" size="large" @click="addVehicle">
                <PlusOutlined />
                立即添加车辆
              </a-button>
            </div>

            <div v-else class="vehicles-grid">
              <div 
                v-for="vehicle in vehicles" 
                :key="vehicle.id"
                class="vehicle-card-item"
              >
                <div class="vehicle-header">
                  <div class="vehicle-image">
                    <img :src="vehicle.image || '/image/default-service.png'" :alt="vehicle.brand" />
                  </div>
                  <div class="vehicle-basic-info">
                    <h4>{{ vehicle.brand }} {{ vehicle.model }}</h4>
                    <div class="license-plate">
                      <span class="plate-number">{{ vehicle.licensePlate }}</span>
                    </div>
                  </div>
                </div>
                
                <div class="vehicle-details">
                  <div class="detail-row">
                    <span class="detail-label">购买年份</span>
                    <span class="detail-value">{{ vehicle.year }}年</span>
                  </div>
                  <div class="detail-row">
                    <span class="detail-label">行驶里程</span>
                    <span class="detail-value">{{ vehicle.mileage ? vehicle.mileage.toLocaleString() : '0' }}公里</span>
                  </div>
                </div>
                
                <div class="vehicle-actions">
                  <a-button size="small" type="text" @click="editVehicle(vehicle)">
                    <EditOutlined />
                    编辑
                  </a-button>
                  <a-button size="small" type="text" @click="viewMaintenance(vehicle.id)">
                    <FileTextOutlined />
                    维保记录
                  </a-button>
                  <a-popconfirm title="确定删除这辆车吗？" @confirm="deleteVehicle(vehicle.id)">
                    <a-button size="small" type="text" danger>
                      <DeleteOutlined />
                      删除
                    </a-button>
                  </a-popconfirm>
                </div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>

      <!-- 最近订单 -->
      <a-card title="最近订单" class="recent-orders-card">
      <template #extra>
        <a-button type="link" @click="viewAllOrders">查看全部</a-button>
      </template>

      <a-table 
        :dataSource="recentOrders" 
        :columns="orderColumns" 
        :pagination="false"
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'status'">
            <a-tag :color="getOrderStatusColor(record.status)">
              {{ getOrderStatusText(record.status) }}
            </a-tag>
          </template>
          <template v-else-if="column.key === 'action'">
            <a-space>
              <a-button size="small" @click="viewOrderDetail(record.id)">查看</a-button>
              <a-button 
                v-if="record.status === 4" 
                size="small" 
                @click="rateOrder(record.id)"
              >
                评价
              </a-button>
            </a-space>
          </template>
        </template>
      </a-table>
      </a-card>
    </div>

    <!-- 编辑资料模态框 -->
    <a-modal
      v-model:visible="profileModalVisible"
      title="编辑个人资料"
      @ok="saveProfile"
      @cancel="cancelEdit"
    >
      <a-form
        ref="profileFormRef"
        :model="profileForm"
        :rules="profileRules"
        layout="vertical"
      >
        <a-form-item label="用户名" name="username">
          <a-input v-model:value="profileForm.username" placeholder="请输入用户名" />
        </a-form-item>
        <a-form-item label="手机号码" name="phone">
          <a-input v-model:value="profileForm.phone" placeholder="请输入手机号码" />
        </a-form-item>
        <a-form-item label="邮箱地址" name="email">
          <a-input v-model:value="profileForm.email" placeholder="请输入邮箱地址" />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- 添加/编辑车辆模态框 -->
    <a-modal
      v-model:visible="vehicleModalVisible"
      :title="editingVehicle ? '编辑车辆' : '添加车辆'"
      @ok="saveVehicle"
      @cancel="cancelVehicleEdit"
    >
      <a-form
        ref="vehicleFormRef"
        :model="vehicleForm"
        :rules="vehicleRules"
        layout="vertical"
      >
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="车辆品牌" name="brand">
              <a-select v-model:value="vehicleForm.brand" placeholder="请选择品牌">
                <a-select-option value="本田">本田</a-select-option>
                <a-select-option value="丰田">丰田</a-select-option>
                <a-select-option value="大众">大众</a-select-option>
                <a-select-option value="奥迪">奥迪</a-select-option>
                <a-select-option value="宝马">宝马</a-select-option>
                <a-select-option value="奔驰">奔驰</a-select-option>
                <a-select-option value="其他">其他</a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="车型" name="model">
              <a-input v-model:value="vehicleForm.model" placeholder="请输入车型" />
            </a-form-item>
          </a-col>
        </a-row>

        <a-form-item label="车牌号" name="licensePlate">
          <a-input v-model:value="vehicleForm.licensePlate" placeholder="请输入车牌号" />
        </a-form-item>

        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="购车年份" name="year">
              <a-date-picker 
                v-model:value="vehicleForm.year" 
                picker="year"
                placeholder="请选择年份"
                style="width: 100%"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="当前里程" name="mileage">
              <a-input-number 
                v-model:value="vehicleForm.mileage" 
                placeholder="请输入里程"
                style="width: 100%"
                :min="0"
                :max="999999"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { 
  EditOutlined, 
  PlusOutlined, 
  PhoneOutlined, 
  MailOutlined, 
  CalendarOutlined,
  CameraOutlined,
  CrownOutlined,
  DollarOutlined,
  ToolOutlined,
  CarOutlined,
  FileTextOutlined,
  DeleteOutlined
} from '@ant-design/icons-vue';
import axios from 'axios';

const router = useRouter();

// 响应式数据
const userInfo = ref({});
const vehicles = ref([]);
const recentOrders = ref([]);
const profileModalVisible = ref(false);
const vehicleModalVisible = ref(false);
const editingVehicle = ref(null);

// 用户统计数据
const userStats = ref({
  totalSpent: 5680,
  serviceCount: 12,
  memberLevel: 'VIP'
});

// 个人资料表单
const profileForm = reactive({
  username: '',
  phone: '',
  email: ''
});

const profileRules = {
  username: [{ required: true, message: '请输入用户名' }],
  phone: [
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码' }
  ],
  email: [
    { type: 'email', message: '请输入正确的邮箱地址' }
  ]
};

// 车辆表单
const vehicleForm = reactive({
  brand: '',
  model: '',
  licensePlate: '',
  year: null,
  mileage: 0
});

const vehicleRules = {
  brand: [{ required: true, message: '请选择车辆品牌' }],
  model: [{ required: true, message: '请输入车型' }],
  licensePlate: [{ required: true, message: '请输入车牌号' }],
  year: [{ required: true, message: '请选择购车年份' }]
};

// 订单表格列定义
const orderColumns = [
  {
    title: '订单编号',
    dataIndex: 'orderNumber',
    key: 'orderNumber',
    width: 150
  },
  {
    title: '服务项目',
    dataIndex: 'serviceName',
    key: 'serviceName'
  },
  {
    title: '服务时间',
    dataIndex: 'serviceTime',
    key: 'serviceTime'
  },
  {
    title: '订单金额',
    dataIndex: 'amount',
    key: 'amount',
    customRender: ({ text }: { text: number }) => `¥${text}`
  },
  {
    title: '状态',
    key: 'status'
  },
  {
    title: '操作',
    key: 'action',
    width: 120
  }
];

// 加载用户信息
const loadUserInfo = () => {
  const userInfoStr = localStorage.getItem('userInfo');
  if (userInfoStr) {
    try {
      userInfo.value = JSON.parse(userInfoStr);
    } catch (error) {
      console.error('解析用户信息失败:', error);
    }
  }
};

// 加载车辆信息
const loadVehicles = async () => {
  try {
    // 模拟数据
    vehicles.value = [
      {
        id: 1,
        brand: '本田',
        model: '雅阁',
        licensePlate: '京A12345',
        year: 2020,
        mileage: 35000,
        image: '/image/default-service.png'
      },
      {
        id: 2,
        brand: '丰田',
        model: '卡罗拉',
        licensePlate: '京B67890',
        year: 2019,
        mileage: 42000,
        image: '/image/default-service.png'
      }
    ];
  } catch (error) {
    message.error('加载车辆信息失败');
  }
};

// 加载最近订单
const loadRecentOrders = async () => {
  try {
    // 模拟数据
    recentOrders.value = [
      {
        id: 1,
        orderNumber: 'ORD202501040001',
        serviceName: '机油更换',
        serviceTime: '2025-01-03 10:00',
        amount: 350,
        status: 4
      },
      {
        id: 2,
        orderNumber: 'ORD202501030001',
        serviceName: '轮胎更换',
        serviceTime: '2025-01-02 14:30',
        amount: 1200,
        status: 3
      }
    ];
  } catch (error) {
    message.error('加载订单信息失败');
  }
};

// 编辑个人资料
const editProfile = () => {
  Object.assign(profileForm, userInfo.value);
  profileModalVisible.value = true;
};

// 保存个人资料
const saveProfile = async () => {
  try {
    // 这里应该调用后端API
    message.success('资料更新成功');
    profileModalVisible.value = false;
    Object.assign(userInfo.value, profileForm);
    localStorage.setItem('userInfo', JSON.stringify(userInfo.value));
  } catch (error) {
    message.error('更新失败');
  }
};

// 取消编辑
const cancelEdit = () => {
  profileModalVisible.value = false;
};

// 添加车辆
const addVehicle = () => {
  editingVehicle.value = null;
  Object.assign(vehicleForm, {
    brand: '',
    model: '',
    licensePlate: '',
    year: null,
    mileage: 0
  });
  vehicleModalVisible.value = true;
};

// 编辑车辆
const editVehicle = (vehicle: any) => {
  editingVehicle.value = vehicle;
  Object.assign(vehicleForm, vehicle);
  vehicleModalVisible.value = true;
};

// 保存车辆
const saveVehicle = async () => {
  try {
    // 这里应该调用后端API
    message.success(editingVehicle.value ? '车辆信息更新成功' : '车辆添加成功');
    vehicleModalVisible.value = false;
    loadVehicles();
  } catch (error) {
    message.error('操作失败');
  }
};

// 取消车辆编辑
const cancelVehicleEdit = () => {
  vehicleModalVisible.value = false;
};

// 删除车辆
const deleteVehicle = async (vehicleId: number) => {
  try {
    // 这里应该调用后端API
    message.success('车辆删除成功');
    loadVehicles();
  } catch (error) {
    message.error('删除失败');
  }
};

// 查看维保记录
const viewMaintenance = (vehicleId: number) => {
  router.push(`/owner/maintenance/${vehicleId}`);
};

// 查看全部订单
const viewAllOrders = () => {
  router.push('/owner/orders');
};

// 查看订单详情
const viewOrderDetail = (orderId: number) => {
  router.push(`/owner/orders/${orderId}`);
};

// 评价订单
const rateOrder = (orderId: number) => {
  router.push(`/owner/rating/${orderId}`);
};

// 获取订单状态颜色
const getOrderStatusColor = (status: number): string => {
  const colors: Record<number, string> = {
    1: 'orange',     // 待处理
    2: 'blue',       // 已确认
    3: 'processing', // 进行中
    4: 'success',    // 已完成
    5: 'error'       // 已取消
  };
  return colors[status] || 'default';
};

// 获取订单状态文本
const getOrderStatusText = (status: number): string => {
  const texts: Record<number, string> = {
    1: '待处理',
    2: '已确认',
    3: '进行中',
    4: '已完成',
    5: '已取消'
  };
  return texts[status] || '未知';
};

// 格式化日期
const formatDate = (dateStr: string): string => {
  if (!dateStr) return '--';
  return new Date(dateStr).toLocaleDateString();
};

// 组件挂载时加载数据
onMounted(() => {
  loadUserInfo();
  loadVehicles();
  loadRecentOrders();
});
</script>

<style scoped>
.personal-center {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0;
  position: relative;
}

.personal-center::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.03)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

/* 页面头部 */
.page-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 0 0 24px 24px;
  padding: 32px;
  margin-bottom: 32px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 1;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section h1 {
  margin: 0 0 8px 0;
  color: #1a1a1a;
  font-size: 32px;
  font-weight: 700;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.title-section p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.header-actions .ant-btn {
  height: 44px;
  border-radius: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 主要内容区域 */
.main-content {
  padding: 0 32px 32px;
  position: relative;
  z-index: 1;
}

/* 个人信息卡片 */
.profile-card {
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.profile-header {
  text-align: center;
  padding: 32px 24px 24px;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
}

.avatar-section {
  margin-bottom: 24px;
}

.user-avatar {
  border: 4px solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  background: linear-gradient(135deg, #667eea, #764ba2);
  font-size: 36px;
  font-weight: 600;
  color: white;
}

.change-avatar {
  margin-top: 8px;
  color: #666;
  font-size: 12px;
}

.profile-info h2 {
  margin: 0 0 12px 0;
  font-size: 24px;
  font-weight: 700;
  color: #1a1a1a;
}

.user-badge {
  margin-bottom: 8px;
}

.vip-tag {
  font-size: 14px;
  font-weight: 600;
  padding: 4px 12px;
  border-radius: 20px;
  border: none;
}

.user-motto {
  margin: 0;
  color: #666;
  font-style: italic;
  font-size: 14px;
}

/* 统计区域 */
.stats-section {
  padding: 24px;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: rgba(248, 250, 252, 0.8);
  border-radius: 16px;
  transition: all 0.3s;
}

.stat-card:hover {
  background: rgba(248, 250, 252, 1);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
  flex-shrink: 0;
}

.stat-icon.spent {
  background: linear-gradient(135deg, #f093fb, #f5576c);
}

.stat-icon.service {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 18px;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 4px;
}

.stat-label {
  color: #666;
  font-size: 12px;
}

/* 联系信息区域 */
.contact-section {
  padding: 0 24px 24px;
}

.contact-section h3 {
  margin: 0 0 20px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
}

.contact-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: rgba(248, 250, 252, 0.6);
  border-radius: 12px;
  transition: all 0.3s;
}

.contact-item:hover {
  background: rgba(248, 250, 252, 1);
}

.contact-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  font-size: 16px;
}

.contact-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.contact-label {
  font-size: 12px;
  color: #666;
}

.contact-value {
  font-size: 14px;
  color: #1a1a1a;
  font-weight: 500;
}

/* 车辆卡片 */
.vehicle-card {
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

/* 空状态 */
.empty-vehicles {
  text-align: center;
  padding: 60px 24px;
}

.empty-illustration {
  width: 80px;
  height: 80px;
  margin: 0 auto 24px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  color: #667eea;
}

.empty-vehicles h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #1a1a1a;
}

.empty-vehicles p {
  margin: 0 0 24px 0;
  color: #666;
  font-size: 14px;
}

/* 车辆网格 */
.vehicles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  padding: 24px;
}

.vehicle-card-item {
  background: rgba(248, 250, 252, 0.8);
  border-radius: 16px;
  padding: 24px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.vehicle-card-item:hover {
  background: rgba(248, 250, 252, 1);
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
}

.vehicle-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 20px;
}

.vehicle-image {
  width: 80px;
  height: 60px;
  border-radius: 12px;
  overflow: hidden;
  flex-shrink: 0;
}

.vehicle-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.vehicle-basic-info h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
}

.license-plate {
  display: inline-block;
}

.plate-number {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  letter-spacing: 1px;
}

.vehicle-details {
  margin-bottom: 20px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  font-size: 14px;
  color: #666;
}

.detail-value {
  font-size: 14px;
  color: #1a1a1a;
  font-weight: 500;
}

.vehicle-actions {
  display: flex;
  gap: 8px;
}

.vehicle-actions .ant-btn {
  border-radius: 8px;
  font-weight: 500;
}

/* 最近订单卡片 */
.recent-orders-card {
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin-top: 32px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .vehicles-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .page-header {
    padding: 24px 20px;
    border-radius: 0;
  }
  
  .main-content {
    padding: 0 20px 24px;
  }
  
  .header-content {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .title-section h1 {
    font-size: 24px;
  }
  
  .profile-header {
    padding: 24px 20px 20px;
  }
  
  .contact-list {
    gap: 12px;
  }
  
  .contact-item {
    padding: 12px;
  }
  
  .vehicles-grid {
    padding: 20px;
    gap: 16px;
  }
  
  .vehicle-card-item {
    padding: 20px;
  }
}

@media (max-width: 480px) {
  .page-header {
    padding: 20px 16px;
  }
  
  .main-content {
    padding: 0 16px 20px;
  }
  
  .title-section h1 {
    font-size: 20px;
  }
  
  .profile-header {
    padding: 20px 16px 16px;
  }
  
  .user-avatar {
    width: 80px !important;
    height: 80px !important;
    font-size: 28px;
  }
  
  .stats-section {
    padding: 20px 16px;
  }
  
  .contact-section {
    padding: 0 16px 20px;
  }
  
  .vehicles-grid {
    padding: 16px;
    gap: 16px;
  }
  
  .vehicle-card-item {
    padding: 16px;
  }
  
  .vehicle-header {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
  
  .vehicle-actions {
    justify-content: center;
  }
}
</style>
