package com.gec.wiki.controller;

import com.gec.wiki.pojo.req.BookingCreateReq;
import com.gec.wiki.pojo.resp.CommonResp;
import com.gec.wiki.service.BookingService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDate;

/**
 * 预约控制器
 */
@RestController
@RequestMapping("/booking")
public class BookingController {

    @Resource
    private BookingService bookingService;

    /**
     * 创建预约
     */
    @PostMapping("/create")
    public CommonResp<Object> createBooking(@RequestBody BookingCreateReq req) {
        // TODO: 从token或session中获取用户ID，这里临时使用固定值
        Long userId = 1L;
        return bookingService.createBooking(req, userId);
    }

    /**
     * 获取用户预约列表
     */
    @GetMapping("/list")
    public CommonResp<Object> getBookingList(@RequestParam(defaultValue = "1") Integer page,
                                            @RequestParam(defaultValue = "10") Integer size) {
        // TODO: 从token或session中获取用户ID，这里临时使用固定值
        Long userId = 1L;
        return bookingService.getBookingList(userId, page, size);
    }

    /**
     * 获取预约详情
     */
    @GetMapping("/{id}")
    public CommonResp<Object> getBookingDetail(@PathVariable Long id) {
        // TODO: 从token或session中获取用户ID，这里临时使用固定值
        Long userId = 1L;
        return bookingService.getBookingDetail(id, userId);
    }

    /**
     * 取消预约
     */
    @PostMapping("/{id}/cancel")
    public CommonResp<Object> cancelBooking(@PathVariable Long id) {
        // TODO: 从token或session中获取用户ID，这里临时使用固定值
        Long userId = 1L;
        return bookingService.cancelBooking(id, userId);
    }

    /**
     * 获取可用时间段
     */
    @GetMapping("/available-times")
    public CommonResp<Object> getAvailableTimes(@RequestParam String date,
                                               @RequestParam Long serviceId) {
        LocalDate bookingDate = LocalDate.parse(date);
        return bookingService.getAvailableTimes(bookingDate, serviceId);
    }
}
