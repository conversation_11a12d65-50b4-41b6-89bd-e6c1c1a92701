<template>
  <a-layout class="about-container">
    <a-page-header
      title="关于汽车维修服务平台"
      sub-title="专业、透明、便捷的汽车维修服务"
      @back="() => $router.go(-1)"
    >
      <template #extra>
        <a-button type="primary" @click="showModal">联系我们</a-button>
      </template>
    </a-page-header>

    <a-layout-content class="content">
      <!-- 平台介绍 -->
      <div class="section">
        <h2 class="section-title">汽车维修服务平台</h2>
        <div class="intro-container">
          <div class="intro-icon">
            <global-outlined />
          </div>
          <div class="intro-content">
            <p>我们是一个专业的汽车维修服务平台，致力于为车主提供透明、便捷、高品质的汽车维修服务。通过整合优质维修资源，建立标准化服务体系，让汽车维修变得更加简单可靠。</p>
            
            <div class="stat-row">
              <div class="stat-item">
                <div class="stat-number">500+</div>
                <div class="stat-label">合作维修店</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">50,000+</div>
                <div class="stat-label">服务次数</div>
              </div>
              <div class="stat-item">
                <div class="stat-number">20,000+</div>
                <div class="stat-label">注册用户</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 核心功能 -->
      <div class="section">
        <h2 class="section-title"><environment-outlined /> 核心功能</h2>
        <div class="feature-grid">
          <div class="feature-item" v-for="(feature, index) in features" :key="index">
            <div class="feature-icon" :class="`feature-bg-${index+1}`">
              <component :is="feature.icon" />
            </div>
            <div class="feature-content">
              <h3>{{ feature.title }}</h3>
              <p>{{ feature.description }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 平台优势 -->
      <div class="section">
        <h2 class="section-title"><global-outlined /> 平台优势</h2>
        <div class="facts-container">
          <div class="facts-column">
            <h3><experiment-outlined /> 价格透明优势</h3>
            <ul>
              <li>统一标准工费：每分钟仅需1元，价格公开透明</li>
              <li>配件价格统一：零件价格由平台根据市场统一定价</li>
              <li>费用锁定保障：维修店无法私自修改维修费用</li>
              <li>明细清单展示：所有费用明细一目了然，杜绝隐性收费</li>
              <li>价格对比功能：多家维修店价格透明对比，选择最优方案</li>
            </ul>
          </div>
          <div class="facts-column">
            <h3><alert-outlined /> 服务品质保障</h3>
            <ul>
              <li>系统功能齐全：集预约、导航、支付、评价于一体</li>
              <li>AI智能诊断：提供专业的汽车故障诊断参考</li>
              <li>全程服务跟踪：从预约到完工全程状态实时更新</li>
              <li>完善售后保障：提供维修质保和投诉处理机制</li>
              <li>在线客服支持：7×24小时客服团队随时为您解答</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- 联系我们 -->
      <div class="section">
        <h2 class="section-title"><team-outlined /> 关于我们</h2>
        <div class="about-content">
          <p>汽车维修服务平台是由一群汽车行业专家、技术开发者和服务运营团队共同打造的专业汽车维修服务平台。我们致力于通过先进的互联网技术和标准化的服务体系，为广大车主提供更加透明、便捷、可靠的汽车维修服务。</p>
          <p>我们拥有AI智能诊断系统、在线客服团队和专业的平台答疑服务，让您在使用过程中遇到任何问题都能得到及时专业的解答。如果您对我们的服务有任何建议或需要技术支持，请通过下方联系方式与我们取得联系。</p>
          
          <div class="contact-box">
            <div class="contact-item">
              <mail-outlined />
              <span>邮箱：<EMAIL></span>
            </div>
            <div class="contact-item">
              <phone-outlined />
              <span>客服热线：************</span>
            </div>
            <div class="contact-item">
              <environment-outlined />
              <span>地址：北京市朝阳区汽车科技园A座12层</span>
            </div>
          </div>
        </div>
      </div>
    </a-layout-content>

    <!-- 联系模态框 -->
    <a-modal v-model:visible="visible" title="联系我们" @ok="handleOk">
      <a-form layout="vertical">
        <a-form-item label="您的姓名">
          <a-input v-model:value="form.name" placeholder="请输入您的姓名" />
        </a-form-item>
        <a-form-item label="联系方式">
          <a-input v-model:value="form.contact" placeholder="请输入邮箱或电话号码" />
        </a-form-item>
        <a-form-item label="留言类型">
          <a-select v-model:value="form.type" placeholder="请选择留言类型">
            <a-select-option value="suggestion">功能建议</a-select-option>
            <a-select-option value="service">服务咨询</a-select-option>
            <a-select-option value="complaint">投诉建议</a-select-option>
            <a-select-option value="cooperation">商家合作</a-select-option>
            <a-select-option value="other">其他</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="留言内容">
          <a-textarea v-model:value="form.message" placeholder="请输入您的留言" :rows="4" />
        </a-form-item>
      </a-form>
    </a-modal>
  </a-layout>
</template>

<script>
import { 
  MailOutlined, 
  PhoneOutlined, 
  TeamOutlined, 
  GlobalOutlined,
  ExperimentOutlined,
  AlertOutlined,
  EnvironmentOutlined,
  DatabaseOutlined,
  AppstoreOutlined,
  SearchOutlined,
  FileProtectOutlined,
  CloudOutlined,
  MobileOutlined
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';

export default {
  components: {
    MailOutlined,
    PhoneOutlined,
    TeamOutlined,
    GlobalOutlined,
    ExperimentOutlined,
    AlertOutlined,
    EnvironmentOutlined,
    DatabaseOutlined,
    AppstoreOutlined,
    SearchOutlined,
    FileProtectOutlined,
    CloudOutlined,
    MobileOutlined
  },
  data() {
    return {
      visible: false,
      form: {
        name: '',
        contact: '',
        type: 'suggestion',
        message: ''
      },
      features: [
        {
          title: '智能选店',
          description: '根据您的位置智能推荐附近优质维修店，支持距离、评分、价格等多维度筛选。',
          icon: 'SearchOutlined'
        },
        {
          title: '路线导航',
          description: '一键规划前往维修店的最优路线，支持实时路况避堵，让您快速到达目的地。',
          icon: 'EnvironmentOutlined'
        },
        {
          title: '在线预约',
          description: '自主选择维修时间和服务项目，支持上门取送车服务，让维修更加便捷。',
          icon: 'AppstoreOutlined'
        },
        {
          title: 'AI智能诊断',
          description: '先进的AI技术帮您快速诊断车辆故障，提供专业的维修建议和解决方案。',
          icon: 'FileProtectOutlined'
        },
        {
          title: '服务记录',
          description: '完整记录每次维修服务详情，包括工时、配件、费用等，便于查看和管理。',
          icon: 'DatabaseOutlined'
        },
        {
          title: '评价互动',
          description: '对维修服务进行评价和投诉，与商家、用户、客服在线沟通交流。',
          icon: 'MobileOutlined'
        }
      ]
    }
  },
  methods: {
    showModal() {
      this.visible = true;
    },
    handleOk() {
      if (!this.form.name || !this.form.contact || !this.form.message) {
        message.warning('请完整填写表单信息');
        return;
      }
      message.success('提交成功，我们的客服团队会尽快与您联系！');
      this.visible = false;
      this.form = { 
        name: '', 
        contact: '', 
        type: 'suggestion',
        message: '' 
      };
    }
  }
}
</script>

<style scoped>
.about-container {
  min-height: 100vh;
}

.content {
  padding: 24px;
  background: #f9f9f9;
}

.section {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.section-title {
  font-size: 22px;
  color: #0072b5;
  margin-bottom: 20px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
  display: flex;
  align-items: center;
}

.section-title .anticon {
  margin-right: 8px;
  font-size: 20px;
}

/* 介绍部分 */
.intro-container {
  display: flex;
  align-items: center;
}

.intro-icon {
  width: 120px;
  height: 120px;
  background: linear-gradient(135deg, #1890ff 0%, #36cfc9 100%);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 60px;
  color: white;
  margin-right: 30px;
  flex-shrink: 0;
}

.intro-content {
  flex-grow: 1;
}

.intro-content p {
  font-size: 16px;
  line-height: 1.8;
  margin-bottom: 20px;
  color: #333;
}

/* 统计数据 */
.stat-row {
  display: flex;
  justify-content: space-around;
  text-align: center;
  margin-top: 24px;
}

.stat-item {
  flex: 1;
  padding: 16px 8px;
  background-color: #f5f5f5;
  border-radius: 6px;
  margin: 0 8px;
}

.stat-number {
  font-size: 24px;
  color: #1890ff;
  font-weight: bold;
}

.stat-label {
  margin-top: 8px;
  color: #666;
}

/* 特色功能 */
.feature-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

@media (max-width: 992px) {
  .feature-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 576px) {
  .feature-grid {
    grid-template-columns: 1fr;
  }
}

.feature-item {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  transition: transform 0.3s;
}

.feature-item:hover {
  transform: translateY(-5px);
}

.feature-icon {
  height: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #fff;
  font-size: 40px;
}

.feature-bg-1 { background: linear-gradient(135deg, #1890ff 0%, #36cfc9 100%); }
.feature-bg-2 { background: linear-gradient(135deg, #13c2c2 0%, #87e8de 100%); }
.feature-bg-3 { background: linear-gradient(135deg, #722ed1 0%, #b37feb 100%); }
.feature-bg-4 { background: linear-gradient(135deg, #eb2f96 0%, #f759ab 100%); }
.feature-bg-5 { background: linear-gradient(135deg, #52c41a 0%, #b7eb8f 100%); }
.feature-bg-6 { background: linear-gradient(135deg, #fa8c16 0%, #ffd666 100%); }

.feature-content {
  padding: 16px;
}

.feature-content h3 {
  font-size: 18px;
  margin-bottom: 8px;
}

.feature-content p {
  color: #666;
  margin-bottom: 0;
  height: 60px;
  overflow: hidden;
}

/* 海洋知识 */
.facts-container {
  display: flex;
}

@media (max-width: 768px) {
  .facts-container {
    flex-direction: column;
  }
}

.facts-column {
  flex: 1;
  padding: 0 16px;
}

.facts-column h3 {
  display: flex;
  align-items: center;
  color: #0072b5;
  margin-bottom: 16px;
}

.facts-column h3 .anticon {
  margin-right: 8px;
}

.facts-column ul {
  padding-left: 20px;
}

.facts-column li {
  margin-bottom: 10px;
  color: #555;
  line-height: 1.6;
}

/* 关于我们 */
.about-content p {
  font-size: 16px;
  line-height: 1.8;
  color: #333;
  margin-bottom: 16px;
}

.contact-box {
  background-color: #f5f5f5;
  padding: 16px;
  border-radius: 8px;
  margin-top: 20px;
}

.contact-item {
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}

.contact-item:last-child {
  margin-bottom: 0;
}

.contact-item .anticon {
  font-size: 18px;
  color: #1890ff;
  margin-right: 12px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .intro-container {
    flex-direction: column;
    text-align: center;
  }
  
  .intro-icon {
    margin-right: 0;
    margin-bottom: 20px;
  }
  
  .stat-row {
    flex-direction: column;
  }
  
  .stat-item {
    margin: 8px 0;
  }
}
</style>