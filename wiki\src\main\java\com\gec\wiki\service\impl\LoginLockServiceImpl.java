package com.gec.wiki.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.gec.wiki.mapper.SystemSettingsMapper;
import com.gec.wiki.mapper.UserLoginLockMapper;
import com.gec.wiki.pojo.SystemSettings;
import com.gec.wiki.pojo.UserLoginLock;
import com.gec.wiki.service.LoginLockService;
import com.gec.wiki.service.SecuritySettingsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * 登录锁定服务实现
 */
@Service
public class LoginLockServiceImpl implements LoginLockService {
    
    private static final Logger LOG = LoggerFactory.getLogger(LoginLockServiceImpl.class);

    // 系统设置键（保留兼容性）
    private static final String MAX_FAIL_COUNT_KEY = "LOGIN_MAX_FAIL_COUNT";

    @Autowired
    private UserLoginLockMapper userLoginLockMapper;

    @Autowired
    private SystemSettingsMapper systemSettingsMapper;

    @Resource
    private SecuritySettingsService securitySettingsService;
    
    @Override
    public UserLoginLock checkUserLocked(String username, String ipAddress) {
        try {
            LOG.info("🔒 检查用户锁定状态：username={}", username);

            // 只检查用户名的记录，不考虑IP地址，确保不同用户独立锁定
            UserLoginLock lockRecord = userLoginLockMapper.findByUsername(username);

            if (lockRecord == null) {
                LOG.info("✅ 用户未被锁定，无锁定记录：username={}", username);
                return null;
            }

            // 检查锁定是否已过期
            if (lockRecord.getLockedUntil() != null && lockRecord.getLockedUntil().isAfter(LocalDateTime.now())) {
                LOG.info("🚫 用户被锁定中：username={}, 锁定到={}", username, lockRecord.getLockedUntil());
                return lockRecord;
            } else if (lockRecord.getLockedUntil() != null) {
                // 锁定已过期，只清除锁定时间，保留失败次数记录
                LOG.info("⏰ 用户锁定已过期，清除锁定时间但保留失败记录：username={}", username);
                clearLockTime(username);
                // 重新获取更新后的记录
                lockRecord = userLoginLockMapper.findByUsername(username);
                return lockRecord; // 返回记录以便显示当前失败次数
            }

            LOG.info("✅ 用户未被锁定：username={}", username);
            return lockRecord; // 返回记录以便显示当前失败次数

        } catch (Exception e) {
            LOG.error("❌ 检查用户锁定状态异常：username={}, error=", username, e);
            // 异常情况下，为了安全起见，不锁定用户
            return null;
        }
    }
    
    @Override
    public int recordLoginFailure(String username, String ipAddress, Long userId) {
        try {
            LOG.info("📝 记录登录失败：username={}, userId={}", username, userId);
            
            // 只基于用户名查找记录，不考虑IP地址
            UserLoginLock lockRecord = userLoginLockMapper.findByUsername(username);
            
            if (lockRecord == null) {
                // 创建新的锁定记录
                lockRecord = new UserLoginLock();
                lockRecord.setUsername(username);
                lockRecord.setIpAddress(ipAddress); // 记录当前IP但不用于查询
                lockRecord.setUserId(userId);
                lockRecord.setFailCount(1);
                lockRecord.setLastFailTime(LocalDateTime.now());
                lockRecord.setCreateTime(LocalDateTime.now());
                lockRecord.setUpdateTime(LocalDateTime.now());
                
                userLoginLockMapper.insert(lockRecord);
                LOG.info("➕ 创建新的登录失败记录：username={}, failCount=1", username);
            } else {
                // 更新现有记录
                lockRecord.setFailCount(lockRecord.getFailCount() + 1);
                lockRecord.setLastFailTime(LocalDateTime.now());
                lockRecord.setUpdateTime(LocalDateTime.now());
                lockRecord.setIpAddress(ipAddress); // 更新最新的IP地址
                
                // 检查是否达到锁定阈值
                int maxFailCount = getMaxFailCount();
                if (lockRecord.getFailCount() >= maxFailCount) {
                    int lockDurationMinutes = securitySettingsService.getLockDurationMinutes();
                    lockRecord.setLockedUntil(LocalDateTime.now().plusMinutes(lockDurationMinutes));
                    LOG.warn("🔒 用户达到最大失败次数，锁定{}分钟：username={}, failCount={}, lockedUntil={}",
                             lockDurationMinutes, username, lockRecord.getFailCount(), lockRecord.getLockedUntil());
                } else {
                    LOG.info("⚠️ 更新登录失败记录：username={}, failCount={}/{}", 
                             username, lockRecord.getFailCount(), maxFailCount);
                }
                
                userLoginLockMapper.updateById(lockRecord);
            }
            
            return lockRecord.getFailCount();
            
        } catch (Exception e) {
            LOG.error("❌ 记录登录失败异常：username={}, error=", username, e);
            return 0;
        }
    }
    
    /**
     * 只清除锁定时间，保留失败次数记录
     */
    private void clearLockTime(String username) {
        try {
            LOG.info("🧹 清除锁定时间：username={}", username);

            UpdateWrapper<UserLoginLock> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("username", username);

            UserLoginLock updateRecord = new UserLoginLock();
            updateRecord.setLockedUntil(null);
            updateRecord.setUpdateTime(LocalDateTime.now());

            int updated = userLoginLockMapper.update(updateRecord, updateWrapper);
            if (updated > 0) {
                LOG.info("✅ 成功清除锁定时间：username={}, 更新记录数={}", username, updated);
            } else {
                LOG.info("ℹ️ 未找到需要清除锁定时间的记录：username={}", username);
            }

        } catch (Exception e) {
            LOG.error("❌ 清除锁定时间异常：username={}, error=", username, e);
        }
    }

    @Override
    public void clearLoginFailure(String username, String ipAddress) {
        try {
            LOG.info("🧹 清除登录失败记录：username={}", username);

            // 只基于用户名清除记录，不考虑IP地址
            UpdateWrapper<UserLoginLock> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("username", username);

            UserLoginLock updateRecord = new UserLoginLock();
            updateRecord.setFailCount(0);
            updateRecord.setLockedUntil(null);
            updateRecord.setUpdateTime(LocalDateTime.now());

            int updated = userLoginLockMapper.update(updateRecord, updateWrapper);
            if (updated > 0) {
                LOG.info("✅ 成功清除登录失败记录：username={}, 清除记录数={}", username, updated);
            } else {
                LOG.info("ℹ️ 未找到需要清除的登录失败记录：username={}", username);
            }

        } catch (Exception e) {
            LOG.error("❌ 清除登录失败记录异常：username={}, error=", username, e);
        }
    }
    
    @Override
    public int getMaxFailCount() {
        try {
            // 优先使用新的安全设置服务
            int maxFailCount = securitySettingsService.getMaxLoginFailCount();
            LOG.debug("🔢 从安全设置获取最大失败次数：{}", maxFailCount);
            return maxFailCount;
        } catch (Exception e) {
            LOG.error("❌ 获取最大失败次数配置异常，尝试从系统设置获取", e);

            // 降级到旧的系统设置
            try {
                String value = systemSettingsMapper.getSettingValue(MAX_FAIL_COUNT_KEY);
                if (value != null) {
                    int maxFailCount = Integer.parseInt(value);
                    LOG.debug("🔢 从系统设置获取最大失败次数：{}", maxFailCount);
                    return maxFailCount;
                }
            } catch (Exception ex) {
                LOG.error("❌ 从系统设置获取最大失败次数也失败", ex);
            }
        }

        // 默认值：5次
        LOG.info("📊 使用默认最大失败次数：5");
        return 5;
    }
    
    @Override
    public boolean setMaxFailCount(int maxFailCount) {
        try {
            LOG.info("⚙️ 设置最大失败次数：{}", maxFailCount);
            
            if (maxFailCount < 1 || maxFailCount > 100) {
                LOG.warn("⚠️ 无效的最大失败次数值：{}", maxFailCount);
                return false;
            }
            
            QueryWrapper<SystemSettings> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("setting_key", MAX_FAIL_COUNT_KEY);
            SystemSettings existing = systemSettingsMapper.selectOne(queryWrapper);
            
            if (existing != null) {
                // 更新现有配置
                existing.setSettingValue(String.valueOf(maxFailCount));
                existing.setUpdateTime(LocalDateTime.now());
                int updated = systemSettingsMapper.updateById(existing);
                
                if (updated > 0) {
                    LOG.info("✅ 成功更新最大失败次数配置：{}", maxFailCount);
                    return true;
                }
            } else {
                // 创建新配置
                SystemSettings newSetting = new SystemSettings();
                newSetting.setSettingKey(MAX_FAIL_COUNT_KEY);
                newSetting.setSettingValue(String.valueOf(maxFailCount));
                newSetting.setSettingDescription("登录最大错误次数，超过后锁定30分钟");
                newSetting.setCreateTime(LocalDateTime.now());
                newSetting.setUpdateTime(LocalDateTime.now());
                
                int inserted = systemSettingsMapper.insert(newSetting);
                if (inserted > 0) {
                    LOG.info("✅ 成功创建最大失败次数配置：{}", maxFailCount);
                    return true;
                }
            }
            
        } catch (Exception e) {
            LOG.error("❌ 设置最大失败次数异常：maxFailCount={}, error=", maxFailCount, e);
        }
        
        return false;
    }
    
    @Override
    public boolean unlockUser(String username) {
        try {
            LOG.info("🔓 手动解锁用户：{}", username);
            
            UpdateWrapper<UserLoginLock> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("username", username);
            
            UserLoginLock updateRecord = new UserLoginLock();
            updateRecord.setFailCount(0);
            updateRecord.setLockedUntil(null);
            updateRecord.setUpdateTime(LocalDateTime.now());
            
            int updated = userLoginLockMapper.update(updateRecord, updateWrapper);
            if (updated > 0) {
                LOG.info("✅ 成功手动解锁用户：username={}, 解锁记录数={}", username, updated);
                return true;
            } else {
                LOG.info("ℹ️ 未找到需要解锁的用户记录：{}", username);
                return false;
            }
            
        } catch (Exception e) {
            LOG.error("❌ 手动解锁用户异常：username={}, error=", username, e);
            return false;
        }
    }
}
